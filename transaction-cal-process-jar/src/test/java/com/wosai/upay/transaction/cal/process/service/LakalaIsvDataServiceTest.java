package com.wosai.upay.transaction.cal.process.service;

import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.IOException;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
 */
@Slf4j
@RunWith(SpringRunner.class)
@SpringBootTest
public class LakalaIsvDataServiceTest {

    @Autowired
    private LakalaIsvDataService lakalaIsvDataService;

    @Test
    public void test(){
       lakalaIsvDataService.send("20220305");

        String sqlByName = lakalaIsvDataService.getSqlByName("odps/lkl-agency.sql");
        System.out.println(sqlByName);

        Set set = new HashSet();

        set.stream().collect(Collectors.joining(" "));



    }
}
