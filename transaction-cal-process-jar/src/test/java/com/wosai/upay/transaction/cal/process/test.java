package com.wosai.upay.transaction.cal.process;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.transaction.cal.process.converter.SummaryBillConverter;
import com.wosai.upay.transaction.cal.process.mapper.TbSummaryBillMapper;
import com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill;
import com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBillExample;
import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.Date;

@RunWith(SpringRunner.class)
@SpringBootTest
public class test {
    @Resource
    private SummaryBillConverter summaryBillConverter;

    @Resource
    private TbSummaryBillMapper tbSummaryBillMapper;

    @Test
    public void test() {
        TbSummaryBillExample tbSummaryBillQuery =new TbSummaryBillExample();
        tbSummaryBillQuery.createCriteria().andIdEqualTo(1L);
        System.out.println(JSON.toJSONString(tbSummaryBillMapper.selectByExample(tbSummaryBillQuery)));

        TbSummaryBill tbSummaryBill = new TbSummaryBill();
        tbSummaryBill.setStatus(SummaryBillStatus.CONFIRMED);
        tbSummaryBill.setSourceBillType(1);
        tbSummaryBill.setTradeMonth(new Date());
        tbSummaryBill.setEntryMonth(new Date());
        tbSummaryBill.setPushStatus(FulfillmentPushStatus.FAILED);
        tbSummaryBill.setSourceValidRefundNum(0);
        tbSummaryBill.setSourceValidTradeAmount(0L);
        tbSummaryBill.setSourceValidTradeNum(0);
        tbSummaryBill.setSourceValidRefundAmount(0L);

        tbSummaryBillMapper.insertSelective(tbSummaryBill);
    }
}
