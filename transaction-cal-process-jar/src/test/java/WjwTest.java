import lombok.SneakyThrows;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;

import java.io.FileWriter;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2022/4/13.
 */
public class WjwTest {
    @SneakyThrows
    public static void main(String[] args) {
        CSVPrinter csv = new CSVPrinter(new FileWriter("wjwtest.csv"), CSVFormat.DEFAULT);
        csv.printRecord("hello", "test", "go\"od,s", 2, 0.33333);
        csv.flush();


    }
}
