<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbSourceBillTypeMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    <result column="id" jdbcType="INTEGER" property="id" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="source_bill_company" jdbcType="TINYINT" property="sourceBillCompany" />
    <result column="source_bill_company_name" jdbcType="VARCHAR" property="sourceBillCompanyName" />
    <result column="source_bill_classify" jdbcType="TINYINT" property="sourceBillClassify" />
    <result column="source_bill_classify_name" jdbcType="VARCHAR" property="sourceBillClassifyName" />
    <result column="source_bill_cycle" jdbcType="TINYINT" property="sourceBillCycle" />
    <result column="source_bill_cycle_name" jdbcType="VARCHAR" property="sourceBillCycleName" />
    <result column="source_bill_input_method" jdbcType="TINYINT" property="sourceBillInputMethod" />
    <result column="source_bill_input_method_name" jdbcType="VARCHAR" property="sourceBillInputMethodName" />
    <result column="split_strategy" jdbcType="TINYINT" property="splitStrategy" />
    <result column="split_strategy_name" jdbcType="VARCHAR" property="splitStrategyName" />
    <result column="policy_id" jdbcType="VARCHAR" property="policyId" />
    <result column="import_template_rule_code" jdbcType="VARCHAR" property="importTemplateRuleCode" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    <result column="config" jdbcType="LONGVARBINARY" property="config" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, name, source_bill_company, source_bill_company_name, source_bill_classify, source_bill_classify_name, 
    source_bill_cycle, source_bill_cycle_name, source_bill_input_method, source_bill_input_method_name, 
    split_strategy, split_strategy_name, policy_id, import_template_rule_code, deleted,
    create_at, update_at
  </sql>
  <sql id="Blob_Column_List">
    config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillTypeExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_source_bill_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillTypeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_source_bill_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillTypeExample">
    delete from tb_source_bill_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    insert into tb_source_bill_type (id, name, source_bill_company, 
      source_bill_company_name, source_bill_classify, 
      source_bill_classify_name, source_bill_cycle, 
      source_bill_cycle_name, source_bill_input_method, 
      source_bill_input_method_name, split_strategy, 
      split_strategy_name, policy_id, import_template_rule_code,
      deleted, create_at, update_at,
      config)
    values (#{id,jdbcType=INTEGER}, #{name,jdbcType=VARCHAR}, #{sourceBillCompany,jdbcType=TINYINT}, 
      #{sourceBillCompanyName,jdbcType=VARCHAR}, #{sourceBillClassify,jdbcType=TINYINT}, 
      #{sourceBillClassifyName,jdbcType=VARCHAR}, #{sourceBillCycle,jdbcType=TINYINT}, 
      #{sourceBillCycleName,jdbcType=VARCHAR}, #{sourceBillInputMethod,jdbcType=TINYINT}, 
      #{sourceBillInputMethodName,jdbcType=VARCHAR}, #{splitStrategy,jdbcType=TINYINT}, 
      #{splitStrategyName,jdbcType=VARCHAR}, #{policyId,jdbcType=VARCHAR}, #{importTemplateRuleCode,jdbcType=VARCHAR},
      #{deleted,jdbcType=BIT}, #{createAt,jdbcType=TIMESTAMP}, #{updateAt,jdbcType=TIMESTAMP},
      #{config,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    insert into tb_source_bill_type
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="sourceBillCompany != null">
        source_bill_company,
      </if>
      <if test="sourceBillCompanyName != null">
        source_bill_company_name,
      </if>
      <if test="sourceBillClassify != null">
        source_bill_classify,
      </if>
      <if test="sourceBillClassifyName != null">
        source_bill_classify_name,
      </if>
      <if test="sourceBillCycle != null">
        source_bill_cycle,
      </if>
      <if test="sourceBillCycleName != null">
        source_bill_cycle_name,
      </if>
      <if test="sourceBillInputMethod != null">
        source_bill_input_method,
      </if>
      <if test="sourceBillInputMethodName != null">
        source_bill_input_method_name,
      </if>
      <if test="splitStrategy != null">
        split_strategy,
      </if>
      <if test="splitStrategyName != null">
        split_strategy_name,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="importTemplateRuleCode != null">
        import_template_rule_code,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="config != null">
        config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillCompany != null">
        #{sourceBillCompany,jdbcType=TINYINT},
      </if>
      <if test="sourceBillCompanyName != null">
        #{sourceBillCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillClassify != null">
        #{sourceBillClassify,jdbcType=TINYINT},
      </if>
      <if test="sourceBillClassifyName != null">
        #{sourceBillClassifyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillCycle != null">
        #{sourceBillCycle,jdbcType=TINYINT},
      </if>
      <if test="sourceBillCycleName != null">
        #{sourceBillCycleName,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillInputMethod != null">
        #{sourceBillInputMethod,jdbcType=TINYINT},
      </if>
      <if test="sourceBillInputMethodName != null">
        #{sourceBillInputMethodName,jdbcType=VARCHAR},
      </if>
      <if test="splitStrategy != null">
        #{splitStrategy,jdbcType=TINYINT},
      </if>
      <if test="splitStrategyName != null">
        #{splitStrategyName,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=VARCHAR},
      </if>
      <if test="importTemplateRuleCode != null">
        #{importTemplateRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="config != null">
        #{config,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillTypeExample" resultType="java.lang.Long">
    select count(*) from tb_source_bill_type
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_source_bill_type
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceBillCompany != null">
        source_bill_company = #{record.sourceBillCompany,jdbcType=TINYINT},
      </if>
      <if test="record.sourceBillCompanyName != null">
        source_bill_company_name = #{record.sourceBillCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceBillClassify != null">
        source_bill_classify = #{record.sourceBillClassify,jdbcType=TINYINT},
      </if>
      <if test="record.sourceBillClassifyName != null">
        source_bill_classify_name = #{record.sourceBillClassifyName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceBillCycle != null">
        source_bill_cycle = #{record.sourceBillCycle,jdbcType=TINYINT},
      </if>
      <if test="record.sourceBillCycleName != null">
        source_bill_cycle_name = #{record.sourceBillCycleName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceBillInputMethod != null">
        source_bill_input_method = #{record.sourceBillInputMethod,jdbcType=TINYINT},
      </if>
      <if test="record.sourceBillInputMethodName != null">
        source_bill_input_method_name = #{record.sourceBillInputMethodName,jdbcType=VARCHAR},
      </if>
      <if test="record.splitStrategy != null">
        split_strategy = #{record.splitStrategy,jdbcType=TINYINT},
      </if>
      <if test="record.splitStrategyName != null">
        split_strategy_name = #{record.splitStrategyName,jdbcType=VARCHAR},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=VARCHAR},
      </if>
      <if test="record.importTemplateRuleCode != null">
        import_template_rule_code = #{record.importTemplateRuleCode,jdbcType=VARCHAR},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateAt != null">
        update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.config != null">
        config = #{record.config,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tb_source_bill_type
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      source_bill_company = #{record.sourceBillCompany,jdbcType=TINYINT},
      source_bill_company_name = #{record.sourceBillCompanyName,jdbcType=VARCHAR},
      source_bill_classify = #{record.sourceBillClassify,jdbcType=TINYINT},
      source_bill_classify_name = #{record.sourceBillClassifyName,jdbcType=VARCHAR},
      source_bill_cycle = #{record.sourceBillCycle,jdbcType=TINYINT},
      source_bill_cycle_name = #{record.sourceBillCycleName,jdbcType=VARCHAR},
      source_bill_input_method = #{record.sourceBillInputMethod,jdbcType=TINYINT},
      source_bill_input_method_name = #{record.sourceBillInputMethodName,jdbcType=VARCHAR},
      split_strategy = #{record.splitStrategy,jdbcType=TINYINT},
      split_strategy_name = #{record.splitStrategyName,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=VARCHAR},
      import_template_rule_code = #{record.importTemplateRuleCode,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      config = #{record.config,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_source_bill_type
    set id = #{record.id,jdbcType=INTEGER},
      name = #{record.name,jdbcType=VARCHAR},
      source_bill_company = #{record.sourceBillCompany,jdbcType=TINYINT},
      source_bill_company_name = #{record.sourceBillCompanyName,jdbcType=VARCHAR},
      source_bill_classify = #{record.sourceBillClassify,jdbcType=TINYINT},
      source_bill_classify_name = #{record.sourceBillClassifyName,jdbcType=VARCHAR},
      source_bill_cycle = #{record.sourceBillCycle,jdbcType=TINYINT},
      source_bill_cycle_name = #{record.sourceBillCycleName,jdbcType=VARCHAR},
      source_bill_input_method = #{record.sourceBillInputMethod,jdbcType=TINYINT},
      source_bill_input_method_name = #{record.sourceBillInputMethodName,jdbcType=VARCHAR},
      split_strategy = #{record.splitStrategy,jdbcType=TINYINT},
      split_strategy_name = #{record.splitStrategyName,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=VARCHAR},
      import_template_rule_code = #{record.importTemplateRuleCode,jdbcType=VARCHAR},
      deleted = #{record.deleted,jdbcType=BIT},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    update tb_source_bill_type
    <set>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillCompany != null">
        source_bill_company = #{sourceBillCompany,jdbcType=TINYINT},
      </if>
      <if test="sourceBillCompanyName != null">
        source_bill_company_name = #{sourceBillCompanyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillClassify != null">
        source_bill_classify = #{sourceBillClassify,jdbcType=TINYINT},
      </if>
      <if test="sourceBillClassifyName != null">
        source_bill_classify_name = #{sourceBillClassifyName,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillCycle != null">
        source_bill_cycle = #{sourceBillCycle,jdbcType=TINYINT},
      </if>
      <if test="sourceBillCycleName != null">
        source_bill_cycle_name = #{sourceBillCycleName,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillInputMethod != null">
        source_bill_input_method = #{sourceBillInputMethod,jdbcType=TINYINT},
      </if>
      <if test="sourceBillInputMethodName != null">
        source_bill_input_method_name = #{sourceBillInputMethodName,jdbcType=VARCHAR},
      </if>
      <if test="splitStrategy != null">
        split_strategy = #{splitStrategy,jdbcType=TINYINT},
      </if>
      <if test="splitStrategyName != null">
        split_strategy_name = #{splitStrategyName,jdbcType=VARCHAR},
      </if>
      <if test="deleted != null">
        deleted = #{deleted,jdbcType=BIT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    update tb_source_bill_type
    set name = #{name,jdbcType=VARCHAR},
      source_bill_company = #{sourceBillCompany,jdbcType=TINYINT},
      source_bill_company_name = #{sourceBillCompanyName,jdbcType=VARCHAR},
      source_bill_classify = #{sourceBillClassify,jdbcType=TINYINT},
      source_bill_classify_name = #{sourceBillClassifyName,jdbcType=VARCHAR},
      source_bill_cycle = #{sourceBillCycle,jdbcType=TINYINT},
      source_bill_cycle_name = #{sourceBillCycleName,jdbcType=VARCHAR},
      source_bill_input_method = #{sourceBillInputMethod,jdbcType=TINYINT},
      source_bill_input_method_name = #{sourceBillInputMethodName,jdbcType=VARCHAR},
      split_strategy = #{splitStrategy,jdbcType=TINYINT},
      split_strategy_name = #{splitStrategyName,jdbcType=VARCHAR},
      deleted = #{deleted,jdbcType=BIT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <select id="selectBySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType" resultType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
    SELECT
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    FROM tb_source_bill_type
    <where>
      <if test="id != null">
        AND id = #{id,jdbcType=INTEGER}
      </if>
      <if test="name != null">
        AND name = #{name,jdbcType=VARCHAR}
      </if>
      <if test="sourceBillCompany != null">
        AND source_bill_company = #{sourceBillCompany,jdbcType=TINYINT}
      </if>
      <if test="sourceBillCompanyName != null">
        AND source_bill_company_name = #{sourceBillCompanyName,jdbcType=VARCHAR}
      </if>
      <if test="sourceBillClassify != null">
        AND source_bill_classify = #{sourceBillClassify,jdbcType=TINYINT}
      </if>
      <if test="sourceBillClassifyName != null">
        AND source_bill_classify_name = #{sourceBillClassifyName,jdbcType=VARCHAR}
      </if>
      <if test="sourceBillCycle != null">
        AND source_bill_cycle = #{sourceBillCycle,jdbcType=TINYINT}
      </if>
      <if test="sourceBillCycleName != null">
        AND source_bill_cycle_name = #{sourceBillCycleName,jdbcType=VARCHAR}
      </if>
      <if test="sourceBillInputMethod != null">
        AND source_bill_input_method = #{sourceBillInputMethod,jdbcType=TINYINT}
      </if>
      <if test="sourceBillInputMethodName != null">
        AND source_bill_input_method_name = #{sourceBillInputMethodName,jdbcType=VARCHAR}
      </if>
      <if test="splitStrategy != null">
        AND split_strategy = #{splitStrategy,jdbcType=TINYINT}
      </if>
      <if test="splitStrategyName != null">
        AND split_strategy_name = #{splitStrategyName,jdbcType=VARCHAR}
      </if>
      <if test="deleted != null">
        AND deleted = #{deleted,jdbcType=BIT}
      </if>
    </where>
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_source_bill_type
    where id = #{id,jdbcType=INTEGER}
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from tb_source_bill_type
    where id = #{id,jdbcType=INTEGER}
  </delete>
</mapper>