<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbSourceBillMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_bill_input_task_id" jdbcType="INTEGER" property="sourceBillInputTaskId" />
    <result column="trade_date_month" jdbcType="DATE" property="tradeDateMonth" />
    <result column="source_bill_type" jdbcType="INTEGER" property="sourceBillType" />
    <result column="sub_num" jdbcType="INTEGER" property="subNum" />
    <result column="source_merchant_id" jdbcType="VARCHAR" property="sourceMerchantId" />
    <result column="source_merchant_name" jdbcType="VARCHAR" property="sourceMerchantName" />
    <result column="source_level2_merchant_id" jdbcType="VARCHAR" property="sourceLevel2MerchantId" />
    <result column="source_level2_merchant_name" jdbcType="VARCHAR" property="sourceLevel2MerchantName" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="source_valid_trade_num" jdbcType="INTEGER" property="sourceValidTradeNum" />
    <result column="source_valid_trade_amount" jdbcType="BIGINT" property="sourceValidTradeAmount" />
    <result column="source_valid_refund_num" jdbcType="INTEGER" property="sourceValidRefundNum" />
    <result column="source_valid_refund_amount" jdbcType="BIGINT" property="sourceValidRefundAmount" />
    <result column="source_settlement_basis_type" jdbcType="VARCHAR" property="sourceSettlementBasisType" />
    <result column="source_settlement_basis" jdbcType="BIGINT" property="sourceSettlementBasis" />
    <result column="source_merchant_fee_rate" jdbcType="VARCHAR" property="sourceMerchantFeeRate" />
    <result column="source_settlement_fee_rate" jdbcType="VARCHAR" property="sourceSettlementFeeRate" />
    <result column="source_settlement_amount" jdbcType="BIGINT" property="sourceSettlementAmount" />
    <result column="is_import" jdbcType="BIT" property="isImport" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="capping_rate" jdbcType="VARCHAR" property="cappingRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_bill_input_task_id, trade_date_month, source_bill_type, sub_num, source_merchant_id, 
    source_merchant_name, source_level2_merchant_id, source_level2_merchant_name, dimension, 
    source_valid_trade_num, source_valid_trade_amount, source_valid_refund_num, source_valid_refund_amount, 
    source_settlement_basis_type, source_settlement_basis, source_merchant_fee_rate, 
    source_settlement_fee_rate, source_settlement_amount, is_import, remark, create_at, 
    update_at, capping_rate
  </sql>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_source_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillExample">
    delete from tb_source_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    limit 1000
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill">
    insert into tb_source_bill (id, source_bill_input_task_id, trade_date_month, 
      source_bill_type, sub_num, source_merchant_id, 
      source_merchant_name, source_level2_merchant_id, 
      source_level2_merchant_name, dimension, source_valid_trade_num, 
      source_valid_trade_amount, source_valid_refund_num, 
      source_valid_refund_amount, source_settlement_basis_type, 
      source_settlement_basis, source_merchant_fee_rate, 
      source_settlement_fee_rate, source_settlement_amount, 
      is_import, remark, create_at, 
      update_at, capping_rate)
    values (#{id,jdbcType=BIGINT}, #{sourceBillInputTaskId,jdbcType=INTEGER}, #{tradeDateMonth,jdbcType=DATE}, 
      #{sourceBillType,jdbcType=INTEGER}, #{subNum,jdbcType=INTEGER}, #{sourceMerchantId,jdbcType=VARCHAR}, 
      #{sourceMerchantName,jdbcType=VARCHAR}, #{sourceLevel2MerchantId,jdbcType=VARCHAR}, 
      #{sourceLevel2MerchantName,jdbcType=VARCHAR}, #{dimension,jdbcType=VARCHAR}, #{sourceValidTradeNum,jdbcType=INTEGER}, 
      #{sourceValidTradeAmount,jdbcType=BIGINT}, #{sourceValidRefundNum,jdbcType=INTEGER}, 
      #{sourceValidRefundAmount,jdbcType=BIGINT}, #{sourceSettlementBasisType,jdbcType=VARCHAR}, 
      #{sourceSettlementBasis,jdbcType=BIGINT}, #{sourceMerchantFeeRate,jdbcType=VARCHAR}, 
      #{sourceSettlementFeeRate,jdbcType=VARCHAR}, #{sourceSettlementAmount,jdbcType=BIGINT}, 
      #{isImport,jdbcType=BIT}, #{remark,jdbcType=VARCHAR}, #{createAt,jdbcType=TIMESTAMP}, 
      #{updateAt,jdbcType=TIMESTAMP}, #{cappingRate,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill">
    insert into tb_source_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceBillInputTaskId != null">
        source_bill_input_task_id,
      </if>
      <if test="tradeDateMonth != null">
        trade_date_month,
      </if>
      <if test="sourceBillType != null">
        source_bill_type,
      </if>
      <if test="subNum != null">
        sub_num,
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id,
      </if>
      <if test="sourceMerchantName != null">
        source_merchant_name,
      </if>
      <if test="sourceLevel2MerchantId != null">
        source_level2_merchant_id,
      </if>
      <if test="sourceLevel2MerchantName != null">
        source_level2_merchant_name,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num,
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount,
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num,
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount,
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type,
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis,
      </if>
      <if test="sourceMerchantFeeRate != null">
        source_merchant_fee_rate,
      </if>
      <if test="sourceSettlementFeeRate != null">
        source_settlement_fee_rate,
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount,
      </if>
      <if test="isImport != null">
        is_import,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="cappingRate != null">
        capping_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sourceBillInputTaskId != null">
        #{sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="tradeDateMonth != null">
        #{tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="subNum != null">
        #{subNum,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceMerchantName != null">
        #{sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantId != null">
        #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantName != null">
        #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="sourceValidTradeNum != null">
        #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceSettlementBasisType != null">
        #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantFeeRate != null">
        #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementFeeRate != null">
        #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementAmount != null">
        #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="isImport != null">
        #{isImport,jdbcType=BIT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="cappingRate != null">
        #{cappingRate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert" >
    insert into tb_source_bill
    (source_bill_input_task_id, trade_date_month, source_bill_type,
    sub_num,
    source_merchant_id, source_merchant_name, source_level2_merchant_id, source_level2_merchant_name,
    dimension,
    source_valid_trade_num, source_valid_trade_amount, source_valid_refund_num, source_valid_refund_amount,
    source_settlement_basis_type, source_settlement_basis, source_merchant_fee_rate, source_settlement_fee_rate, source_settlement_amount,
    remark, extra_params, capping_rate,service_provider_id)
    values
    <foreach collection="list" index="index" item="item" open="" close="" separator=",">
      (
      #{sourceBillInputTaskId}, #{tradeDateMonth}, #{sourceBillType},
      #{item.subNum},
      #{item.sourceMerchantId}, #{item.sourceMerchantName}, #{item.sourceLevel2MerchantId}, #{item.sourceLevel2MerchantName},
      #{item.dimension},
      #{item.sourceValidTradeNum}, #{item.sourceValidTradeAmount}, #{item.sourceValidRefundNum}, #{item.sourceValidRefundAmount},
      #{item.sourceSettlementBasisType}, #{item.sourceSettlementBasis}, #{item.sourceMerchantFeeRate}, #{item.sourceSettlementFeeRate}, #{item.sourceSettlementAmount},
      #{item.remark},#{item.extraParams, jdbcType=OTHER, typeHandler=com.wosai.upay.transaction.cal.process.mapper.dao.JsonBlobTypeHandler}, #{item.cappingRate},#{item.serviceProviderId}
      )
    </foreach>

  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillExample" resultType="java.lang.Long">
    select count(*) from tb_source_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_source_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sourceBillInputTaskId != null">
        source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="record.tradeDateMonth != null">
        trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="record.sourceBillType != null">
        source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="record.subNum != null">
        sub_num = #{record.subNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceMerchantId != null">
        source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceMerchantName != null">
        source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLevel2MerchantId != null">
        source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLevel2MerchantName != null">
        source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.dimension != null">
        dimension = #{record.dimension,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceValidTradeNum != null">
        source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidTradeAmount != null">
        source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceValidRefundNum != null">
        source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidRefundAmount != null">
        source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceSettlementBasisType != null">
        source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementBasis != null">
        source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="record.sourceMerchantFeeRate != null">
        source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementFeeRate != null">
        source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementAmount != null">
        source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="record.isImport != null">
        is_import = #{record.isImport,jdbcType=BIT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateAt != null">
        update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cappingRate != null">
        capping_rate = #{record.cappingRate,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

<!--  customer sql-->
  <update id="updateByExample" parameterType="map">
    update tb_source_bill
    set id = #{record.id,jdbcType=BIGINT},
      source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
      trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      sub_num = #{record.subNum,jdbcType=INTEGER},
      source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
      source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
      source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
      dimension = #{record.dimension,jdbcType=VARCHAR},
      source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
      source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
      source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      is_import = #{record.isImport,jdbcType=BIT},
      remark = #{record.remark,jdbcType=VARCHAR},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      capping_rate = #{record.cappingRate,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill">
    update tb_source_bill
    <set>
      <if test="sourceBillInputTaskId != null">
        source_bill_input_task_id = #{sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="tradeDateMonth != null">
        trade_date_month = #{tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="subNum != null">
        sub_num = #{subNum,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceMerchantName != null">
        source_merchant_name = #{sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantId != null">
        source_level2_merchant_id = #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantName != null">
        source_level2_merchant_name = #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type = #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis = #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantFeeRate != null">
        source_merchant_fee_rate = #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementFeeRate != null">
        source_settlement_fee_rate = #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="isImport != null">
        is_import = #{isImport,jdbcType=BIT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="cappingRate != null">
        capping_rate = #{cappingRate,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill">
    update tb_source_bill
    set source_bill_input_task_id = #{sourceBillInputTaskId,jdbcType=INTEGER},
      trade_date_month = #{tradeDateMonth,jdbcType=DATE},
      source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      sub_num = #{subNum,jdbcType=INTEGER},
      source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      source_merchant_name = #{sourceMerchantName,jdbcType=VARCHAR},
      source_level2_merchant_id = #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      source_level2_merchant_name = #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      dimension = #{dimension,jdbcType=VARCHAR},
      source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
      source_settlement_basis_type = #{sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{sourceSettlementBasis,jdbcType=BIGINT},
      source_merchant_fee_rate = #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      source_settlement_fee_rate = #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
      is_import = #{isImport,jdbcType=BIT},
      remark = #{remark,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      capping_rate = #{cappingRate,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_source_bill
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_source_bill
    where id = #{id,jdbcType=BIGINT}
  </delete>
</mapper>