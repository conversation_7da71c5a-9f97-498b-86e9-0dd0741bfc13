<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.MerchantAuthorizeMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="task_id" jdbcType="BIGINT" property="taskId" />
    <result column="merchant_id" jdbcType="VARCHAR" property="merchantId" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="ctime" jdbcType="BIGINT" property="ctime" />
    <result column="mtime" jdbcType="BIGINT" property="mtime" />
    <result column="version" jdbcType="BIGINT" property="version" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, task_id, merchant_id, status, ctime, mtime, version
  </sql>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorizeExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from merchant_authorize
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from merchant_authorize
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from merchant_authorize
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorizeExample">
    delete from merchant_authorize
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize">
    insert into merchant_authorize (id, task_id, merchant_id, 
      status, ctime, mtime, 
      version)
    values (#{id,jdbcType=BIGINT}, #{taskId,jdbcType=BIGINT}, #{merchantId,jdbcType=VARCHAR}, 
      #{status,jdbcType=INTEGER}, #{ctime,jdbcType=BIGINT}, #{mtime,jdbcType=BIGINT}, 
      #{version,jdbcType=BIGINT})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize">
    insert into merchant_authorize
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="taskId != null">
        task_id,
      </if>
      <if test="merchantId != null">
        merchant_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="version != null">
        version,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="taskId != null">
        #{taskId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorizeExample" resultType="java.lang.Long">
    select count(*) from merchant_authorize
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update merchant_authorize
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.taskId != null">
        task_id = #{record.taskId,jdbcType=BIGINT},
      </if>
      <if test="record.merchantId != null">
        merchant_id = #{record.merchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=BIGINT},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update merchant_authorize
    set id = #{record.id,jdbcType=BIGINT},
      task_id = #{record.taskId,jdbcType=BIGINT},
      merchant_id = #{record.merchantId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=BIGINT},
      mtime = #{record.mtime,jdbcType=BIGINT},
      version = #{record.version,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize">
    update merchant_authorize
    <set>
      <if test="taskId != null">
        task_id = #{taskId,jdbcType=BIGINT},
      </if>
      <if test="merchantId != null">
        merchant_id = #{merchantId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=BIGINT},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize">
    update merchant_authorize
    set task_id = #{taskId,jdbcType=BIGINT},
      merchant_id = #{merchantId,jdbcType=VARCHAR},
      status = #{status,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=BIGINT},
      mtime = #{mtime,jdbcType=BIGINT},
      version = #{version,jdbcType=BIGINT}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByExampleWithPage" parameterType="map" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM merchant_authorize
    <if test="example != null">
      <where>
        <foreach collection="example.oredCriteria" item="criteria" separator=" OR ">
          <if test="criteria.valid">
            <trim prefix="(" suffix=")" prefixOverrides="AND ">
              <foreach collection="criteria.criteria" item="criterion">
                <choose>
                  <when test="criterion.noValue">
                    AND ${criterion.condition}
                  </when>
                  <when test="criterion.singleValue">
                    AND ${criterion.condition} #{criterion.value}
                  </when>
                  <when test="criterion.betweenValue">
                    AND ${criterion.condition} #{criterion.value} AND #{criterion.secondValue}
                  </when>
                  <when test="criterion.listValue">
                    AND ${criterion.condition}
                    <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                      #{listItem}
                    </foreach>
                  </when>
                </choose>
              </foreach>
            </trim>
          </if>
        </foreach>
      </where>
    </if>
    LIMIT #{offset}, #{limit}
  </select>
</mapper>