<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbSummaryBillMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="sn" jdbcType="VARCHAR" property="sn" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="policy_id" jdbcType="VARCHAR" property="policyId" />
    <result column="trade_month" jdbcType="DATE" property="tradeMonth" />
    <result column="entry_month" jdbcType="DATE" property="entryMonth" />
    <result column="source_bill_type" jdbcType="INTEGER" property="sourceBillType" />
    <result column="source_valid_trade_num" jdbcType="INTEGER" property="sourceValidTradeNum" />
    <result column="source_valid_trade_amount" jdbcType="BIGINT" property="sourceValidTradeAmount" />
    <result column="source_valid_refund_num" jdbcType="INTEGER" property="sourceValidRefundNum" />
    <result column="source_valid_refund_amount" jdbcType="BIGINT" property="sourceValidRefundAmount" />
    <result column="source_settlement_basis_type" jdbcType="VARCHAR" property="sourceSettlementBasisType" />
    <result column="source_settlement_basis" jdbcType="BIGINT" property="sourceSettlementBasis" />
    <result column="source_settlement_amount" jdbcType="BIGINT" property="sourceSettlementAmount" />
    <result column="source_merchant_number" jdbcType="BIGINT" property="sourceMerchantNumber" />
    <result column="server_channel_code" jdbcType="VARCHAR" property="serverChannelCode" />
    <result column="nc_name" jdbcType="VARCHAR" property="ncName" />
    <result column="company_name" jdbcType="VARCHAR" property="companyName" />
    <result column="property_id" jdbcType="VARCHAR" property="propertyId" />
    <result column="status" jdbcType="VARCHAR" property="status" />
    <result column="push_status" jdbcType="VARCHAR" property="pushStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
    <result column="version" jdbcType="INTEGER" property="version" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill">
    <result column="extra_params" jdbcType="LONGVARBINARY" property="extraParams" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sn, name, policy_id, trade_month, entry_month, source_bill_type, source_valid_trade_num,
    source_valid_trade_amount, source_valid_refund_num, source_valid_refund_amount, source_settlement_basis_type,
    source_settlement_basis, source_settlement_amount, source_merchant_number, server_channel_code,
    nc_name, company_name, property_id, status, push_status, remark, ctime, mtime, deleted, version
  </sql>
  <sql id="Blob_Column_List">
    extra_params
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBillExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_summary_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBillExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_summary_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBillExample">
    delete from tb_summary_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill">
    insert into tb_summary_bill (id, sn, name,
      policy_id, trade_month, entry_month,
      source_bill_type, source_valid_trade_num, source_valid_trade_amount,
      source_valid_refund_num, source_valid_refund_amount,
      source_settlement_basis_type, source_settlement_basis,
      source_settlement_amount, source_merchant_number,
      server_channel_code, nc_name, company_name,
      property_id, status, push_status,
      remark, ctime, mtime,
      deleted, version, extra_params
      )
    values (#{id,jdbcType=BIGINT}, #{sn,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR},
      #{policyId,jdbcType=VARCHAR}, #{tradeMonth,jdbcType=DATE}, #{entryMonth,jdbcType=DATE},
      #{sourceBillType,jdbcType=INTEGER}, #{sourceValidTradeNum,jdbcType=INTEGER}, #{sourceValidTradeAmount,jdbcType=BIGINT},
      #{sourceValidRefundNum,jdbcType=INTEGER}, #{sourceValidRefundAmount,jdbcType=BIGINT},
      #{sourceSettlementBasisType,jdbcType=VARCHAR}, #{sourceSettlementBasis,jdbcType=BIGINT},
      #{sourceSettlementAmount,jdbcType=BIGINT}, #{sourceMerchantNumber,jdbcType=BIGINT},
      #{serverChannelCode,jdbcType=VARCHAR}, #{ncName,jdbcType=VARCHAR}, #{companyName,jdbcType=VARCHAR},
      #{propertyId,jdbcType=VARCHAR}, #{status,jdbcType=VARCHAR}, #{pushStatus,jdbcType=VARCHAR},
      #{remark,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP},
      #{deleted,jdbcType=BIT}, #{version,jdbcType=INTEGER}, #{extraParams,jdbcType=LONGVARBINARY}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill">
    insert into tb_summary_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="tradeMonth != null">
        trade_month,
      </if>
      <if test="entryMonth != null">
        entry_month,
      </if>
      <if test="sourceBillType != null">
        source_bill_type,
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num,
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount,
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num,
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount,
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type,
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis,
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount,
      </if>
      <if test="sourceMerchantNumber != null">
        source_merchant_number,
      </if>
      <if test="serverChannelCode != null">
        server_channel_code,
      </if>
      <if test="ncName != null">
        nc_name,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="propertyId != null">
        property_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extraParams != null">
        extra_params,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=VARCHAR},
      </if>
      <if test="tradeMonth != null">
        #{tradeMonth,jdbcType=DATE},
      </if>
      <if test="entryMonth != null">
        #{entryMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeNum != null">
        #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceSettlementBasisType != null">
        #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceSettlementAmount != null">
        #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantNumber != null">
        #{sourceMerchantNumber,jdbcType=BIGINT},
      </if>
      <if test="serverChannelCode != null">
        #{serverChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="ncName != null">
        #{ncName,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="propertyId != null">
        #{propertyId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extraParams != null">
        #{extraParams,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBillExample" resultType="java.lang.Long">
    select count(*) from tb_summary_bill
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_summary_bill
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sn != null">
        sn = #{record.sn,jdbcType=VARCHAR},
      </if>
      <if test="record.name != null">
        name = #{record.name,jdbcType=VARCHAR},
      </if>
      <if test="record.policyId != null">
        policy_id = #{record.policyId,jdbcType=VARCHAR},
      </if>
      <if test="record.tradeMonth != null">
        trade_month = #{record.tradeMonth,jdbcType=DATE},
      </if>
      <if test="record.entryMonth != null">
        entry_month = #{record.entryMonth,jdbcType=DATE},
      </if>
      <if test="record.sourceBillType != null">
        source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidTradeNum != null">
        source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidTradeAmount != null">
        source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceValidRefundNum != null">
        source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidRefundAmount != null">
        source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceSettlementBasisType != null">
        source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementBasis != null">
        source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="record.sourceSettlementAmount != null">
        source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceMerchantNumber != null">
        source_merchant_number = #{record.sourceMerchantNumber,jdbcType=BIGINT},
      </if>
      <if test="record.serverChannelCode != null">
        server_channel_code = #{record.serverChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="record.ncName != null">
        nc_name = #{record.ncName,jdbcType=VARCHAR},
      </if>
      <if test="record.companyName != null">
        company_name = #{record.companyName,jdbcType=VARCHAR},
      </if>
      <if test="record.propertyId != null">
        property_id = #{record.propertyId,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=VARCHAR},
      </if>
      <if test="record.pushStatus != null">
        push_status = #{record.pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.deleted != null">
        deleted = #{record.deleted,jdbcType=BIT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.extraParams != null">
        extra_params = #{record.extraParams,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tb_summary_bill
    set id = #{record.id,jdbcType=BIGINT},
      sn = #{record.sn,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=VARCHAR},
      trade_month = #{record.tradeMonth,jdbcType=DATE},
      entry_month = #{record.entryMonth,jdbcType=DATE},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      source_merchant_number = #{record.sourceMerchantNumber,jdbcType=BIGINT},
      server_channel_code = #{record.serverChannelCode,jdbcType=VARCHAR},
      nc_name = #{record.ncName,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      property_id = #{record.propertyId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      push_status = #{record.pushStatus,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      version = #{record.version,jdbcType=INTEGER},
      extra_params = #{record.extraParams,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_summary_bill
    set id = #{record.id,jdbcType=BIGINT},
      sn = #{record.sn,jdbcType=VARCHAR},
      name = #{record.name,jdbcType=VARCHAR},
      policy_id = #{record.policyId,jdbcType=VARCHAR},
      trade_month = #{record.tradeMonth,jdbcType=DATE},
      entry_month = #{record.entryMonth,jdbcType=DATE},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      source_merchant_number = #{record.sourceMerchantNumber,jdbcType=BIGINT},
      server_channel_code = #{record.serverChannelCode,jdbcType=VARCHAR},
      nc_name = #{record.ncName,jdbcType=VARCHAR},
      company_name = #{record.companyName,jdbcType=VARCHAR},
      property_id = #{record.propertyId,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=VARCHAR},
      push_status = #{record.pushStatus,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      deleted = #{record.deleted,jdbcType=BIT},
      version = #{record.version,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <insert id="insertSelectiveOrUpdate" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill">
    insert into tb_summary_bill
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sn != null">
        sn,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="policyId != null">
        policy_id,
      </if>
      <if test="tradeMonth != null">
        trade_month,
      </if>
      <if test="entryMonth != null">
        entry_month,
      </if>
      <if test="sourceBillType != null">
        source_bill_type,
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num,
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount,
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num,
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount,
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type,
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis,
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount,
      </if>
      <if test="sourceMerchantNumber != null">
        source_merchant_number,
      </if>
      <if test="serverChannelCode != null">
        server_channel_code,
      </if>
      <if test="ncName != null">
        nc_name,
      </if>
      <if test="companyName != null">
        company_name,
      </if>
      <if test="propertyId != null">
        property_id,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="deleted != null">
        deleted,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="extraParams != null">
        extra_params,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sn != null">
        #{sn,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="policyId != null">
        #{policyId,jdbcType=VARCHAR},
      </if>
      <if test="tradeMonth != null">
        #{tradeMonth,jdbcType=DATE},
      </if>
      <if test="entryMonth != null">
        #{entryMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeNum != null">
        #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceSettlementBasisType != null">
        #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceSettlementAmount != null">
        #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantNumber != null">
        #{sourceMerchantNumber,jdbcType=BIGINT},
      </if>
      <if test="serverChannelCode != null">
        #{serverChannelCode,jdbcType=VARCHAR},
      </if>
      <if test="ncName != null">
        #{ncName,jdbcType=VARCHAR},
      </if>
      <if test="companyName != null">
        #{companyName,jdbcType=VARCHAR},
      </if>
      <if test="propertyId != null">
        #{propertyId,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=VARCHAR},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="deleted != null">
        #{deleted,jdbcType=BIT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="extraParams != null">
        #{extraParams,jdbcType=LONGVARBINARY},
      </if>
    </trim>
    ON DUPLICATE KEY UPDATE
    policy_id = #{policyId,jdbcType=VARCHAR},
    trade_month = #{tradeMonth,jdbcType=DATE},
    entry_month = #{entryMonth,jdbcType=DATE},
    source_bill_type = #{sourceBillType,jdbcType=INTEGER},
    source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
    source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
    source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
    source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
    source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
    source_merchant_number = #{sourceMerchantNumber,jdbcType=BIGINT},
    property_id = #{propertyId,jdbcType=VARCHAR},
    nc_name = #{ncName,jdbcType=VARCHAR},
    company_name = #{companyName,jdbcType=VARCHAR},
    status = #{status,jdbcType=VARCHAR},
    push_status = #{pushStatus,jdbcType=VARCHAR},
    remark = #{remark,jdbcType=VARCHAR},
    extra_params = #{extraParams,jdbcType=LONGVARBINARY}
  </insert>
</mapper>