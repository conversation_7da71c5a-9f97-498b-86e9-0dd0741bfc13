<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbSourceBillInputTaskMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTask">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="source_bill_type" jdbcType="INTEGER" property="sourceBillType" />
    <result column="source_bill_start_date" jdbcType="DATE" property="sourceBillStartDate" />
    <result column="source_bill_end_date" jdbcType="DATE" property="sourceBillEndDate" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="file_url" jdbcType="VARCHAR" property="fileUrl" />
    <result column="create_by" jdbcType="VARCHAR" property="createBy" />
    <result column="confirm_by" jdbcType="VARCHAR" property="confirmBy" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs">
    <result column="remark" jdbcType="LONGVARBINARY" property="remark" />
    <result column="error_info" jdbcType="LONGVARBINARY" property="errorInfo" />
    <result column="extra" jdbcType="LONGVARBINARY" property="extra" />
  </resultMap>
  <resultMap id="TaskWithType" extends="ResultMapWithBLOBs" type="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithType">
    <association property="tbSourceBillType" javaType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType">
      <id column="sourceBillTypeId" jdbcType="INTEGER" property="id" />
      <result column="name" jdbcType="VARCHAR" property="name" />
      <result column="source_bill_company" jdbcType="TINYINT" property="sourceBillCompany" />
      <result column="source_bill_company_name" jdbcType="VARCHAR" property="sourceBillCompanyName" />
      <result column="source_bill_classify" jdbcType="TINYINT" property="sourceBillClassify" />
      <result column="source_bill_classify_name" jdbcType="VARCHAR" property="sourceBillClassifyName" />
      <result column="source_bill_cycle" jdbcType="TINYINT" property="sourceBillCycle" />
      <result column="source_bill_cycle_name" jdbcType="VARCHAR" property="sourceBillCycleName" />
      <result column="source_bill_input_method" jdbcType="TINYINT" property="sourceBillInputMethod" />
      <result column="source_bill_input_method_name" jdbcType="VARCHAR" property="sourceBillInputMethodName" />
      <result column="split_strategy" jdbcType="TINYINT" property="splitStrategy" />
      <result column="split_strategy_name" jdbcType="VARCHAR" property="splitStrategyName" />
      <result column="deleted" jdbcType="BIT" property="deleted" />
      <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
      <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    </association>
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_bill_type, source_bill_start_date, source_bill_end_date, task_status,
    file_url, create_by, confirm_by, create_at, update_at
  </sql>
  <sql id="Blob_Column_List">
    remark, error_info, extra,config
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskExample" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_source_bill_input_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_source_bill_input_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="ResultMapWithBLOBs">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_source_bill_input_task
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="selectJoinBillTypeList" resultMap="TaskWithType">
    SELECT
    t0.* ,
    t1.id AS 'sourceBillTypeId',
    t1.name,
    t1.source_bill_company,
    t1.source_bill_company_name,
    t1.source_bill_classify,
    t1.source_bill_classify_name,
    t1.source_bill_cycle,
    t1.source_bill_cycle_name,
    t1.source_bill_input_method,
    t1.source_bill_input_method_name,
    t1.split_strategy,
    t1.split_strategy_name
    FROM tb_source_bill_input_task t0
    LEFT JOIN tb_source_bill_type t1 ON t0.source_bill_type = t1.id
    <where>
      <if test="sourceBillType != null">
        AND t0.source_bill_type = #{sourceBillType}
      </if>
      <if test="sourceBillCompany != null">
        AND t1.source_bill_company = #{sourceBillCompany}
      </if>
      <if test="sourceBillClassify != null">
        AND t1.source_bill_classify = #{sourceBillClassify}
      </if>
      <if test="taskStatus != null">
        AND t0.task_status = #{taskStatus}
      </if>
      <if test="createAtStart != null">
        AND t0.create_at &gt;= #{createAtStart}
      </if>
      <if test="createAtEnd != null">
        AND t0.create_at &lt;= #{createAtEnd}
      </if>
      <if test="tradeDateMonth != null">
        AND date_format(t0.source_bill_start_date, '%Y%m') = #{tradeDateMonth}
      </if>
    </where>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from tb_source_bill_input_task
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskExample">
    delete from tb_source_bill_input_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs">
    insert into tb_source_bill_input_task (id, source_bill_type, source_bill_start_date,
      source_bill_end_date, task_status, file_url,
      create_by, confirm_by, create_at,
      update_at, remark, error_info,
      extra)
    values (#{id,jdbcType=INTEGER}, #{sourceBillType,jdbcType=INTEGER}, #{sourceBillStartDate,jdbcType=DATE},
      #{sourceBillEndDate,jdbcType=DATE}, #{taskStatus,jdbcType=INTEGER}, #{fileUrl,jdbcType=VARCHAR},
      #{createBy,jdbcType=VARCHAR}, #{confirmBy,jdbcType=VARCHAR}, #{createAt,jdbcType=TIMESTAMP},
      #{updateAt,jdbcType=TIMESTAMP}, #{remark,jdbcType=LONGVARBINARY}, #{errorInfo,jdbcType=LONGVARBINARY},
      #{extra,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs"
          keyProperty="id" useGeneratedKeys="true">
    insert into tb_source_bill_input_task
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceBillType != null">
        source_bill_type,
      </if>
      <if test="sourceBillStartDate != null">
        source_bill_start_date,
      </if>
      <if test="sourceBillEndDate != null">
        source_bill_end_date,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="fileUrl != null">
        file_url,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="confirmBy != null">
        confirm_by,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="errorInfo != null">
        error_info,
      </if>
      <if test="extra != null">
        extra,
      </if>
      <if test="config != null">
        config,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="sourceBillType != null">
        #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="sourceBillStartDate != null">
        #{sourceBillStartDate,jdbcType=DATE},
      </if>
      <if test="sourceBillEndDate != null">
        #{sourceBillEndDate,jdbcType=DATE},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="confirmBy != null">
        #{confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=LONGVARBINARY},
      </if>
      <if test="errorInfo != null">
        #{errorInfo,jdbcType=LONGVARBINARY},
      </if>
      <if test="extra != null">
        #{extra,jdbcType=LONGVARBINARY},
      </if>
      <if test="config != null">
        #{config,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskExample" resultType="java.lang.Long">
    select count(*) from tb_source_bill_input_task
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_source_bill_input_task
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.sourceBillType != null">
        source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceBillStartDate != null">
        source_bill_start_date = #{record.sourceBillStartDate,jdbcType=DATE},
      </if>
      <if test="record.sourceBillEndDate != null">
        source_bill_end_date = #{record.sourceBillEndDate,jdbcType=DATE},
      </if>
      <if test="record.taskStatus != null">
        task_status = #{record.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="record.fileUrl != null">
        file_url = #{record.fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.createBy != null">
        create_by = #{record.createBy,jdbcType=VARCHAR},
      </if>
      <if test="record.confirmBy != null">
        confirm_by = #{record.confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateAt != null">
        update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.errorInfo != null">
        error_info = #{record.errorInfo,jdbcType=LONGVARBINARY},
      </if>
      <if test="record.extra != null">
        extra = #{record.extra,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tb_source_bill_input_task
    set id = #{record.id,jdbcType=INTEGER},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      source_bill_start_date = #{record.sourceBillStartDate,jdbcType=DATE},
      source_bill_end_date = #{record.sourceBillEndDate,jdbcType=DATE},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      confirm_by = #{record.confirmBy,jdbcType=VARCHAR},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=LONGVARBINARY},
      error_info = #{record.errorInfo,jdbcType=LONGVARBINARY},
      extra = #{record.extra,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_source_bill_input_task
    set id = #{record.id,jdbcType=INTEGER},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      source_bill_start_date = #{record.sourceBillStartDate,jdbcType=DATE},
      source_bill_end_date = #{record.sourceBillEndDate,jdbcType=DATE},
      task_status = #{record.taskStatus,jdbcType=INTEGER},
      file_url = #{record.fileUrl,jdbcType=VARCHAR},
      create_by = #{record.createBy,jdbcType=VARCHAR},
      confirm_by = #{record.confirmBy,jdbcType=VARCHAR},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs">
    update tb_source_bill_input_task
    <set>
      <if test="sourceBillType != null">
        source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="sourceBillStartDate != null">
        source_bill_start_date = #{sourceBillStartDate,jdbcType=DATE},
      </if>
      <if test="sourceBillEndDate != null">
        source_bill_end_date = #{sourceBillEndDate,jdbcType=DATE},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="fileUrl != null">
        file_url = #{fileUrl,jdbcType=VARCHAR},
      </if>
      <if test="createBy != null">
        create_by = #{createBy,jdbcType=VARCHAR},
      </if>
      <if test="confirmBy != null">
        confirm_by = #{confirmBy,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=LONGVARBINARY},
      </if>
      <if test="errorInfo != null">
        error_info = #{errorInfo,jdbcType=LONGVARBINARY},
      </if>
      <if test="extra != null">
        extra = #{extra,jdbcType=LONGVARBINARY},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKeyWithBLOBs" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs">
    update tb_source_bill_input_task
    set source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      source_bill_start_date = #{sourceBillStartDate,jdbcType=DATE},
      source_bill_end_date = #{sourceBillEndDate,jdbcType=DATE},
      task_status = #{taskStatus,jdbcType=INTEGER},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      confirm_by = #{confirmBy,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=LONGVARBINARY},
      error_info = #{errorInfo,jdbcType=LONGVARBINARY},
      extra = #{extra,jdbcType=LONGVARBINARY}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTask">
    update tb_source_bill_input_task
    set source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      source_bill_start_date = #{sourceBillStartDate,jdbcType=DATE},
      source_bill_end_date = #{sourceBillEndDate,jdbcType=DATE},
      task_status = #{taskStatus,jdbcType=INTEGER},
      file_url = #{fileUrl,jdbcType=VARCHAR},
      create_by = #{createBy,jdbcType=VARCHAR},
      confirm_by = #{confirmBy,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>