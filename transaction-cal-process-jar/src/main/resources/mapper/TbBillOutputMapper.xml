<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbBillOutputMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="source_bill_input_task_id" jdbcType="INTEGER" property="sourceBillInputTaskId" />
    <result column="trade_date_month" jdbcType="DATE" property="tradeDateMonth" />
    <result column="source_bill_type" jdbcType="INTEGER" property="sourceBillType" />
    <result column="sub_num" jdbcType="INTEGER" property="subNum" />
    <result column="source_merchant_id" jdbcType="VARCHAR" property="sourceMerchantId" />
    <result column="source_merchant_name" jdbcType="VARCHAR" property="sourceMerchantName" />
    <result column="source_level2_merchant_id" jdbcType="VARCHAR" property="sourceLevel2MerchantId" />
    <result column="source_level2_merchant_name" jdbcType="VARCHAR" property="sourceLevel2MerchantName" />
    <result column="sqb_merchant_id" jdbcType="VARCHAR" property="sqbMerchantId" />
    <result column="sqb_merchant_sn" jdbcType="VARCHAR" property="sqbMerchantSn" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="source_valid_trade_num" jdbcType="INTEGER" property="sourceValidTradeNum" />
    <result column="source_valid_trade_amount" jdbcType="BIGINT" property="sourceValidTradeAmount" />
    <result column="source_valid_refund_num" jdbcType="INTEGER" property="sourceValidRefundNum" />
    <result column="source_valid_refund_amount" jdbcType="BIGINT" property="sourceValidRefundAmount" />
    <result column="source_valid_trade_amount_ratio" jdbcType="CHAR" property="sourceValidTradeAmountRatio" />
    <result column="source_settlement_basis_type" jdbcType="VARCHAR" property="sourceSettlementBasisType" />
    <result column="source_settlement_basis" jdbcType="BIGINT" property="sourceSettlementBasis" />
    <result column="source_merchant_fee_rate" jdbcType="VARCHAR" property="sourceMerchantFeeRate" />
    <result column="source_settlement_fee_rate" jdbcType="VARCHAR" property="sourceSettlementFeeRate" />
    <result column="source_settlement_amount" jdbcType="BIGINT" property="sourceSettlementAmount" />
    <result column="sqb_trade_num" jdbcType="INTEGER" property="sqbTradeNum" />
    <result column="sqb_trade_amount" jdbcType="BIGINT" property="sqbTradeAmount" />
    <result column="sqb_refund_num" jdbcType="INTEGER" property="sqbRefundNum" />
    <result column="sqb_refund_amount" jdbcType="BIGINT" property="sqbRefundAmount" />
    <result column="sqb_clearing_num" jdbcType="INTEGER" property="sqbClearingNum" />
    <result column="sqb_clearing_amount" jdbcType="BIGINT" property="sqbClearingAmount" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_import" jdbcType="BIT" property="isImport" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="capping_rate" jdbcType="VARCHAR" property="cappingRate" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, source_bill_input_task_id, trade_date_month, source_bill_type, sub_num, source_merchant_id,
    source_merchant_name, source_level2_merchant_id, source_level2_merchant_name, sqb_merchant_id, 
    sqb_merchant_sn, dimension, source_valid_trade_num, source_valid_trade_amount, source_valid_refund_num, 
    source_valid_refund_amount, source_valid_trade_amount_ratio, source_settlement_basis_type, 
    source_settlement_basis, source_merchant_fee_rate, source_settlement_fee_rate, source_settlement_amount, 
    sqb_trade_num, sqb_trade_amount, sqb_refund_num, sqb_refund_amount, sqb_clearing_num, 
    sqb_clearing_amount, remark, is_import, create_at, update_at, capping_rate,service_provider_id
  </sql>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_bill_output
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from tb_bill_output
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_bill_output
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputExample">
    delete from tb_bill_output
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    limit 1000
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    insert into tb_bill_output (id, source_bill_input_task_id, trade_date_month,
      source_bill_type, sub_num, source_merchant_id,
      source_merchant_name, source_level2_merchant_id,
      source_level2_merchant_name, sqb_merchant_id,
      sqb_merchant_sn, dimension, source_valid_trade_num,
      source_valid_trade_amount, source_valid_refund_num,
      source_valid_refund_amount, source_valid_trade_amount_ratio,
      source_settlement_basis_type, source_settlement_basis,
      source_merchant_fee_rate, source_settlement_fee_rate,
      source_settlement_amount, sqb_trade_num, sqb_trade_amount,
      sqb_refund_num, sqb_refund_amount, sqb_clearing_num,
      sqb_clearing_amount, remark, is_import,
      create_at, update_at, capping_rate
      )
    values (#{id,jdbcType=BIGINT}, #{sourceBillInputTaskId,jdbcType=INTEGER}, #{tradeDateMonth,jdbcType=DATE},
      #{sourceBillType,jdbcType=INTEGER}, #{subNum,jdbcType=INTEGER}, #{sourceMerchantId,jdbcType=VARCHAR},
      #{sourceMerchantName,jdbcType=VARCHAR}, #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      #{sourceLevel2MerchantName,jdbcType=VARCHAR}, #{sqbMerchantId,jdbcType=VARCHAR},
      #{sqbMerchantSn,jdbcType=VARCHAR}, #{dimension,jdbcType=VARCHAR}, #{sourceValidTradeNum,jdbcType=INTEGER},
      #{sourceValidTradeAmount,jdbcType=BIGINT}, #{sourceValidRefundNum,jdbcType=INTEGER},
      #{sourceValidRefundAmount,jdbcType=BIGINT}, #{sourceValidTradeAmountRatio,jdbcType=CHAR},
      #{sourceSettlementBasisType,jdbcType=VARCHAR}, #{sourceSettlementBasis,jdbcType=BIGINT},
      #{sourceMerchantFeeRate,jdbcType=VARCHAR}, #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      #{sourceSettlementAmount,jdbcType=BIGINT}, #{sqbTradeNum,jdbcType=INTEGER}, #{sqbTradeAmount,jdbcType=BIGINT},
      #{sqbRefundNum,jdbcType=INTEGER}, #{sqbRefundAmount,jdbcType=BIGINT}, #{sqbClearingNum,jdbcType=INTEGER},
      #{sqbClearingAmount,jdbcType=BIGINT}, #{remark,jdbcType=VARCHAR}, #{isImport,jdbcType=BIT},
      #{createAt,jdbcType=TIMESTAMP}, #{updateAt,jdbcType=TIMESTAMP}, #{cappingRate,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    insert into tb_bill_output
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="sourceBillInputTaskId != null">
        source_bill_input_task_id,
      </if>
      <if test="tradeDateMonth != null">
        trade_date_month,
      </if>
      <if test="sourceBillType != null">
        source_bill_type,
      </if>
      <if test="subNum != null">
        sub_num,
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id,
      </if>
      <if test="sourceMerchantName != null">
        source_merchant_name,
      </if>
      <if test="sourceLevel2MerchantId != null">
        source_level2_merchant_id,
      </if>
      <if test="sourceLevel2MerchantName != null">
        source_level2_merchant_name,
      </if>
      <if test="sqbMerchantId != null">
        sqb_merchant_id,
      </if>
      <if test="sqbMerchantSn != null">
        sqb_merchant_sn,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num,
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount,
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num,
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount,
      </if>
      <if test="sourceValidTradeAmountRatio != null">
        source_valid_trade_amount_ratio,
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type,
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis,
      </if>
      <if test="sourceMerchantFeeRate != null">
        source_merchant_fee_rate,
      </if>
      <if test="sourceSettlementFeeRate != null">
        source_settlement_fee_rate,
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount,
      </if>
      <if test="sqbTradeNum != null">
        sqb_trade_num,
      </if>
      <if test="sqbTradeAmount != null">
        sqb_trade_amount,
      </if>
      <if test="sqbRefundNum != null">
        sqb_refund_num,
      </if>
      <if test="sqbRefundAmount != null">
        sqb_refund_amount,
      </if>
      <if test="sqbClearingNum != null">
        sqb_clearing_num,
      </if>
      <if test="sqbClearingAmount != null">
        sqb_clearing_amount,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isImport != null">
        is_import,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="cappingRate != null">
        capping_rate,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="sourceBillInputTaskId != null">
        #{sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="tradeDateMonth != null">
        #{tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="subNum != null">
        #{subNum,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceMerchantName != null">
        #{sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantId != null">
        #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantName != null">
        #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantId != null">
        #{sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantSn != null">
        #{sqbMerchantSn,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="sourceValidTradeNum != null">
        #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidTradeAmountRatio != null">
        #{sourceValidTradeAmountRatio,jdbcType=CHAR},
      </if>
      <if test="sourceSettlementBasisType != null">
        #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantFeeRate != null">
        #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementFeeRate != null">
        #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementAmount != null">
        #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbTradeNum != null">
        #{sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sqbTradeAmount != null">
        #{sqbTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbRefundNum != null">
        #{sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sqbRefundAmount != null">
        #{sqbRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbClearingNum != null">
        #{sqbClearingNum,jdbcType=INTEGER},
      </if>
      <if test="sqbClearingAmount != null">
        #{sqbClearingAmount,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isImport != null">
        #{isImport,jdbcType=BIT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="cappingRate != null">
        #{cappingRate,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <insert id="batchInsert">
    insert into tb_bill_output
    (source_bill_input_task_id, trade_date_month, source_bill_type, sub_num,
    source_merchant_id, source_merchant_name, source_level2_merchant_id, source_level2_merchant_name, sqb_merchant_id, sqb_merchant_sn,
    dimension,
    source_valid_trade_num, source_valid_trade_amount, source_valid_refund_num, source_valid_refund_amount,
    source_valid_trade_amount_ratio,
    source_settlement_basis_type, source_settlement_basis, source_merchant_fee_rate, source_settlement_fee_rate,source_settlement_amount,
    sqb_trade_num, sqb_trade_amount, sqb_refund_num, sqb_refund_amount, sqb_clearing_num, sqb_clearing_amount,
    remark, extra_params, capping_rate,service_provider_id)
    values
    <foreach collection="list" index="index" item="item" open="" close="" separator=",">
      (
      #{item.sourceBillInputTaskId}, #{item.tradeDateMonth}, #{item.sourceBillType}, #{item.subNum},
      #{item.sourceMerchantId}, #{item.sourceMerchantName}, #{item.sourceLevel2MerchantId}, #{item.sourceLevel2MerchantName}, #{item.sqbMerchantId}, #{item.sqbMerchantSn},
      #{item.dimension},
      #{item.sourceValidTradeNum}, #{item.sourceValidTradeAmount}, #{item.sourceValidRefundNum}, #{item.sourceValidRefundAmount},
      #{item.sourceValidTradeAmountRatio},
      #{item.sourceSettlementBasisType}, #{item.sourceSettlementBasis}, #{item.sourceMerchantFeeRate}, #{item.sourceSettlementFeeRate}, #{item.sourceSettlementAmount},
      #{item.sqbTradeNum}, #{item.sqbTradeAmount}, #{item.sqbRefundNum}, #{item.sqbRefundAmount}, #{item.sqbClearingNum}, #{item.sqbClearingAmount},
      #{item.remark}, #{item.extraParams, jdbcType=OTHER, typeHandler=com.wosai.upay.transaction.cal.process.mapper.dao.JsonBlobTypeHandler},#{item.cappingRate},#{item.serviceProviderId}
      )
    </foreach>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputExample" resultType="java.lang.Long">
    select count(*) from tb_bill_output
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="statisticalTradeTotal" resultType="com.wosai.upay.transaction.cal.process.model.dto.TradeInfoDto">
    SELECT
      -- 上游账单交易信息
      source_valid_payment_num_total, -- 账单有效支付汇总笔数
      source_valid_refund_num_total, -- 账单有效支付退款汇总笔数
      -- 账单有效交易汇总笔数 = 账单有效支付汇总笔数 - 账单有效支付退款汇总笔数
      source_valid_payment_num_total - source_valid_refund_num_total AS 'source_valid_trade_num_total',

      source_valid_payment_amount_total, -- 账单有效支付汇总金额
      source_valid_refund_amount_total, -- 账单有效支付退款汇总金额
      -- 账单有效交易汇总金额 = 账单有效支付汇总金额 - 账单有效支付退款汇总金额
      source_valid_payment_amount_total - source_valid_refund_amount_total AS 'source_valid_trade_amount_total',
      source_settlement_basis_total, -- 结算依据汇总金额,
      source_settlement_amount_total, -- 账单结算总金额

      -- 该账期内对应收钱吧交易信息
      sqb_payment_num_total, -- 收钱吧有效支付汇总笔数
      sqb_refund_num_total, -- 收钱吧有效支付退款汇总笔数
      -- 收钱吧有效交易汇总笔数 = 收钱吧有效支付汇总笔数 - 收钱吧有效支付退款汇总笔数
      sqb_payment_num_total - sqb_refund_num_total AS 'sqb_trade_num_total',
      sqb_payment_amount_total, -- 收钱吧有效支付汇总金额
      sqb_refund_amount_total, -- 收钱吧有效支付退款汇总金额
      -- 收钱吧有效交易汇总金额 = 收钱吧有效支付汇总金额 - 收钱吧有效支付退款汇总金额
      sqb_payment_amount_total - sqb_refund_amount_total AS 'sqb_trade_amount_total',

      -- 差额显示（差额大于0为上游多，反之为上游少）
      -- 上游账单与收钱吧统计笔数差
      (source_valid_payment_num_total - source_valid_refund_num_total) - (sqb_payment_num_total - sqb_refund_num_total) AS 'valid_trade_num_total_balance',
      -- 上游账单与收钱吧统计金额差
      source_settlement_basis_total - (sqb_payment_amount_total - sqb_refund_amount_total) AS 'valid_trade_amount_total_balance'
    FROM (
           SELECT
             SUM(source_valid_trade_num) AS 'source_valid_payment_num_total',
             SUM(source_valid_trade_amount) AS 'source_valid_payment_amount_total',
             SUM(source_valid_refund_num) AS 'source_valid_refund_num_total',
             SUM(source_valid_refund_amount) AS 'source_valid_refund_amount_total',
             SUM(source_settlement_basis) AS 'source_settlement_basis_total',
             SUM(source_settlement_amount) AS 'source_settlement_amount_total',

             SUM(sqb_trade_num) AS 'sqb_payment_num_total',
             SUM(sqb_trade_amount) AS 'sqb_payment_amount_total',
             SUM(sqb_refund_num) AS 'sqb_refund_num_total',
             SUM(sqb_refund_amount) AS 'sqb_refund_amount_total'
           FROM tb_bill_output
           WHERE source_bill_input_task_id = #{taskId}
         ) AS statistical_trade_total_table
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_bill_output
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.sourceBillInputTaskId != null">
        source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="record.tradeDateMonth != null">
        trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="record.sourceBillType != null">
        source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="record.subNum != null">
        sub_num = #{record.subNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceMerchantId != null">
        source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceMerchantName != null">
        source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLevel2MerchantId != null">
        source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLevel2MerchantName != null">
        source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.sqbMerchantId != null">
        sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sqbMerchantSn != null">
        sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR},
      </if>
      <if test="record.dimension != null">
        dimension = #{record.dimension,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceValidTradeNum != null">
        source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidTradeAmount != null">
        source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceValidRefundNum != null">
        source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidRefundAmount != null">
        source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceValidTradeAmountRatio != null">
        source_valid_trade_amount_ratio = #{record.sourceValidTradeAmountRatio,jdbcType=CHAR},
      </if>
      <if test="record.sourceSettlementBasisType != null">
        source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementBasis != null">
        source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="record.sourceMerchantFeeRate != null">
        source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementFeeRate != null">
        source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementAmount != null">
        source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sqbTradeNum != null">
        sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbTradeAmount != null">
        sqb_trade_amount = #{record.sqbTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sqbRefundNum != null">
        sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbRefundAmount != null">
        sqb_refund_amount = #{record.sqbRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sqbClearingNum != null">
        sqb_clearing_num = #{record.sqbClearingNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbClearingAmount != null">
        sqb_clearing_amount = #{record.sqbClearingAmount,jdbcType=BIGINT},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isImport != null">
        is_import = #{record.isImport,jdbcType=BIT},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateAt != null">
        update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cappingRate != null">
        capping_rate = #{record.cappingRate,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_bill_output
    set id = #{record.id,jdbcType=BIGINT},
      source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
      trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      sub_num = #{record.subNum,jdbcType=INTEGER},
      source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
      source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
      source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
      sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
      sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR},
      dimension = #{record.dimension,jdbcType=VARCHAR},
      source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      source_valid_trade_amount_ratio = #{record.sourceValidTradeAmountRatio,jdbcType=CHAR},
      source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
      source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
      source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
      sqb_trade_amount = #{record.sqbTradeAmount,jdbcType=BIGINT},
      sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
      sqb_refund_amount = #{record.sqbRefundAmount,jdbcType=BIGINT},
      sqb_clearing_num = #{record.sqbClearingNum,jdbcType=INTEGER},
      sqb_clearing_amount = #{record.sqbClearingAmount,jdbcType=BIGINT},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_import = #{record.isImport,jdbcType=BIT},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      capping_rate = #{record.cappingRate,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    update tb_bill_output
    <set>
      <if test="sourceBillInputTaskId != null">
        source_bill_input_task_id = #{sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="tradeDateMonth != null">
        trade_date_month = #{tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="subNum != null">
        sub_num = #{subNum,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceMerchantName != null">
        source_merchant_name = #{sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantId != null">
        source_level2_merchant_id = #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantName != null">
        source_level2_merchant_name = #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantId != null">
        sqb_merchant_id = #{sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantSn != null">
        sqb_merchant_sn = #{sqbMerchantSn,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidTradeAmountRatio != null">
        source_valid_trade_amount_ratio = #{sourceValidTradeAmountRatio,jdbcType=CHAR},
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type = #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis = #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantFeeRate != null">
        source_merchant_fee_rate = #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementFeeRate != null">
        source_settlement_fee_rate = #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbTradeNum != null">
        sqb_trade_num = #{sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sqbTradeAmount != null">
        sqb_trade_amount = #{sqbTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbRefundNum != null">
        sqb_refund_num = #{sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sqbRefundAmount != null">
        sqb_refund_amount = #{sqbRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbClearingNum != null">
        sqb_clearing_num = #{sqbClearingNum,jdbcType=INTEGER},
      </if>
      <if test="sqbClearingAmount != null">
        sqb_clearing_amount = #{sqbClearingAmount,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isImport != null">
        is_import = #{isImport,jdbcType=BIT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="cappingRate != null">
        capping_rate = #{cappingRate,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    update tb_bill_output
    set source_bill_input_task_id = #{sourceBillInputTaskId,jdbcType=INTEGER},
      trade_date_month = #{tradeDateMonth,jdbcType=DATE},
      source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      sub_num = #{subNum,jdbcType=INTEGER},
      source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      source_merchant_name = #{sourceMerchantName,jdbcType=VARCHAR},
      source_level2_merchant_id = #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      source_level2_merchant_name = #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      sqb_merchant_id = #{sqbMerchantId,jdbcType=VARCHAR},
      sqb_merchant_sn = #{sqbMerchantSn,jdbcType=VARCHAR},
      dimension = #{dimension,jdbcType=VARCHAR},
      source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
      source_valid_trade_amount_ratio = #{sourceValidTradeAmountRatio,jdbcType=CHAR},
      source_settlement_basis_type = #{sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{sourceSettlementBasis,jdbcType=BIGINT},
      source_merchant_fee_rate = #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      source_settlement_fee_rate = #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
      sqb_trade_num = #{sqbTradeNum,jdbcType=INTEGER},
      sqb_trade_amount = #{sqbTradeAmount,jdbcType=BIGINT},
      sqb_refund_num = #{sqbRefundNum,jdbcType=INTEGER},
      sqb_refund_amount = #{sqbRefundAmount,jdbcType=BIGINT},
      sqb_clearing_num = #{sqbClearingNum,jdbcType=INTEGER},
      sqb_clearing_amount = #{sqbClearingAmount,jdbcType=BIGINT},
      remark = #{remark,jdbcType=VARCHAR},
      is_import = #{isImport,jdbcType=BIT},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      update_at = #{updateAt,jdbcType=TIMESTAMP},
      capping_rate = #{cappingRate,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <select id="findBillOutputList" resultType="com.wosai.upay.transaction.cal.process.model.dto.BillOutput" timeout="30">
    select
    o.id,
    o.source_bill_input_task_id,
    o.trade_date_month,
    o.source_merchant_id,
    o.sqb_merchant_id,
    o.sqb_merchant_sn,
    o.source_merchant_name,
    o.source_level2_merchant_id,
    o.source_level2_merchant_name,
    o.dimension,
    o.source_bill_type,
    o.source_valid_trade_num as 'source_trade_num',
    o.source_valid_trade_amount as 'source_trade_amount',
    o.source_valid_refund_num as 'source_refund_num',
    o.source_valid_refund_amount as 'source_refund_amount',
    o.source_settlement_basis_type,
    o.source_settlement_basis,
    o.source_merchant_fee_rate,
    o.source_settlement_fee_rate,
    o.source_settlement_amount,
    o.remark,
    o.create_at,
    o.update_at,
    o.service_provider_id,
    (case when tsbit.task_status = 5 then 1 else 0 end) as 'is_import',
    o.sqb_trade_num,
    o.sqb_trade_amount,
    o.sqb_refund_num,
    o.sqb_refund_amount,
    o.sqb_clearing_num,
    o.sqb_clearing_amount,
    o.source_valid_trade_amount_ratio,
    t.name as 'source_bill_type_name',
    t.source_bill_classify,
    t.source_bill_classify_name,
    t.source_bill_company,
    t.source_bill_company_name
    from tb_bill_output as o
    <if test="lastId != null">
      force index(`idx_source_bill_type_month`)
    </if>
    left join tb_source_bill_type as t on o.source_bill_type = t.id
    left join tb_source_bill_input_task tsbit on o.source_bill_input_task_id = tsbit.id
    where 1 = 1
    <if test="billType != null ">
      and o.source_bill_type = #{billType}
    </if>
    <if test="billSourceCompany != null and billSourceCompany != ''">
      and t.source_bill_company = #{billSourceCompany}
    </if>
    <if test="billSourceClassify != null ">
      and t.source_bill_classify = #{billSourceClassify}
    </if>
    <if test="startDate != null">
      and o.trade_date_month >= #{startDate}
    </if>
    <if test="endDate != null">
      and o.trade_date_month &lt;= #{endDate}
    </if>
    <if test="sourceMerchantId != null and sourceMerchantId != ''">
      and o.source_merchant_id = #{sourceMerchantId}
    </if>
    <if test="sourceMerchantName != null and sourceMerchantName != ''">
      and o.source_merchant_name like #{sourceMerchantName}"%"
    </if>
    <if test="sqbMerchantSn != null and sqbMerchantSn != ''">
      and o.sqb_merchant_sn = #{sqbMerchantSn}
    </if>
    <if test="serviceProviderId != null and serviceProviderId != ''">
      and o.service_provider_id = #{serviceProviderId}
    </if>
    <if test="lastId != null">
      and o.id &lt; #{lastId}
    </if>
  </select>
</mapper>