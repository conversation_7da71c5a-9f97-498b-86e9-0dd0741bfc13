<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.StatementPlanMapper">


    <select id="selectByPrimaryKey" parameterType="java.lang.Long"
            resultType="com.wosai.upay.transaction.cal.process.model.domain.StatementPlan">
    select *
    from statement_plan
    where id = #{id,jdbcType=BIGINT} and  delete_at is null
  </select>

    <select id="listPlan" resultType="com.wosai.upay.transaction.cal.process.model.domain.StatementPlan">
        select *
        from statement_plan
        <where>
            <if test="id!=null">
                and id=#{id}
            </if>
            <if test="name!=null">
                and name like concat('%',#{name},'%')
            </if>
            <if test="agreementType!=null">
                AND `agreement_type` =#{agreementType}
            </if>

            <if test="1==1">
                AND delete_at is null
            </if>
            order by create_at desc
        </where>
    </select>


    <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.StatementPlan">
        insert into statement_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="name != null">
                name,
            </if>
            <if test="email != null">
                email,
            </if>

            <if test="agreementType != null">
                agreement_type,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createAt != null">
                create_at,
            </if>
            <if test="updateAt != null">
                update_at,
            </if>
            <if test="deleteAt != null">
                delete_at,
            </if>
            <if test="agreementConfig != null">
                agreement_config,
            </if>
            <if test="statementConfig != null">
                statement_config,
            </if>
            <if test="comment != null">
                comment,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="email != null">
                #{email,jdbcType=VARCHAR},
            </if>
            <if test="agreementType != null">
                #{agreementType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=INTEGER},
            </if>
            <if test="createBy != null">
                #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createAt != null">
                #{createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updateAt != null">
                #{updateAt,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteAt != null">
                #{deleteAt,jdbcType=TIMESTAMP},
            </if>
            <if test="agreementConfig != null">
                #{agreementConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="statementConfig != null">
                #{statementConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="comment != null">
                #{comment,jdbcType=LONGVARCHAR},
            </if>
        </trim>
    </insert>

    <update id="updateByPrimaryKeySelective"
            parameterType="com.wosai.upay.transaction.cal.process.model.domain.StatementPlan">
        update statement_plan
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="agreementType != null">
                agreement_type = #{agreementType,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=INTEGER},
            </if>
            <if test="email != null">
                email = #{email,jdbcType=VARCHAR},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="createAt != null">
                create_at = #{createAt,jdbcType=TIMESTAMP},
            </if>
            <if test="updateAt != null">
                update_at = #{updateAt,jdbcType=TIMESTAMP},
            </if>
            <if test="deleteAt != null">
                delete_at = #{deleteAt,jdbcType=TIMESTAMP},
            </if>
            <if test="agreementConfig != null">
                agreement_config = #{agreementConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="statementConfig != null">
                statement_config = #{statementConfig,jdbcType=LONGVARCHAR},
            </if>
            <if test="comment != null">
                comment = #{comment,jdbcType=LONGVARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>