<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.StatementTaskMapper">
    <select id="selectTaskList" resultType="com.wosai.upay.transaction.cal.process.model.dto.StatementTaskDto">
        select st.create_at as create_time,st.plan_id,st.id as task_id, sp.name as plan_name, st.type as merchant_type,
        st.isv_code,st.p_sn as
        group_sn, st.merchant_ids,st.status,st.create_by from
        statement_task as st
        inner join statement_plan as sp on st.plan_id = sp.id
        where st.delete_at is null
        and sp.delete_at is null
        <if test="taskId != null">
            and st.id = #{taskId}
        </if>

        <if test="merchantType != null">
            and st.type = #{merchantType}
        </if>

        <if test="groupSn != null and groupSn != '' ">
            and st.p_sn = #{groupSn}
        </if>

        <if test="merchantId != null and merchantId != ''">
            and st.merchant_ids like "%"#{merchantId}"%"
        </if>

        <if test="planId != null">
            and st.plan_id = #{planId}
        </if>

        <if test="planName != null and planName != ''">
            and sp.name like #{planName}"%"
        </if>

        <if test="isvCode != null and isvCode != ''">
            and st.isv_code like #{isvCode}"%"
        </if>

        order by st.id desc
    </select>

    <select id="selectAllAliveTask"
            resultType="com.wosai.upay.transaction.cal.process.model.domain.StatementTask">
        select *
        from statement_task as st
                     left join statement_plan as sp on st.plan_id = sp.id
        where st.delete_at is null
          and sp.delete_at is null
          and st.status = 1
          and sp.status = 1
    </select>

    <select id="selectAllAliveTaskByType"
            resultType="com.wosai.upay.transaction.cal.process.model.domain.StatementTask">
        select *
        from statement_task as st
                     left join statement_plan as sp on st.plan_id = sp.id
        where st.delete_at is null
          and sp.delete_at is null
          and st.status = 1
          and sp.status = 1
          <if test="type != null">
              and st.type = #{type}
          </if>
    </select>
</mapper>