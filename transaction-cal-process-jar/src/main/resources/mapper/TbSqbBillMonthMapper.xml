<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbSqbBillMonthMapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="trade_month" jdbcType="CHAR" property="tradeMonth" />
    <result column="bill_type" jdbcType="INTEGER" property="billType" />
    <result column="source_merchant_id" jdbcType="VARCHAR" property="sourceMerchantId" />
    <result column="sqb_merchant_id" jdbcType="VARCHAR" property="sqbMerchantId" />
    <result column="sqb_trade_num" jdbcType="INTEGER" property="sqbTradeNum" />
    <result column="sqb_trade_money" jdbcType="BIGINT" property="sqbTradeMoney" />
    <result column="sqb_refund_num" jdbcType="INTEGER" property="sqbRefundNum" />
    <result column="sqb_refund_money" jdbcType="BIGINT" property="sqbRefundMoney" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="sqb_merchant_sn" jdbcType="VARCHAR" property="sqbMerchantSn" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, trade_month, bill_type, source_merchant_id, sqb_merchant_id, sqb_trade_num, sqb_trade_money, 
    sqb_refund_num, sqb_refund_money, dimension, create_at, sqb_merchant_sn
  </sql>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonthExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_sqb_bill_month
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from tb_sqb_bill_month
    where id = #{id,jdbcType=INTEGER}
  </select>
  <select id="listSqbBillMonth" resultType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth">
    SELECT *
    FROM tb_sqb_bill_month
    WHERE bill_type = #{billType} AND trade_month = #{billMonth}
      AND sqb_merchant_sn IS NOT NULL AND sqb_merchant_sn != ''
    <if test='offset != null and size != null'>
      LIMIT #{offset}, #{size}
    </if>
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from tb_sqb_bill_month
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonthExample">
    delete from tb_sqb_bill_month
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth">
    insert into tb_sqb_bill_month (id, trade_month, bill_type, 
      source_merchant_id, sqb_merchant_id, sqb_trade_num, 
      sqb_trade_money, sqb_refund_num, sqb_refund_money, 
      dimension, create_at, sqb_merchant_sn
      )
    values (#{id,jdbcType=INTEGER}, #{tradeMonth,jdbcType=CHAR}, #{billType,jdbcType=INTEGER}, 
      #{sourceMerchantId,jdbcType=VARCHAR}, #{sqbMerchantId,jdbcType=VARCHAR}, #{sqbTradeNum,jdbcType=INTEGER}, 
      #{sqbTradeMoney,jdbcType=BIGINT}, #{sqbRefundNum,jdbcType=INTEGER}, #{sqbRefundMoney,jdbcType=BIGINT}, 
      #{dimension,jdbcType=VARCHAR}, #{createAt,jdbcType=TIMESTAMP}, #{sqbMerchantSn,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth">
    insert into tb_sqb_bill_month
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="tradeMonth != null">
        trade_month,
      </if>
      <if test="billType != null">
        bill_type,
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id,
      </if>
      <if test="sqbMerchantId != null">
        sqb_merchant_id,
      </if>
      <if test="sqbTradeNum != null">
        sqb_trade_num,
      </if>
      <if test="sqbTradeMoney != null">
        sqb_trade_money,
      </if>
      <if test="sqbRefundNum != null">
        sqb_refund_num,
      </if>
      <if test="sqbRefundMoney != null">
        sqb_refund_money,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="sqbMerchantSn != null">
        sqb_merchant_sn,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=INTEGER},
      </if>
      <if test="tradeMonth != null">
        #{tradeMonth,jdbcType=CHAR},
      </if>
      <if test="billType != null">
        #{billType,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantId != null">
        #{sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbTradeNum != null">
        #{sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sqbTradeMoney != null">
        #{sqbTradeMoney,jdbcType=BIGINT},
      </if>
      <if test="sqbRefundNum != null">
        #{sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sqbRefundMoney != null">
        #{sqbRefundMoney,jdbcType=BIGINT},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="sqbMerchantSn != null">
        #{sqbMerchantSn,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonthExample" resultType="java.lang.Long">
    select count(*) from tb_sqb_bill_month
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_sqb_bill_month
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.tradeMonth != null">
        trade_month = #{record.tradeMonth,jdbcType=CHAR},
      </if>
      <if test="record.billType != null">
        bill_type = #{record.billType,jdbcType=INTEGER},
      </if>
      <if test="record.sourceMerchantId != null">
        source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sqbMerchantId != null">
        sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sqbTradeNum != null">
        sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbTradeMoney != null">
        sqb_trade_money = #{record.sqbTradeMoney,jdbcType=BIGINT},
      </if>
      <if test="record.sqbRefundNum != null">
        sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbRefundMoney != null">
        sqb_refund_money = #{record.sqbRefundMoney,jdbcType=BIGINT},
      </if>
      <if test="record.dimension != null">
        dimension = #{record.dimension,jdbcType=VARCHAR},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.sqbMerchantSn != null">
        sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_sqb_bill_month
    set id = #{record.id,jdbcType=INTEGER},
      trade_month = #{record.tradeMonth,jdbcType=CHAR},
      bill_type = #{record.billType,jdbcType=INTEGER},
      source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
      sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
      sqb_trade_money = #{record.sqbTradeMoney,jdbcType=BIGINT},
      sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
      sqb_refund_money = #{record.sqbRefundMoney,jdbcType=BIGINT},
      dimension = #{record.dimension,jdbcType=VARCHAR},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth">
    update tb_sqb_bill_month
    <set>
      <if test="tradeMonth != null">
        trade_month = #{tradeMonth,jdbcType=CHAR},
      </if>
      <if test="billType != null">
        bill_type = #{billType,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantId != null">
        sqb_merchant_id = #{sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbTradeNum != null">
        sqb_trade_num = #{sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sqbTradeMoney != null">
        sqb_trade_money = #{sqbTradeMoney,jdbcType=BIGINT},
      </if>
      <if test="sqbRefundNum != null">
        sqb_refund_num = #{sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sqbRefundMoney != null">
        sqb_refund_money = #{sqbRefundMoney,jdbcType=BIGINT},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="sqbMerchantSn != null">
        sqb_merchant_sn = #{sqbMerchantSn,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth">
    update tb_sqb_bill_month
    set trade_month = #{tradeMonth,jdbcType=CHAR},
      bill_type = #{billType,jdbcType=INTEGER},
      source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      sqb_merchant_id = #{sqbMerchantId,jdbcType=VARCHAR},
      sqb_trade_num = #{sqbTradeNum,jdbcType=INTEGER},
      sqb_trade_money = #{sqbTradeMoney,jdbcType=BIGINT},
      sqb_refund_num = #{sqbRefundNum,jdbcType=INTEGER},
      sqb_refund_money = #{sqbRefundMoney,jdbcType=BIGINT},
      dimension = #{dimension,jdbcType=VARCHAR},
      create_at = #{createAt,jdbcType=TIMESTAMP},
      sqb_merchant_sn = #{sqbMerchantSn,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>