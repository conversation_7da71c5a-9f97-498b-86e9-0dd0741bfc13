<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wosai.upay.transaction.cal.process.mapper.TbBillOutputV2Mapper">
  <resultMap id="BaseResultMap" type="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="summary_bill_sn" jdbcType="VARCHAR" property="summaryBillSn" />
    <result column="source_bill_input_task_id" jdbcType="INTEGER" property="sourceBillInputTaskId" />
    <result column="trade_date_month" jdbcType="DATE" property="tradeDateMonth" />
    <result column="source_bill_type" jdbcType="INTEGER" property="sourceBillType" />
    <result column="sub_num" jdbcType="INTEGER" property="subNum" />
    <result column="source_merchant_id" jdbcType="VARCHAR" property="sourceMerchantId" />
    <result column="source_merchant_name" jdbcType="VARCHAR" property="sourceMerchantName" />
    <result column="source_level2_merchant_id" jdbcType="VARCHAR" property="sourceLevel2MerchantId" />
    <result column="source_level2_merchant_name" jdbcType="VARCHAR" property="sourceLevel2MerchantName" />
    <result column="sqb_merchant_id" jdbcType="VARCHAR" property="sqbMerchantId" />
    <result column="sqb_merchant_sn" jdbcType="VARCHAR" property="sqbMerchantSn" />
    <result column="dimension" jdbcType="VARCHAR" property="dimension" />
    <result column="source_valid_trade_num" jdbcType="INTEGER" property="sourceValidTradeNum" />
    <result column="source_valid_trade_amount" jdbcType="BIGINT" property="sourceValidTradeAmount" />
    <result column="source_valid_refund_num" jdbcType="INTEGER" property="sourceValidRefundNum" />
    <result column="source_valid_refund_amount" jdbcType="BIGINT" property="sourceValidRefundAmount" />
    <result column="source_valid_trade_amount_ratio" jdbcType="CHAR" property="sourceValidTradeAmountRatio" />
    <result column="source_settlement_basis_type" jdbcType="VARCHAR" property="sourceSettlementBasisType" />
    <result column="source_settlement_basis" jdbcType="BIGINT" property="sourceSettlementBasis" />
    <result column="source_merchant_fee_rate" jdbcType="VARCHAR" property="sourceMerchantFeeRate" />
    <result column="source_settlement_fee_rate" jdbcType="VARCHAR" property="sourceSettlementFeeRate" />
    <result column="source_settlement_amount" jdbcType="BIGINT" property="sourceSettlementAmount" />
    <result column="sqb_trade_num" jdbcType="INTEGER" property="sqbTradeNum" />
    <result column="sqb_trade_amount" jdbcType="BIGINT" property="sqbTradeAmount" />
    <result column="sqb_refund_num" jdbcType="INTEGER" property="sqbRefundNum" />
    <result column="sqb_refund_amount" jdbcType="BIGINT" property="sqbRefundAmount" />
    <result column="sqb_clearing_num" jdbcType="INTEGER" property="sqbClearingNum" />
    <result column="sqb_clearing_amount" jdbcType="BIGINT" property="sqbClearingAmount" />
    <result column="merchant_commission" jdbcType="BIGINT" property="merchantCommission" />
    <result column="push_status" jdbcType="VARCHAR" property="pushStatus" />
    <result column="channel_cost" jdbcType="BIGINT" property="channelCost" />
    <result column="source_cost" jdbcType="BIGINT" property="sourceCost" />
    <result column="service_provider_id" jdbcType="VARCHAR" property="serviceProviderId" />
    <result column="source" jdbcType="VARCHAR" property="source" />
    <result column="not_settlement_reason" jdbcType="VARCHAR" property="notSettlementReason" />
    <result column="device_no" jdbcType="VARCHAR" property="deviceNo" />
    <result column="import_task_sn" jdbcType="VARCHAR" property="importTaskSn" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="is_import" jdbcType="BIT" property="isImport" />
    <result column="create_at" jdbcType="TIMESTAMP" property="createAt" />
    <result column="update_at" jdbcType="TIMESTAMP" property="updateAt" />
    <result column="capping_rate" jdbcType="VARCHAR" property="cappingRate" />
  </resultMap>
  <resultMap extends="BaseResultMap" id="ResultMapWithBLOBs" type="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2">
    <result column="extra_params" jdbcType="LONGVARBINARY" property="extraParams" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, summary_bill_sn, source_bill_input_task_id, trade_date_month, source_bill_type, 
    sub_num, source_merchant_id, source_merchant_name, source_level2_merchant_id, source_level2_merchant_name, 
    sqb_merchant_id, sqb_merchant_sn, dimension, source_valid_trade_num, source_valid_trade_amount, 
    source_valid_refund_num, source_valid_refund_amount, source_valid_trade_amount_ratio, 
    source_settlement_basis_type, source_settlement_basis, source_merchant_fee_rate, 
    source_settlement_fee_rate, source_settlement_amount, sqb_trade_num, sqb_trade_amount, 
    sqb_refund_num, sqb_refund_amount, sqb_clearing_num, sqb_clearing_amount, merchant_commission, 
    push_status, channel_cost, source_cost, service_provider_id, source, not_settlement_reason, 
    device_no, import_task_sn, remark, is_import, create_at, update_at, capping_rate
  </sql>
  <sql id="Blob_Column_List">
    extra_params
  </sql>
  <select id="selectByExampleWithBLOBs" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2Example" resultMap="ResultMapWithBLOBs">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_bill_output_v2
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2Example" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from tb_bill_output_v2
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <delete id="deleteByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2Example">
    delete from tb_bill_output_v2
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2">
    insert into tb_bill_output_v2 (id, summary_bill_sn, source_bill_input_task_id, 
      trade_date_month, source_bill_type, sub_num, 
      source_merchant_id, source_merchant_name, source_level2_merchant_id, 
      source_level2_merchant_name, sqb_merchant_id, 
      sqb_merchant_sn, dimension, source_valid_trade_num, 
      source_valid_trade_amount, source_valid_refund_num, 
      source_valid_refund_amount, source_valid_trade_amount_ratio, 
      source_settlement_basis_type, source_settlement_basis, 
      source_merchant_fee_rate, source_settlement_fee_rate, 
      source_settlement_amount, sqb_trade_num, sqb_trade_amount, 
      sqb_refund_num, sqb_refund_amount, sqb_clearing_num, 
      sqb_clearing_amount, merchant_commission, push_status, 
      channel_cost, source_cost, service_provider_id, 
      source, not_settlement_reason, device_no, 
      import_task_sn, remark, is_import, 
      create_at, update_at, capping_rate, 
      extra_params)
    values (#{id,jdbcType=BIGINT}, #{summaryBillSn,jdbcType=VARCHAR}, #{sourceBillInputTaskId,jdbcType=INTEGER}, 
      #{tradeDateMonth,jdbcType=DATE}, #{sourceBillType,jdbcType=INTEGER}, #{subNum,jdbcType=INTEGER}, 
      #{sourceMerchantId,jdbcType=VARCHAR}, #{sourceMerchantName,jdbcType=VARCHAR}, #{sourceLevel2MerchantId,jdbcType=VARCHAR}, 
      #{sourceLevel2MerchantName,jdbcType=VARCHAR}, #{sqbMerchantId,jdbcType=VARCHAR}, 
      #{sqbMerchantSn,jdbcType=VARCHAR}, #{dimension,jdbcType=VARCHAR}, #{sourceValidTradeNum,jdbcType=INTEGER}, 
      #{sourceValidTradeAmount,jdbcType=BIGINT}, #{sourceValidRefundNum,jdbcType=INTEGER}, 
      #{sourceValidRefundAmount,jdbcType=BIGINT}, #{sourceValidTradeAmountRatio,jdbcType=CHAR}, 
      #{sourceSettlementBasisType,jdbcType=VARCHAR}, #{sourceSettlementBasis,jdbcType=BIGINT}, 
      #{sourceMerchantFeeRate,jdbcType=VARCHAR}, #{sourceSettlementFeeRate,jdbcType=VARCHAR}, 
      #{sourceSettlementAmount,jdbcType=BIGINT}, #{sqbTradeNum,jdbcType=INTEGER}, #{sqbTradeAmount,jdbcType=BIGINT}, 
      #{sqbRefundNum,jdbcType=INTEGER}, #{sqbRefundAmount,jdbcType=BIGINT}, #{sqbClearingNum,jdbcType=INTEGER}, 
      #{sqbClearingAmount,jdbcType=BIGINT}, #{merchantCommission,jdbcType=BIGINT}, #{pushStatus,jdbcType=VARCHAR}, 
      #{channelCost,jdbcType=BIGINT}, #{sourceCost,jdbcType=BIGINT}, #{serviceProviderId,jdbcType=VARCHAR}, 
      #{source,jdbcType=VARCHAR}, #{notSettlementReason,jdbcType=VARCHAR}, #{deviceNo,jdbcType=VARCHAR}, 
      #{importTaskSn,jdbcType=VARCHAR}, #{remark,jdbcType=VARCHAR}, #{isImport,jdbcType=BIT}, 
      #{createAt,jdbcType=TIMESTAMP}, #{updateAt,jdbcType=TIMESTAMP}, #{cappingRate,jdbcType=VARCHAR}, 
      #{extraParams,jdbcType=LONGVARBINARY})
  </insert>
  <insert id="insertSelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2">
    insert into tb_bill_output_v2
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="summaryBillSn != null">
        summary_bill_sn,
      </if>
      <if test="sourceBillInputTaskId != null">
        source_bill_input_task_id,
      </if>
      <if test="tradeDateMonth != null">
        trade_date_month,
      </if>
      <if test="sourceBillType != null">
        source_bill_type,
      </if>
      <if test="subNum != null">
        sub_num,
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id,
      </if>
      <if test="sourceMerchantName != null">
        source_merchant_name,
      </if>
      <if test="sourceLevel2MerchantId != null">
        source_level2_merchant_id,
      </if>
      <if test="sourceLevel2MerchantName != null">
        source_level2_merchant_name,
      </if>
      <if test="sqbMerchantId != null">
        sqb_merchant_id,
      </if>
      <if test="sqbMerchantSn != null">
        sqb_merchant_sn,
      </if>
      <if test="dimension != null">
        dimension,
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num,
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount,
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num,
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount,
      </if>
      <if test="sourceValidTradeAmountRatio != null">
        source_valid_trade_amount_ratio,
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type,
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis,
      </if>
      <if test="sourceMerchantFeeRate != null">
        source_merchant_fee_rate,
      </if>
      <if test="sourceSettlementFeeRate != null">
        source_settlement_fee_rate,
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount,
      </if>
      <if test="sqbTradeNum != null">
        sqb_trade_num,
      </if>
      <if test="sqbTradeAmount != null">
        sqb_trade_amount,
      </if>
      <if test="sqbRefundNum != null">
        sqb_refund_num,
      </if>
      <if test="sqbRefundAmount != null">
        sqb_refund_amount,
      </if>
      <if test="sqbClearingNum != null">
        sqb_clearing_num,
      </if>
      <if test="sqbClearingAmount != null">
        sqb_clearing_amount,
      </if>
      <if test="merchantCommission != null">
        merchant_commission,
      </if>
      <if test="pushStatus != null">
        push_status,
      </if>
      <if test="channelCost != null">
        channel_cost,
      </if>
      <if test="sourceCost != null">
        source_cost,
      </if>
      <if test="serviceProviderId != null">
        service_provider_id,
      </if>
      <if test="source != null">
        source,
      </if>
      <if test="notSettlementReason != null">
        not_settlement_reason,
      </if>
      <if test="deviceNo != null">
        device_no,
      </if>
      <if test="importTaskSn != null">
        import_task_sn,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="isImport != null">
        is_import,
      </if>
      <if test="createAt != null">
        create_at,
      </if>
      <if test="updateAt != null">
        update_at,
      </if>
      <if test="cappingRate != null">
        capping_rate,
      </if>
      <if test="extraParams != null">
        extra_params,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="summaryBillSn != null">
        #{summaryBillSn,jdbcType=VARCHAR},
      </if>
      <if test="sourceBillInputTaskId != null">
        #{sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="tradeDateMonth != null">
        #{tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="subNum != null">
        #{subNum,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceMerchantName != null">
        #{sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantId != null">
        #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantName != null">
        #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantId != null">
        #{sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantSn != null">
        #{sqbMerchantSn,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="sourceValidTradeNum != null">
        #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidTradeAmountRatio != null">
        #{sourceValidTradeAmountRatio,jdbcType=CHAR},
      </if>
      <if test="sourceSettlementBasisType != null">
        #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantFeeRate != null">
        #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementFeeRate != null">
        #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementAmount != null">
        #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbTradeNum != null">
        #{sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sqbTradeAmount != null">
        #{sqbTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbRefundNum != null">
        #{sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sqbRefundAmount != null">
        #{sqbRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbClearingNum != null">
        #{sqbClearingNum,jdbcType=INTEGER},
      </if>
      <if test="sqbClearingAmount != null">
        #{sqbClearingAmount,jdbcType=BIGINT},
      </if>
      <if test="merchantCommission != null">
        #{merchantCommission,jdbcType=BIGINT},
      </if>
      <if test="pushStatus != null">
        #{pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="channelCost != null">
        #{channelCost,jdbcType=BIGINT},
      </if>
      <if test="sourceCost != null">
        #{sourceCost,jdbcType=BIGINT},
      </if>
      <if test="serviceProviderId != null">
        #{serviceProviderId,jdbcType=VARCHAR},
      </if>
      <if test="source != null">
        #{source,jdbcType=VARCHAR},
      </if>
      <if test="notSettlementReason != null">
        #{notSettlementReason,jdbcType=VARCHAR},
      </if>
      <if test="deviceNo != null">
        #{deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="importTaskSn != null">
        #{importTaskSn,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isImport != null">
        #{isImport,jdbcType=BIT},
      </if>
      <if test="createAt != null">
        #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="cappingRate != null">
        #{cappingRate,jdbcType=VARCHAR},
      </if>
      <if test="extraParams != null">
        #{extraParams,jdbcType=LONGVARBINARY},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2Example" resultType="java.lang.Long">
    select count(*) from tb_bill_output_v2
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update tb_bill_output_v2
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.summaryBillSn != null">
        summary_bill_sn = #{record.summaryBillSn,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceBillInputTaskId != null">
        source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="record.tradeDateMonth != null">
        trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="record.sourceBillType != null">
        source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="record.subNum != null">
        sub_num = #{record.subNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceMerchantId != null">
        source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceMerchantName != null">
        source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLevel2MerchantId != null">
        source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceLevel2MerchantName != null">
        source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="record.sqbMerchantId != null">
        sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="record.sqbMerchantSn != null">
        sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR},
      </if>
      <if test="record.dimension != null">
        dimension = #{record.dimension,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceValidTradeNum != null">
        source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidTradeAmount != null">
        source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceValidRefundNum != null">
        source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sourceValidRefundAmount != null">
        source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sourceValidTradeAmountRatio != null">
        source_valid_trade_amount_ratio = #{record.sourceValidTradeAmountRatio,jdbcType=CHAR},
      </if>
      <if test="record.sourceSettlementBasisType != null">
        source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementBasis != null">
        source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="record.sourceMerchantFeeRate != null">
        source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementFeeRate != null">
        source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="record.sourceSettlementAmount != null">
        source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sqbTradeNum != null">
        sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbTradeAmount != null">
        sqb_trade_amount = #{record.sqbTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sqbRefundNum != null">
        sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbRefundAmount != null">
        sqb_refund_amount = #{record.sqbRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="record.sqbClearingNum != null">
        sqb_clearing_num = #{record.sqbClearingNum,jdbcType=INTEGER},
      </if>
      <if test="record.sqbClearingAmount != null">
        sqb_clearing_amount = #{record.sqbClearingAmount,jdbcType=BIGINT},
      </if>
      <if test="record.merchantCommission != null">
        merchant_commission = #{record.merchantCommission,jdbcType=BIGINT},
      </if>
      <if test="record.pushStatus != null">
        push_status = #{record.pushStatus,jdbcType=VARCHAR},
      </if>
      <if test="record.channelCost != null">
        channel_cost = #{record.channelCost,jdbcType=BIGINT},
      </if>
      <if test="record.sourceCost != null">
        source_cost = #{record.sourceCost,jdbcType=BIGINT},
      </if>
      <if test="record.serviceProviderId != null">
        service_provider_id = #{record.serviceProviderId,jdbcType=VARCHAR},
      </if>
      <if test="record.source != null">
        source = #{record.source,jdbcType=VARCHAR},
      </if>
      <if test="record.notSettlementReason != null">
        not_settlement_reason = #{record.notSettlementReason,jdbcType=VARCHAR},
      </if>
      <if test="record.deviceNo != null">
        device_no = #{record.deviceNo,jdbcType=VARCHAR},
      </if>
      <if test="record.importTaskSn != null">
        import_task_sn = #{record.importTaskSn,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.isImport != null">
        is_import = #{record.isImport,jdbcType=BIT},
      </if>
      <if test="record.createAt != null">
        create_at = #{record.createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.updateAt != null">
        update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="record.cappingRate != null">
        capping_rate = #{record.cappingRate,jdbcType=VARCHAR},
      </if>
      <if test="record.extraParams != null">
        extra_params = #{record.extraParams,jdbcType=LONGVARBINARY},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExampleWithBLOBs" parameterType="map">
    update tb_bill_output_v2
    set id = #{record.id,jdbcType=BIGINT},
      summary_bill_sn = #{record.summaryBillSn,jdbcType=VARCHAR},
      source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
      trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
      source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
      sub_num = #{record.subNum,jdbcType=INTEGER},
      source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
      source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
      source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
      source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
      sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
      sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR},
      dimension = #{record.dimension,jdbcType=VARCHAR},
      source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
      source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
      source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
      source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
      source_valid_trade_amount_ratio = #{record.sourceValidTradeAmountRatio,jdbcType=CHAR},
      source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
      source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
      source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
      source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
      source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
      sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
      sqb_trade_amount = #{record.sqbTradeAmount,jdbcType=BIGINT},
      sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
      sqb_refund_amount = #{record.sqbRefundAmount,jdbcType=BIGINT},
      sqb_clearing_num = #{record.sqbClearingNum,jdbcType=INTEGER},
      sqb_clearing_amount = #{record.sqbClearingAmount,jdbcType=BIGINT},
      merchant_commission = #{record.merchantCommission,jdbcType=BIGINT},
      push_status = #{record.pushStatus,jdbcType=VARCHAR},
      channel_cost = #{record.channelCost,jdbcType=BIGINT},
      source_cost = #{record.sourceCost,jdbcType=BIGINT},
      service_provider_id = #{record.serviceProviderId,jdbcType=VARCHAR},
      source = #{record.source,jdbcType=VARCHAR},
      not_settlement_reason = #{record.notSettlementReason,jdbcType=VARCHAR},
      device_no = #{record.deviceNo,jdbcType=VARCHAR},
      import_task_sn = #{record.importTaskSn,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      is_import = #{record.isImport,jdbcType=BIT},
      create_at = #{record.createAt,jdbcType=TIMESTAMP},
      update_at = #{record.updateAt,jdbcType=TIMESTAMP},
      capping_rate = #{record.cappingRate,jdbcType=VARCHAR},
      extra_params = #{record.extraParams,jdbcType=LONGVARBINARY}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update tb_bill_output
    set id = #{record.id,jdbcType=BIGINT},
    summary_bill_sn = #{record.summaryBillSn,jdbcType=VARCHAR},
    source_bill_input_task_id = #{record.sourceBillInputTaskId,jdbcType=INTEGER},
    trade_date_month = #{record.tradeDateMonth,jdbcType=DATE},
    source_bill_type = #{record.sourceBillType,jdbcType=INTEGER},
    sub_num = #{record.subNum,jdbcType=INTEGER},
    source_merchant_id = #{record.sourceMerchantId,jdbcType=VARCHAR},
    source_merchant_name = #{record.sourceMerchantName,jdbcType=VARCHAR},
    source_level2_merchant_id = #{record.sourceLevel2MerchantId,jdbcType=VARCHAR},
    source_level2_merchant_name = #{record.sourceLevel2MerchantName,jdbcType=VARCHAR},
    sqb_merchant_id = #{record.sqbMerchantId,jdbcType=VARCHAR},
    sqb_merchant_sn = #{record.sqbMerchantSn,jdbcType=VARCHAR},
    dimension = #{record.dimension,jdbcType=VARCHAR},
    source_valid_trade_num = #{record.sourceValidTradeNum,jdbcType=INTEGER},
    source_valid_trade_amount = #{record.sourceValidTradeAmount,jdbcType=BIGINT},
    source_valid_refund_num = #{record.sourceValidRefundNum,jdbcType=INTEGER},
    source_valid_refund_amount = #{record.sourceValidRefundAmount,jdbcType=BIGINT},
    source_valid_trade_amount_ratio = #{record.sourceValidTradeAmountRatio,jdbcType=CHAR},
    source_settlement_basis_type = #{record.sourceSettlementBasisType,jdbcType=VARCHAR},
    source_settlement_basis = #{record.sourceSettlementBasis,jdbcType=BIGINT},
    source_merchant_fee_rate = #{record.sourceMerchantFeeRate,jdbcType=VARCHAR},
    source_settlement_fee_rate = #{record.sourceSettlementFeeRate,jdbcType=VARCHAR},
    source_settlement_amount = #{record.sourceSettlementAmount,jdbcType=BIGINT},
    sqb_trade_num = #{record.sqbTradeNum,jdbcType=INTEGER},
    sqb_trade_amount = #{record.sqbTradeAmount,jdbcType=BIGINT},
    sqb_refund_num = #{record.sqbRefundNum,jdbcType=INTEGER},
    sqb_refund_amount = #{record.sqbRefundAmount,jdbcType=BIGINT},
    sqb_clearing_num = #{record.sqbClearingNum,jdbcType=INTEGER},
    sqb_clearing_amount = #{record.sqbClearingAmount,jdbcType=BIGINT},
    merchant_commission = #{record.merchantCommission,jdbcType=BIGINT},
    push_status = #{record.pushStatus,jdbcType=VARCHAR},
    channel_cost = #{record.channelCost,jdbcType=BIGINT},
    source_cost = #{record.sourceCost,jdbcType=BIGINT},
    service_provider_id = #{record.serviceProviderId,jdbcType=VARCHAR},
    source = #{record.source,jdbcType=VARCHAR},
    not_settlement_reason = #{record.notSettlementReason,jdbcType=VARCHAR},
    device_no = #{record.deviceNo,jdbcType=VARCHAR},
    import_task_sn = #{record.importTaskSn,jdbcType=VARCHAR},
    remark = #{record.remark,jdbcType=VARCHAR},
    is_import = #{record.isImport,jdbcType=BIT},
    create_at = #{record.createAt,jdbcType=TIMESTAMP},
    update_at = #{record.updateAt,jdbcType=TIMESTAMP},
    capping_rate = #{record.cappingRate,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>

  <!--  custom_sql-->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from tb_bill_output_v2
    where id = #{id,jdbcType=BIGINT}
  </delete>

  <insert id="batchInsert">
    insert into tb_bill_output_v2
    (id, summary_bill_sn, source_bill_input_task_id,
    trade_date_month, source_bill_type, sub_num,
    source_merchant_id, source_merchant_name, source_level2_merchant_id,
    source_level2_merchant_name, sqb_merchant_id,
    sqb_merchant_sn, dimension, source_valid_trade_num,
    source_valid_trade_amount, source_valid_refund_num,
    source_valid_refund_amount, source_valid_trade_amount_ratio,
    source_settlement_basis_type, source_settlement_basis,
    source_merchant_fee_rate, source_settlement_fee_rate,
    source_settlement_amount, sqb_trade_num, sqb_trade_amount,
    sqb_refund_num, sqb_refund_amount, sqb_clearing_num,
    sqb_clearing_amount, merchant_commission, channel_cost, source_cost,
    service_provider_id, source, not_settlement_reason,
    device_no, import_task_sn, remark,
    is_import, create_at, update_at,
    capping_rate, extra_params)
    values
    <foreach close="" collection="list" index="index" item="item" open="" separator=",">
      (#{item.id,jdbcType=BIGINT}, #{item.summaryBillSn,jdbcType=VARCHAR}, #{item.sourceBillInputTaskId,jdbcType=INTEGER},
      #{item.tradeDateMonth,jdbcType=DATE}, #{item.sourceBillType,jdbcType=INTEGER}, #{item.subNum,jdbcType=INTEGER},
      #{item.sourceMerchantId,jdbcType=VARCHAR}, #{item.sourceMerchantName,jdbcType=VARCHAR}, #{item.sourceLevel2MerchantId,jdbcType=VARCHAR},
      #{item.sourceLevel2MerchantName,jdbcType=VARCHAR}, #{item.sqbMerchantId,jdbcType=VARCHAR},
      #{item.sqbMerchantSn,jdbcType=VARCHAR}, #{item.dimension,jdbcType=VARCHAR}, #{item.sourceValidTradeNum,jdbcType=INTEGER},
      #{item.sourceValidTradeAmount,jdbcType=BIGINT}, #{item.sourceValidRefundNum,jdbcType=INTEGER},
      #{item.sourceValidRefundAmount,jdbcType=BIGINT}, #{item.sourceValidTradeAmountRatio,jdbcType=CHAR},
      #{item.sourceSettlementBasisType,jdbcType=VARCHAR}, #{item.sourceSettlementBasis,jdbcType=BIGINT},
      #{item.sourceMerchantFeeRate,jdbcType=VARCHAR}, #{item.sourceSettlementFeeRate,jdbcType=VARCHAR},
      #{item.sourceSettlementAmount,jdbcType=BIGINT}, #{item.sqbTradeNum,jdbcType=INTEGER}, #{item.sqbTradeAmount,jdbcType=BIGINT},
      #{item.sqbRefundNum,jdbcType=INTEGER}, #{item.sqbRefundAmount,jdbcType=BIGINT}, #{item.sqbClearingNum,jdbcType=INTEGER},
      #{item.sqbClearingAmount,jdbcType=BIGINT}, #{item.merchantCommission,jdbcType=BIGINT}, #{item.channelCost,jdbcType=BIGINT}, #{item.sourceCost,jdbcType=BIGINT},
      #{item.serviceProviderId,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR}, #{item.notSettlementReason,jdbcType=VARCHAR},
      #{item.deviceNo,jdbcType=VARCHAR}, #{item.importTaskSn,jdbcType=VARCHAR}, #{item.remark,jdbcType=VARCHAR},
      #{item.isImport,jdbcType=BIT}, #{item.createAt,jdbcType=TIMESTAMP}, #{item.updateAt,jdbcType=TIMESTAMP},
      #{item.cappingRate,jdbcType=VARCHAR}, #{item.extraParams,jdbcType=LONGVARBINARY})
    </foreach>
  </insert>

  <select id="statisticalTradeTotal" resultType="com.wosai.upay.transaction.cal.process.model.dto.TradeInfoDto">
    SELECT
    -- 上游账单交易信息
    source_valid_payment_num_total, -- 账单有效支付汇总笔数
    source_valid_refund_num_total, -- 账单有效支付退款汇总笔数
    -- 账单有效交易汇总笔数 = 账单有效支付汇总笔数 - 账单有效支付退款汇总笔数
    source_valid_payment_num_total - source_valid_refund_num_total AS 'source_valid_trade_num_total',

    source_valid_payment_amount_total, -- 账单有效支付汇总金额
    source_valid_refund_amount_total, -- 账单有效支付退款汇总金额
    -- 账单有效交易汇总金额 = 账单有效支付汇总金额 - 账单有效支付退款汇总金额
    source_valid_payment_amount_total - source_valid_refund_amount_total AS 'source_valid_trade_amount_total',
    source_settlement_basis_total, -- 结算依据汇总金额,
    source_settlement_amount_total, -- 账单结算总金额

    -- 该账期内对应收钱吧交易信息
    sqb_payment_num_total, -- 收钱吧有效支付汇总笔数
    sqb_refund_num_total, -- 收钱吧有效支付退款汇总笔数
    -- 收钱吧有效交易汇总笔数 = 收钱吧有效支付汇总笔数 - 收钱吧有效支付退款汇总笔数
    sqb_payment_num_total - sqb_refund_num_total AS 'sqb_trade_num_total',
    sqb_payment_amount_total, -- 收钱吧有效支付汇总金额
    sqb_refund_amount_total, -- 收钱吧有效支付退款汇总金额
    -- 收钱吧有效交易汇总金额 = 收钱吧有效支付汇总金额 - 收钱吧有效支付退款汇总金额
    sqb_payment_amount_total - sqb_refund_amount_total AS 'sqb_trade_amount_total',

    -- 差额显示（差额大于0为上游多，反之为上游少）
    -- 上游账单与收钱吧统计笔数差
    (source_valid_payment_num_total - source_valid_refund_num_total) - (sqb_payment_num_total - sqb_refund_num_total) AS 'valid_trade_num_total_balance',
    -- 上游账单与收钱吧统计金额差
    source_settlement_basis_total - (sqb_payment_amount_total - sqb_refund_amount_total) AS 'valid_trade_amount_total_balance'
    FROM (
    SELECT
    SUM(source_valid_trade_num) AS 'source_valid_payment_num_total',
    SUM(source_valid_trade_amount) AS 'source_valid_payment_amount_total',
    SUM(source_valid_refund_num) AS 'source_valid_refund_num_total',
    SUM(source_valid_refund_amount) AS 'source_valid_refund_amount_total',
    SUM(source_settlement_basis) AS 'source_settlement_basis_total',
    SUM(source_settlement_amount) AS 'source_settlement_amount_total',

    SUM(sqb_trade_num) AS 'sqb_payment_num_total',
    SUM(sqb_trade_amount) AS 'sqb_payment_amount_total',
    SUM(sqb_refund_num) AS 'sqb_refund_num_total',
    SUM(sqb_refund_amount) AS 'sqb_refund_amount_total'
    FROM tb_bill_output_v2
    WHERE source_bill_input_task_id = #{taskId}
    ) AS statistical_trade_total_table
  </select>

  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    ,
    <include refid="Blob_Column_List" />
    from tb_bill_output_v2
    where id = #{id,jdbcType=BIGINT}
  </select>

  <update id="updateByPrimaryKeySelective" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    update tb_bill_output_v2
    <set>
      <if test="sourceBillInputTaskId != null">
        source_bill_input_task_id = #{sourceBillInputTaskId,jdbcType=INTEGER},
      </if>
      <if test="tradeDateMonth != null">
        trade_date_month = #{tradeDateMonth,jdbcType=DATE},
      </if>
      <if test="sourceBillType != null">
        source_bill_type = #{sourceBillType,jdbcType=INTEGER},
      </if>
      <if test="subNum != null">
        sub_num = #{subNum,jdbcType=INTEGER},
      </if>
      <if test="sourceMerchantId != null">
        source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceMerchantName != null">
        source_merchant_name = #{sourceMerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantId != null">
        source_level2_merchant_id = #{sourceLevel2MerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sourceLevel2MerchantName != null">
        source_level2_merchant_name = #{sourceLevel2MerchantName,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantId != null">
        sqb_merchant_id = #{sqbMerchantId,jdbcType=VARCHAR},
      </if>
      <if test="sqbMerchantSn != null">
        sqb_merchant_sn = #{sqbMerchantSn,jdbcType=VARCHAR},
      </if>
      <if test="dimension != null">
        dimension = #{dimension,jdbcType=VARCHAR},
      </if>
      <if test="sourceValidTradeNum != null">
        source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidTradeAmount != null">
        source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidRefundNum != null">
        source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sourceValidRefundAmount != null">
        source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sourceValidTradeAmountRatio != null">
        source_valid_trade_amount_ratio = #{sourceValidTradeAmountRatio,jdbcType=CHAR},
      </if>
      <if test="sourceSettlementBasisType != null">
        source_settlement_basis_type = #{sourceSettlementBasisType,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementBasis != null">
        source_settlement_basis = #{sourceSettlementBasis,jdbcType=BIGINT},
      </if>
      <if test="sourceMerchantFeeRate != null">
        source_merchant_fee_rate = #{sourceMerchantFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementFeeRate != null">
        source_settlement_fee_rate = #{sourceSettlementFeeRate,jdbcType=VARCHAR},
      </if>
      <if test="sourceSettlementAmount != null">
        source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbTradeNum != null">
        sqb_trade_num = #{sqbTradeNum,jdbcType=INTEGER},
      </if>
      <if test="sqbTradeAmount != null">
        sqb_trade_amount = #{sqbTradeAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbRefundNum != null">
        sqb_refund_num = #{sqbRefundNum,jdbcType=INTEGER},
      </if>
      <if test="sqbRefundAmount != null">
        sqb_refund_amount = #{sqbRefundAmount,jdbcType=BIGINT},
      </if>
      <if test="sqbClearingNum != null">
        sqb_clearing_num = #{sqbClearingNum,jdbcType=INTEGER},
      </if>
      <if test="sqbClearingAmount != null">
        sqb_clearing_amount = #{sqbClearingAmount,jdbcType=BIGINT},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="isImport != null">
        is_import = #{isImport,jdbcType=BIT},
      </if>
      <if test="createAt != null">
        create_at = #{createAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateAt != null">
        update_at = #{updateAt,jdbcType=TIMESTAMP},
      </if>
      <if test="cappingRate != null">
        capping_rate = #{cappingRate,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>

  <update id="updateByPrimaryKey" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    update tb_bill_output_v2
    set source_bill_input_task_id = #{sourceBillInputTaskId,jdbcType=INTEGER},
    trade_date_month = #{tradeDateMonth,jdbcType=DATE},
    source_bill_type = #{sourceBillType,jdbcType=INTEGER},
    sub_num = #{subNum,jdbcType=INTEGER},
    source_merchant_id = #{sourceMerchantId,jdbcType=VARCHAR},
    source_merchant_name = #{sourceMerchantName,jdbcType=VARCHAR},
    source_level2_merchant_id = #{sourceLevel2MerchantId,jdbcType=VARCHAR},
    source_level2_merchant_name = #{sourceLevel2MerchantName,jdbcType=VARCHAR},
    sqb_merchant_id = #{sqbMerchantId,jdbcType=VARCHAR},
    sqb_merchant_sn = #{sqbMerchantSn,jdbcType=VARCHAR},
    dimension = #{dimension,jdbcType=VARCHAR},
    source_valid_trade_num = #{sourceValidTradeNum,jdbcType=INTEGER},
    source_valid_trade_amount = #{sourceValidTradeAmount,jdbcType=BIGINT},
    source_valid_refund_num = #{sourceValidRefundNum,jdbcType=INTEGER},
    source_valid_refund_amount = #{sourceValidRefundAmount,jdbcType=BIGINT},
    source_valid_trade_amount_ratio = #{sourceValidTradeAmountRatio,jdbcType=CHAR},
    source_settlement_basis_type = #{sourceSettlementBasisType,jdbcType=VARCHAR},
    source_settlement_basis = #{sourceSettlementBasis,jdbcType=BIGINT},
    source_merchant_fee_rate = #{sourceMerchantFeeRate,jdbcType=VARCHAR},
    source_settlement_fee_rate = #{sourceSettlementFeeRate,jdbcType=VARCHAR},
    source_settlement_amount = #{sourceSettlementAmount,jdbcType=BIGINT},
    sqb_trade_num = #{sqbTradeNum,jdbcType=INTEGER},
    sqb_trade_amount = #{sqbTradeAmount,jdbcType=BIGINT},
    sqb_refund_num = #{sqbRefundNum,jdbcType=INTEGER},
    sqb_refund_amount = #{sqbRefundAmount,jdbcType=BIGINT},
    sqb_clearing_num = #{sqbClearingNum,jdbcType=INTEGER},
    sqb_clearing_amount = #{sqbClearingAmount,jdbcType=BIGINT},
    remark = #{remark,jdbcType=VARCHAR},
    is_import = #{isImport,jdbcType=BIT},
    create_at = #{createAt,jdbcType=TIMESTAMP},
    update_at = #{updateAt,jdbcType=TIMESTAMP},
    capping_rate = #{cappingRate,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="findBillOutputList" resultType="com.wosai.upay.transaction.cal.process.model.dto.BillOutput" timeout="30">
    select
    o.id,
    o.source_bill_input_task_id,
    o.trade_date_month,
    o.source_merchant_id,
    o.sqb_merchant_id,
    o.sqb_merchant_sn,
    o.source_merchant_name,
    o.source_level2_merchant_id,
    o.source_level2_merchant_name,
    o.dimension,
    o.source_bill_type,
    o.source_valid_trade_num as 'source_trade_num',
    o.source_valid_trade_amount as 'source_trade_amount',
    o.source_valid_refund_num as 'source_refund_num',
    o.source_valid_refund_amount as 'source_refund_amount',
    o.source_settlement_basis_type,
    o.source_settlement_basis,
    o.source_merchant_fee_rate,
    o.source_settlement_fee_rate,
    o.source_settlement_amount,
    o.remark,
    o.create_at,
    o.update_at,
    o.service_provider_id,
    (case when tsbit.task_status = 5 then 1 else 0 end) as 'is_import',
    o.sqb_trade_num,
    o.sqb_trade_amount,
    o.sqb_refund_num,
    o.sqb_refund_amount,
    o.sqb_clearing_num,
    o.sqb_clearing_amount,
    o.source_valid_trade_amount_ratio,
    t.name as 'source_bill_type_name',
    t.source_bill_classify,
    t.source_bill_classify_name,
    t.source_bill_company,
    t.source_bill_company_name
    from tb_bill_output_v2 as o
    <if test="lastId != null">
      force index(`idx_source_bill_type_month`)
    </if>
    left join tb_source_bill_type as t on o.source_bill_type = t.id
    left join tb_source_bill_input_task tsbit on o.source_bill_input_task_id = tsbit.id
    where 1 = 1
    <if test="billType != null ">
      and o.source_bill_type = #{billType}
    </if>
    <if test="billSourceCompany != null and billSourceCompany != ''">
      and t.source_bill_company = #{billSourceCompany}
    </if>
    <if test="billSourceClassify != null ">
      and t.source_bill_classify = #{billSourceClassify}
    </if>
    <if test="startDate != null">
      and o.trade_date_month &gt;= #{startDate}
    </if>
    <if test="endDate != null">
      and o.trade_date_month &lt;= #{endDate}
    </if>
    <if test="sourceMerchantId != null and sourceMerchantId != ''">
      and o.source_merchant_id = #{sourceMerchantId}
    </if>
    <if test="sourceMerchantName != null and sourceMerchantName != ''">
      and o.source_merchant_name like #{sourceMerchantName}"%"
    </if>
    <if test="sqbMerchantSn != null and sqbMerchantSn != ''">
      and o.sqb_merchant_sn = #{sqbMerchantSn}
    </if>
    <if test="serviceProviderId != null and serviceProviderId != ''">
      and o.service_provider_id = #{serviceProviderId}
    </if>
    <if test="lastId != null">
      and o.id &lt; #{lastId}
    </if>
  </select>

  <insert id="insertOrUpdateBatch" parameterType="com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput">
    insert into tb_bill_output_v2 (id, summary_bill_sn, source_bill_input_task_id,
    trade_date_month, source_bill_type, sub_num,
    source_merchant_id, source_merchant_name, source_level2_merchant_id,
    source_level2_merchant_name, sqb_merchant_id,
    sqb_merchant_sn, dimension, source_valid_trade_num,
    source_valid_trade_amount, source_valid_refund_num,
    source_valid_refund_amount, source_valid_trade_amount_ratio,
    source_settlement_basis_type, source_settlement_basis,
    source_merchant_fee_rate, source_settlement_fee_rate,
    source_settlement_amount, sqb_trade_num, sqb_trade_amount,
    sqb_refund_num, sqb_refund_amount, sqb_clearing_num,
    sqb_clearing_amount, push_status, channel_cost,
    source_cost, service_provider_id, source,
    not_settlement_reason, device_no, import_task_sn,
    remark, is_import, create_at,
    update_at, capping_rate, extra_params
    )
    values
    <foreach close="" collection="list" index="index" item="item" open="" separator=",">
      (
      #{item.id,jdbcType=BIGINT}, #{item.summaryBillSn,jdbcType=VARCHAR}, #{item.sourceBillInputTaskId,jdbcType=INTEGER},
      #{item.tradeDateMonth,jdbcType=DATE}, #{item.sourceBillType,jdbcType=INTEGER}, #{item.subNum,jdbcType=INTEGER},
      #{item.sourceMerchantId,jdbcType=VARCHAR}, #{item.sourceMerchantName,jdbcType=VARCHAR}, #{item.sourceLevel2MerchantId,jdbcType=VARCHAR},
      #{item.sourceLevel2MerchantName,jdbcType=VARCHAR}, #{item.sqbMerchantId,jdbcType=VARCHAR},
      #{item.sqbMerchantSn,jdbcType=VARCHAR}, #{item.dimension,jdbcType=VARCHAR}, #{item.sourceValidTradeNum,jdbcType=INTEGER},
      #{item.sourceValidTradeAmount,jdbcType=BIGINT}, #{item.sourceValidRefundNum,jdbcType=INTEGER},
      #{item.sourceValidRefundAmount,jdbcType=BIGINT}, #{item.sourceValidTradeAmountRatio,jdbcType=CHAR},
      #{item.sourceSettlementBasisType,jdbcType=VARCHAR}, #{item.sourceSettlementBasis,jdbcType=BIGINT},
      #{item.sourceMerchantFeeRate,jdbcType=VARCHAR}, #{item.sourceSettlementFeeRate,jdbcType=VARCHAR},
      #{item.sourceSettlementAmount,jdbcType=BIGINT}, #{item.sqbTradeNum,jdbcType=INTEGER}, #{item.sqbTradeAmount,jdbcType=BIGINT},
      #{item.sqbRefundNum,jdbcType=INTEGER}, #{item.sqbRefundAmount,jdbcType=BIGINT}, #{item.sqbClearingNum,jdbcType=INTEGER},
      #{item.sqbClearingAmount,jdbcType=BIGINT}, #{item.pushStatus,jdbcType=VARCHAR}, #{item.channelCost,jdbcType=BIGINT},
      #{item.sourceCost,jdbcType=BIGINT}, #{item.serviceProviderId,jdbcType=VARCHAR}, #{item.source,jdbcType=VARCHAR},
      #{item.notSettlementReason,jdbcType=VARCHAR}, #{item.deviceNo,jdbcType=VARCHAR}, #{item.importTaskSn,jdbcType=VARCHAR},
      #{item.remark,jdbcType=VARCHAR}, #{item.isImport,jdbcType=BIT}, #{item.createAt,jdbcType=TIMESTAMP},
      #{item.updateAt,jdbcType=TIMESTAMP}, #{item.cappingRate,jdbcType=VARCHAR}, #{item.extraParams,jdbcType=LONGVARBINARY}
      )
    </foreach>
    on duplicate key update
    push_status = values(push_status),
    source_valid_trade_amount = values(source_valid_trade_amount),
    source_valid_refund_num = values(source_valid_refund_num),
    source_valid_refund_amount = values(source_valid_refund_amount),
    source_settlement_basis = values(source_settlement_basis),
    source_settlement_amount = values(source_settlement_amount),
    sqb_trade_num = values(sqb_trade_num),
    sqb_trade_amount = values(sqb_trade_amount),
    sqb_refund_num = values(sqb_refund_num),
    sqb_refund_amount = values(sqb_refund_amount),
    sqb_clearing_num = values(sqb_clearing_num),
    sqb_clearing_amount = values(sqb_clearing_amount)
  </insert>
</mapper>