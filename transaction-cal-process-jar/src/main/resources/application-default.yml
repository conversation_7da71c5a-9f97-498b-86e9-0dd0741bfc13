spring:
  datasource:
    url: tk-transaction-cal-process-upay_transaction_cal-1304
    druid:
      initial-size: 10
      max-active: 30
      min-idle: 10
      max-wait: 3000
      validation-query: select 1
      validation-query-timeout: 30
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  redis:
    host: r-8vbkddg0ez3eak2rzq.redis.zhangbei.rds.aliyuncs.com
    password: roFXzHwXPY3RnI%5
    port: 6379
    database: 1
    timeout: 30000
    pool:
      max-active: 8
      max-wait: -1
      max-idle: 8
      min-idle: 0

jsonrpc:
  core-business-server: http://core-business.beta.iwosai.com
  upay-task-center: http://upay-task-center.beta.iwosai.com
  user-service: http://user-service.beta.iwosai.com
  sales-service: http://sales-service.beta.iwosai.com
  crm: http://sales-system-service.beta.iwosai.com
  signature-proxy-server: http://signature-proxy.beta.iwosai.com
  merchant-contract-job: http://merchant-contract-job.beta.iwosai.com
  sms-gateway: http://sms-gateway.beta.iwosai.com/sms/voice/send
  merchant-user-service: http://merchant-user-service.beta.iwosai.com/
  cornucopia-admin: http://cornucopia-admin.beta.iwosai.com
  aop-gateway-service: http://aop-gateway.beta.iwosai.com
  business-logstash: http://business-logstash.beta.iwosai.com
  upay-transaction: http://upay-transaction.beta.iwosai.com

config:
  aop:
    terminal-merchant-authorize-dev-code: CUMR9RYXB2RW
    terminal-merchant-pending-authorize-template-code: E8IGGWJRMO3L
    terminal-merchant-authorize-success-template-code: BB4Y0JFWULMK


odps:
  accessId: LTAI23c07B5LAdIX
  accessKey: y1RUT0MJcgDVMzsAxgZhEUH03Y40wY
  endPoint: http://service.odps.aliyun.com/api
  projectName: wosai_main

file:
  path:
    bill: /app/log/

oss:
  endpoint:
    url: http://oss-cn-hangzhou.aliyuncs.com

hopeedu:
  billKey: 57be927c227646ea8614f82838a43a9b
  intoNo: 10007
  filePath: app/hopeedu