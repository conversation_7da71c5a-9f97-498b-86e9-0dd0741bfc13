<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<generatorConfiguration>
    <context id="mysqlTables" targetRuntime="MyBatis3">
        <!--去除注释  -->
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>
        <jdbcConnection driverClass="com.mysql.jdbc.Driver"
                        connectionURL="******************************************************************************************************************************************************"
                        userId="p_yangxiayuan" password="ULWve&amp;03kNI*Vct+a$pxCGmb)i28"/>

        <!-- 指定生成的类型为java类型，避免数据库中number等类型字段 -->
        <javaTypeResolver>
            <property name="forceBigDecimals" value="false"/>
        </javaTypeResolver>

        <!-- 生成model模型，对应的包，存放位置可以指定具体的路径,如/ProjectName/src，也可以使用MAVEN来自动生成 -->
        <javaModelGenerator targetProject="src/main/java" targetPackage="com.wosai.upay.transaction.cal.process.model.domain">
            <property name="enableSubPackages" value="true"/>
        </javaModelGenerator>

        <!--对应的xml mapper文件  -->
        <sqlMapGenerator targetProject="src/main/resources" targetPackage="mapper">
            <property name="enableSubPackages" value="true"/>
        </sqlMapGenerator>

        <!-- 对应的dao接口 -->
        <javaClientGenerator type="XMLMAPPER" targetProject="src/main/java"
                             targetPackage="com.wosai.upay.transaction.cal.process.mapper">
            <property name="enableSubPackages" value="false"/>
        </javaClientGenerator>

        <!-- 表映射为实体 -->
<!--        <table tableName="statement_task" domainObjectName="StatementTask">-->
<!--        </table>-->

<!--        <table tableName="statement_plan" domainObjectName="StatementPlan">-->
<!--        </table>-->

<!--        <table tableName="tb_summary_bill" domainObjectName="TbSummaryBill">-->
<!--        <table tableName="tb_source_bill_type" domainObjectName="TbSourceBillType">-->
        <table tableName="tb_bill_output" domainObjectName="TbBillOutput"></table>
<!--        <table tableName="tb_bill_output_v2" domainObjectName="TbBillOutputV2">-->
<!--    </table>-->
<!--        <table tableName="tb_source_bill_v2" domainObjectName="TbSourceBillV2">-->
<!--        </table>-->

    </context>
</generatorConfiguration>