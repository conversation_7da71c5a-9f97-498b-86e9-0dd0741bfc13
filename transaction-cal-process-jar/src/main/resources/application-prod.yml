spring:
  datasource:
    url: pk-transaction-cal-process-upay_transaction_cal-6254?useUnicode=yes&zeroDateTimeBehavior=convertToNull
    druid:
      initial-size: 10
      max-active: 30
      min-idle: 10
      max-wait: 3000
      validation-query: select 1
      validation-query-timeout: 30
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
  redis:
    host: r-bp1dd0926c175fe4.redis.rds.aliyuncs.com
    password: UWME#nYmYikHIjFK
    port: 6379
    database: 1
    timeout: 30000
    pool:
      max-active: 8
      max-wait: -1
      max-idle: 8
      min-idle: 0

jsonrpc:
  core-business-server: http://app-core-business
  upay-task-center: http://upay-task-center
  user-service: http://user-service
  sales-service: http://sales-system-service
  crm: http://sales-system-service
  signature-proxy-server: http://signature-proxy
  merchant-contract-job: http://merchant-contract-job
  sms-gateway: http://sms-gateway/sms/voice/send
  merchant-user-service: http://merchant-user-service
  cornucopia-admin: http://cornucopia-admin
  aop-gateway-service: http://aop-gateway
  business-logstash: http://business-logstash
  upay-transaction: http://upay-transaction-query.internal.shouqianba.com

config:
  aop:
    terminal-merchant-authorize-dev-code: I0UDXL7CVVCT
    terminal-merchant-pending-authorize-template-code: E8IGGWJRMO3L
    terminal-merchant-authorize-success-template-code: BB4Y0JFWULMK

odps:
  accessId: LTAI23c07B5LAdIX
  accessKey: y1RUT0MJcgDVMzsAxgZhEUH03Y40wY
  endPoint: http://service.cn.maxcompute.aliyun-inc.com/api
  projectName: wosai_main

lakala:
  send-data-enable: true
  sftp:
    host: ftpgds.lakala.com.cn
    username: unbrdata
    password: fgDYetR#yg
    port: 22
    path:

file:
  path:
    bill: /app/log/transaction-cal-process-jar/

oss:
  endpoint:
    url: http://oss-cn-hangzhou.aliyuncs.com


hopeedu:
  billKey: 57be927c227646ea8614f82838a43a9b
  intoNo: 10007
  filePath: /app/hopeedu