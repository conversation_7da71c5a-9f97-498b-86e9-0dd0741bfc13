spring:
  profiles:
    active: default
  application:
    name: transaction-cal-process
app:
  name: transaction-cal-process
  group: pay
mybatis:
  mapper-locations: classpath:mapper/*.xml
  config-location: classpath:mybatis-config.xml

mapper:
  style: camelhumpandlowercase

odps:
  accessId: LTAI23c07B5LAdIX
  accessKey: y1RUT0MJcgDVMzsAxgZhEUH03Y40wY
  endPoint: http://service.cn.maxcompute.aliyun-inc.com/api
  projectName: wosai_main

lakala:
  send-data-enable: false
  sftp:
    host: **************
    username: upay
    password: transaction-cal-process
    port: 5837
    path: transaction-cal-process/lakala


lark:
  chatbot:
    url: https://open.feishu.cn/open-apis/bot/v2/hook/1eebb60a-ef83-411b-8b76-2cfe3ff01ccf
    feeInversion: https://open.feishu.cn/open-apis/bot/v2/hook/fb1ed8da-6873-4118-88d9-82c7bca32d24



hopeedu:
  billKey: 57be927c227646ea8614f82838a43a9b
  intoNo: 11111
  filePath: /app/hopeedu
