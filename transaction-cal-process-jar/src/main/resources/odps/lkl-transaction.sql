
-- 交易流水数据
SELECT  t.merchant_sn AS `商户号 string 32`
     ,t.store_sn AS `门店号 string 32`
     ,t.terminal_sn AS `终端号 string 32`
     ,t.tsn AS `流水号 string 32`
     ,(
    CASE    WHEN TYPE IN (30, 31, 13) THEN 'pay'
            ELSE 'refund'
        END
    ) AS `流水类型 pay:refund`
     ,t.original_amount AS `订单原始金额 number long`
     ,t.effective_amount AS `订单金额 number long`
     ,t.payer_uid AS `用户id string 45`
     ,t.fee_rate AS `费率 number double`
     ,FLOOR(abs(t.fee)) AS `手续费 number long`
     ,(case when t.pay_way = 2 then '支付宝' when t.pay_way = 3 then '微信' when t.pay_way = 17 then '云闪付' else '其他' END ) AS `支付方式 支付宝:微信:云闪付:其他`
     ,FROM_UNIXTIME(t.ctime/1000) AS `交易时间 date`
FROM    wosai_main.dwb_f_trans_transaction t
   ,wosai_main.dwb_d_crm_merchant m
WHERE   t.merchant_id = m.id
  AND     t.pt = '{pt}'
  AND     m.pt = '{pt}'
  AND     t.status = 2000
  AND     t.TYPE IN (30, 31,13,11,10)
  AND     m.level1_code = '90001'
;