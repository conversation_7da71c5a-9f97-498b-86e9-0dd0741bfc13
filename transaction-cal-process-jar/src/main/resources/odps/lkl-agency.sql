
--组织机构表

SELECT  id AS `组织id string 36`
     ,code AS `组织编码 string 18`
     ,status AS `组织状态 number int 0:禁用、1启动`
     ,name AS `组织名称 string 36`
     ,parent AS `父组织id string 36`
     ,REPLACE(path, ',', '/') AS `组织路径完整code string 128`
     ,name_path AS `组织路径完整名称 string 128`
     ,level AS `组织路径层级 number int`
     ,tags AS `组织标签 string 64`
     ,lakala_code AS `拉卡拉code string 10`
     ,FROM_UNIXTIME(ctime/1000) AS `组织创建时间 date`
     ,FROM_UNIXTIME(mtime/1000) AS `组织最近一次更新时间 date`
FROM    wosai_hz_main.ods_crm_organization
where   (path = '90001' OR path LIKE '90001,%')
  AND     pt = '{pt}'
;