--- 商户门店终端数据

SELECT  m.sn AS `商户号 string  32`
     ,m.name AS `商户名称 string  128`
     ,FROM_UNIXTIME(m.ctime/1000) AS `商户创建时间 date`
     ,m.organization_id AS `商户所属组织id string  36`
     ,s.sn AS `门店号 string  32`
     ,s.name `门店名称 string  128`
     ,FROM_UNIXTIME(s.ctime/1000) AS `门店创建时间 date`
     ,s.organization_id AS `门店所属组织id string 36`
     ,s.keeper_user_name AS `门店维护人 string 32`
     ,t.sn AS `终端号 string 32`
     ,t.name `终端名称 string 128`
     ,FROM_UNIXTIME(t.ctime/1000) AS `终端创建时间 date`
     ,nvl(nvl(iot1.device_sn, iot2.device_sn), t.device_fingerprint) AS `设备号 string 128`
     ,(
    CASE    WHEN v.category = 101 THEN 'POS终端'
            WHEN v.category = 102 THEN '扫码王'
            WHEN v.category = 103 THEN '收银插件'
            WHEN v.category = 104 THEN '自助点餐码'
            WHEN v.category = 105 THEN '久久折'
            WHEN v.category = 106 THEN '收钱吧App'
            WHEN v.category = 107 THEN '刷脸设备'
            WHEN v.category = 108 THEN '收款门店码'
            WHEN v.category = 109 THEN '校园终端'
            ELSE '其他类型'
        END
    ) AS `终端分类 string 32`
     ,(
    CASE    WHEN m.status = 0 THEN '关闭'
            WHEN m.status = 1 THEN '正常'
            ELSE '禁用'
        END
    ) AS `商户状态 关闭:正常:禁用`
     ,(
    CASE    WHEN p.status IS NULL THEN '未认证'
            ELSE '已认证'
        END
    ) AS `微信升级认证 未认证:已认证`
     ,(
    CASE    WHEN a.status IS NULL THEN '未通过'
            ELSE '已通过'
        END
    ) AS `真实性审核 未通过:已通过`
     ,nvl(
        get_json_object(
                mc.params
            ,"$.lakala_trade_params.lakala_merc_id"
            )
    ,'无'
    )AS `拉卡拉商户号 string 32`
     ,(
    CASE    WHEN t.status = 0 THEN '已解绑'
            WHEN t.status = 1 THEN '正常'
            ELSE '已禁用'
        END
    ) AS `终端状态 已解绑:正常:已禁用`
     ,v.name AS `终端应用名称 string 128`
     ,v.appid AS `终端应用appid string 36`
FROM    wosai_main.dwb_d_crm_merchant m
            LEFT JOIN wosai_main.dwb_d_crm_store s
                      ON      s.merchant_id = m.id LEFT
                JOIN    wosai_hz_main.ods_trans_v2_terminal t
                    ON      (t.merchant_id = m.id and s.id = t.store_id)
            LEFT JOIN wosai_hz_main.ods_trans_vendor_app v
                      ON      t.vendor_app_appid = v.appid LEFT
                JOIN    wosai_hz_main.ods_trans_merchant_provider_params P
                        ON      (p.merchant_sn = m.sn AND p.pt = '{pt}' AND p.payway = 3 AND p.status = 1 )
            LEFT JOIN wosai_main.dwd_crm_merchant_audit a
                      on      (a.merchant_sn = m.sn AND a.pt = '{pt}' AND a.status = 3) LEFT
                JOIN    wosai_main.dim_upay_trans_trans_merchant_config_df mc
                        ON      (mc.merchant_id = m.id AND mc.pt = '{pt}' AND mc.payway IS NULL)
            LEFT JOIN wosai_hz_main.ods_iot_device_qrcode iot1
                      on      ( iot1.pt = '{pt}' AND iot1.qrcode = t.device_fingerprint ) LEFT
                JOIN    wosai_hz_main.ods_iot_device_qrcode iot2
                        on      ( iot2.pt = '{pt}' AND iot2.back_qrcode = t.device_fingerprint )
WHERE   m.pt = '{pt}'
  AND     s.pt = '{pt}'
  AND     t.pt = '{pt}'
  AND     v.pt = '{pt}'
  AND     m.level1_code = '90001'
;
