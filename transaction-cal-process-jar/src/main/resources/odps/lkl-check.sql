-- 通过此sql来检查对应分区的数据是否存在
select
    (select count(1) from  wosai_hz_main.ods_crm_organization where pt = '{pt}') as ods_crm_organization_count,
    (select count(1) from  wosai_main.dwb_d_crm_merchant where pt = '{pt}') as dwb_d_crm_merchant_count,
    (select count(1) from  wosai_main.dwb_d_crm_store where pt = '{pt}') as dwb_d_crm_store_count,
    (select count(1) from  wosai_hz_main.ods_trans_v2_terminal where pt = '{pt}') as ods_trans_v2_terminal_count,
    (select count(1) from  wosai_hz_main.ods_trans_vendor_app where pt = '{pt}') as ods_trans_vendor_app_count,
    (select count(1) from  wosai_hz_main.ods_trans_merchant_provider_params where pt = '{pt}') as ods_trans_merchant_provider_params_count,
    (select count(1) from  wosai_main.dwd_crm_merchant_audit where pt = '{pt}') as dwd_crm_merchant_audit_count,
    (select count(1) from  wosai_main.dwb_f_trans_transaction where pt = '{pt}') as dwb_f_trans_transaction_count,
    (select count(1) from  wosai_hz_main.ods_withdraw_deposit_record where pt = '{pt}') as ods_withdraw_deposit_record_count,
    (select count(1) from  wosai_main.dim_upay_trans_trans_merchant_config_df where pt = '{pt}') as ods_trans_merchant_config_count
;