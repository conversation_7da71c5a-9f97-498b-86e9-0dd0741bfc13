package com.wosai.upay.transaction.cal.process.model.domain;

import com.alibaba.fastjson.JSON;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.transaction.cal.process.model.dto.SummaryBillRemark;
import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Date;

public class TbSummaryBill {
    private Long id;

    private String sn;

    private String name;

    private String policyId;

    private Date tradeMonth;

    private Date entryMonth;

    private Integer sourceBillType;

    private Integer sourceValidTradeNum = 0;

    private Long sourceValidTradeAmount = 0L;

    private Integer sourceValidRefundNum = 0;

    private Long sourceValidRefundAmount = 0L;

    private String sourceSettlementBasisType;

    private Long sourceSettlementBasis;

    private Long sourceSettlementAmount = 0L;

    private Long sourceMerchantNumber = 0L;

    private String serverChannelCode;

    /**
     * 对方名称
     */
    private String ncName;

    /**
     * 我方名称
     */
    private String companyName;

    /**
     * 业务性质
     */
    private String propertyId;

    private SummaryBillStatus status;

    private FulfillmentPushStatus pushStatus;

    private String remark;

    private Date ctime;

    private Date mtime;

    private Boolean deleted;

    private Integer version;

    private byte[] extraParams;

    public boolean isConfirmed() {
        return this.status == SummaryBillStatus.CONFIRMED;
    }

    public void generateName(String policyName) {
        this.name = policyName + "_" + DateTimeUtil.convertToYyyyMm(tradeMonth);
        if (StringUtils.isNotEmpty(serverChannelCode)) {
            this.name = this.name + "_" + serverChannelCode;
        }
    }

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSn() {
        return sn;
    }

    public void setSn(String sn) {
        this.sn = sn;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public Date getTradeMonth() {
        return tradeMonth;
    }

    public void setTradeMonth(Date tradeMonth) {
        this.tradeMonth = tradeMonth;
    }

    public Date getEntryMonth() {
        return entryMonth;
    }

    public void setEntryMonth(Date entryMonth) {
        this.entryMonth = entryMonth;
    }

    public Integer getSourceBillType() {
        return sourceBillType;
    }

    public void setSourceBillType(Integer sourceBillType) {
        this.sourceBillType = sourceBillType;
    }

    public Integer getSourceValidTradeNum() {
        return sourceValidTradeNum;
    }

    public void setSourceValidTradeNum(Integer sourceValidTradeNum) {
        this.sourceValidTradeNum = sourceValidTradeNum;
    }

    public Long getSourceValidTradeAmount() {
        return sourceValidTradeAmount;
    }

    public void setSourceValidTradeAmount(Long sourceValidTradeAmount) {
        this.sourceValidTradeAmount = sourceValidTradeAmount;
    }

    public Integer getSourceValidRefundNum() {
        return sourceValidRefundNum;
    }

    public void setSourceValidRefundNum(Integer sourceValidRefundNum) {
        this.sourceValidRefundNum = sourceValidRefundNum;
    }

    public Long getSourceValidRefundAmount() {
        return sourceValidRefundAmount;
    }

    public void setSourceValidRefundAmount(Long sourceValidRefundAmount) {
        this.sourceValidRefundAmount = sourceValidRefundAmount;
    }

    public String getSourceSettlementBasisType() {
        return sourceSettlementBasisType;
    }

    public void setSourceSettlementBasisType(String sourceSettlementBasisType) {
        this.sourceSettlementBasisType = sourceSettlementBasisType;
    }

    public Long getSourceSettlementBasis() {
        return sourceSettlementBasis;
    }

    public void setSourceSettlementBasis(Long sourceSettlementBasis) {
        this.sourceSettlementBasis = sourceSettlementBasis;
    }

    public Long getSourceSettlementAmount() {
        return sourceSettlementAmount;
    }

    public void setSourceSettlementAmount(Long sourceSettlementAmount) {
        this.sourceSettlementAmount = sourceSettlementAmount;
    }

    public Long getSourceMerchantNumber() {
        return sourceMerchantNumber;
    }

    public void setSourceMerchantNumber(Long sourceMerchantNumber) {
        this.sourceMerchantNumber = sourceMerchantNumber;
    }

    public String getServerChannelCode() {
        return serverChannelCode;
    }

    public void setServerChannelCode(String serverChannelCode) {
        this.serverChannelCode = serverChannelCode;
    }

    public String getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(String propertyId) {
        this.propertyId = propertyId;
    }


    public String getNcName() {
        return ncName;
    }

    public void setNcName(String ncName) {
        this.ncName = ncName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        this.companyName = companyName;
    }

    public SummaryBillStatus getStatus() {
        return status;
    }

    public void setStatus(SummaryBillStatus status) {
        this.status = status;
    }

    public FulfillmentPushStatus getPushStatus() {
        return pushStatus;
    }

    public void setPushStatus(FulfillmentPushStatus pushStatus) {
        this.pushStatus = pushStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getCtime() {
        return ctime;
    }

    public void setCtime(Date ctime) {
        this.ctime = ctime;
    }

    public Date getMtime() {
        return mtime;
    }

    public void setMtime(Date mtime) {
        this.mtime = mtime;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public byte[] getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(byte[] extraParams) {
        this.extraParams = extraParams;
    }

    public void cancelImportTask(String taskSn) {
        if (StringUtils.isNotEmpty(remark)) {
            SummaryBillRemark remark = JSON.parseObject(this.remark, SummaryBillRemark.class);
            remark.cancelTask(taskSn);
            this.remark = JSON.toJSONString(remark);
        }
    }

    public SummaryBillRemark queryRemarkObj() {
        return JSON.parseObject(this.remark, SummaryBillRemark.class);
    }

    /**
     * Remove bill output and update summary bill amounts
     * @param billOutput bill output to remove
     * @throws CommonInvalidParameterException if bill is already confirmed
     */
    public void removeBillOutput(TbBillOutputV2 billOutput) {
        if (this.isConfirmed()) {
            throw new CommonInvalidParameterException("账单不支持修改");
        }
        this.sourceValidRefundAmount -= billOutput.getSourceValidRefundAmount();
        this.sourceSettlementAmount -= billOutput.getSourceSettlementAmount();
        this.sourceValidTradeAmount -= billOutput.getSourceValidTradeAmount();
        this.sourceValidRefundNum -= billOutput.getSourceValidRefundNum();
        this.sourceMerchantNumber -= 1;
        this.sourceValidTradeNum -= billOutput.getSourceValidTradeNum();
    }
}