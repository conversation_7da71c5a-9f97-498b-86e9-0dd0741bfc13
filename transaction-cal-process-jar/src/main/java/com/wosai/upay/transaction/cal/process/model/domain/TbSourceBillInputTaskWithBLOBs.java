package com.wosai.upay.transaction.cal.process.model.domain;

public class TbSourceBillInputTaskWithBLOBs extends TbSourceBillInputTask {
    private byte[] remark;

    private byte[] errorInfo;

    private byte[] extra;

    private byte[] config;

    public byte[] getRemark() {
        return remark;
    }

    public void setRemark(byte[] remark) {
        this.remark = remark;
    }

    public byte[] getErrorInfo() {
        return errorInfo;
    }

    public void setErrorInfo(byte[] errorInfo) {
        this.errorInfo = errorInfo;
    }

    public byte[] getExtra() {
        return extra;
    }

    public void setExtra(byte[] extra) {
        this.extra = extra;
    }

    public byte[] getConfig() {
        return config;
    }

    public void setConfig(byte[] config) {
        this.config = config;
    }
}