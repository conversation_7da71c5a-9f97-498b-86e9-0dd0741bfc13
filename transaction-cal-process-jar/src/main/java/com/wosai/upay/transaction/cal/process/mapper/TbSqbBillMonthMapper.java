package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth;
import com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonthExample;

import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface TbSqbBillMonthMapper {
    long countByExample(TbSqbBillMonthExample example);

    int deleteByExample(TbSqbBillMonthExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbSqbBillMonth record);

    int insertSelective(TbSqbBillMonth record);

    List<TbSqbBillMonth> selectByExample(TbSqbBillMonthExample example);

    TbSqbBillMonth selectByPrimaryKey(Integer id);

    List<TbSqbBillMonth> listSqbBillMonth(@Param("billMonth") String billMonth,
                                          @Param("billType") int billType,
                                          @Param("offset") Integer offset,
                                          @Param("size") Integer size);

    int updateByExampleSelective(@Param("record") TbSqbBillMonth record, @Param("example") TbSqbBillMonthExample example);

    int updateByExample(@Param("record") TbSqbBillMonth record, @Param("example") TbSqbBillMonthExample example);

    int updateByPrimaryKeySelective(TbSqbBillMonth record);

    int updateByPrimaryKey(TbSqbBillMonth record);
}