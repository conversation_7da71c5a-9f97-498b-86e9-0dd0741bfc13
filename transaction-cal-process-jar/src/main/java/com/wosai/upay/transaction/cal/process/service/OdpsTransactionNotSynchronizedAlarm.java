package com.wosai.upay.transaction.cal.process.service;

import com.aliyun.odps.Instance;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.task.SQLTask;
import com.wosai.upay.transaction.cal.process.util.*;
import com.aliyun.odps.data.Record;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @description:
 * @author: lll
 * @date: 2022-03-16 1:05 下午
 */

@Component
public class OdpsTransactionNotSynchronizedAlarm {

    private static final String SQL = "SELECT * from wosai_hz_main.ods_trans_v2_transaction where pt = '${date}' limit 10" +
            ";";
    private final static Logger log = LoggerFactory.getLogger(OdpsTransactionNotSynchronizedAlarm.class);

    private static final String  templateId = "LLLLLLLLLLLL";

    @Autowired
    SendVoiceMsgUtils sendVoiceMsgUtils;

    @Scheduled(cron = "0 0/5 4 * * ?")
    public void odpsAlarm() throws Exception {
        if (CommonApolloUtil.getAlarmFlag()) {
            String date = DateTimeUtil.getDate();
            String sql = SQL.replace("${date}", date);
            Instance i;
            try {
                i = SQLTask.run(OdpsUtils.odps, sql);
                i.waitForSuccess();
                List<Record> records = SQLTask.getResult(i);
                if (records.size() == 0) {
                    List<String> phoneNums = CommonApolloUtil.getAlarmPhonums();
                    for (String phoneNum : phoneNums) {
                        sendVoiceMsgUtils.send(templateId, phoneNum);
                    }

                }
            } catch (OdpsException e) {
                log.error("数仓任务同步延时告警失败，原因：", e.getMessage());
            }
        }

    }


}
