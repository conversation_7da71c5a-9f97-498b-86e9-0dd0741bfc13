package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.StatementTask;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskDto;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

@Mapper
public interface StatementTaskMapper extends tk.mybatis.mapper.common.Mapper<StatementTask> {

    List<StatementTaskDto> selectTaskList(@Param("taskId") Long taskId, @Param("merchantType") Integer merchantType
            , @Param("groupSn") String groupSn, @Param("merchantId") String merchantId
            , @Param("planId") Long planId, @Param("planName") String planName, @Param("isvCode") String isvCode
    );

    List<StatementTask> selectAllAliveTask();

    List<StatementTask> selectAllAliveTaskByType(@Param("type") Integer type);

}