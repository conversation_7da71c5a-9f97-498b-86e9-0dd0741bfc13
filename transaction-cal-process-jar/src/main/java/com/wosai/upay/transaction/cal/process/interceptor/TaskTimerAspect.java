package com.wosai.upay.transaction.cal.process.interceptor;


import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.codehaus.jackson.map.ObjectMapper;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@Aspect
@Order(10)
public class TaskTimerAspect {

    private final ObjectMapper objectMapper = new ObjectMapper();


    @Pointcut("@annotation(com.wosai.upay.transaction.cal.process.interceptor.CalculateMethodTimeConsuming)")
    public void exclusiveTaskPoint() {}


    @Around("exclusiveTaskPoint()")
    public Object invoke(ProceedingJoinPoint point) throws Throwable {
        Object result = null;
        long startTime = System.currentTimeMillis();
        result = point.proceed();
        long endTime = System.currentTimeMillis();
        try {
            CalculateMethodTimeConsuming exclusiveTask = ((MethodSignature)point.getSignature()).getMethod()
                    .getDeclaredAnnotation(CalculateMethodTimeConsuming.class);
            log.info("taskName:{},params:{},time-consuming:{}ms", exclusiveTask.name(), objectMapper.writeValueAsString(point.getArgs()), endTime - startTime);
        }catch (Exception e){
            log.error("统计耗时出错:{}", e.getMessage());
        }
        return result;
    }

}
