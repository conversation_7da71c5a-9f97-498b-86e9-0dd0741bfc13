package com.wosai.upay.transaction.cal.process.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.stereotype.Component;
import redis.clients.jedis.Jedis;
import redis.clients.jedis.Protocol;
import redis.clients.util.SafeEncoder;

import java.util.Collections;

/**
 * RedisUtils
 *
 * <AUTHOR>
 * @date 2019-09-20 09:42
 */
@Slf4j
@Component
public class RedisUtils {

    /**
     * 用于Redis释放分布式锁的lua脚本
     */
    private static final String REDIS_RELEASE_LUA;

    static {
        StringBuilder sb = new StringBuilder();
        sb.append("if redis.call(\"get\",KEYS[1]) == ARGV[1] ");
        sb.append("then ");
        sb.append("    return redis.call(\"del\",KEYS[1]) ");
        sb.append("else ");
        sb.append("    return 0 ");
        sb.append("end ");
        REDIS_RELEASE_LUA = sb.toString();
    }

    private final RedisTemplate redisTemplate;

    public RedisUtils(RedisTemplate redisTemplate) {
        // Redis序列化成字符串
        RedisSerializer stringSerializer = new StringRedisSerializer();
        redisTemplate.setKeySerializer(stringSerializer);
        redisTemplate.setValueSerializer(stringSerializer);
        redisTemplate.setHashKeySerializer(stringSerializer);
        redisTemplate.setHashValueSerializer(stringSerializer);
        this.redisTemplate = redisTemplate;
    }

    /**
     * 【分布式下原子操作】 如果key不存在，则设置 <key, value><br>
     *
     * @param key        the key
     * @param value      the value
     * @param expireTime key 过期时间（单位：秒）
     * @return true 表示 set 成功,否则表示失败
     */
    public boolean setNX(String key, String value, long expireTime) {
        try {
            return (Boolean) redisTemplate.execute((RedisCallback<Boolean>) connection -> {
                RedisSerializer keySerializer = redisTemplate.getKeySerializer();
                RedisSerializer valueSerializer = redisTemplate.getValueSerializer();
                Object object = connection.execute("set",
                        keySerializer.serialize(key),
                        valueSerializer.serialize(value),
                        SafeEncoder.encode("NX"),
                        SafeEncoder.encode("EX"),
                        Protocol.toByteArray(expireTime));
                return object != null;
            });
        } catch (Throwable e) {
            log.error("Redis 执行 setNX(String {}, String {}, long {}) 异常:{}", key, value, expireTime, e);
            return false;
        }
    }

    /**
     * 【分布式下原子操作】当 key 存在，并且 value 相等的情况下删除该 key
     *
     * @param key   the key
     * @param value the value
     * @return true 表示删除成功,否则表示删除失败
     */
    public boolean deleteIfEqual(String key, String value) {
        try {
            return (Boolean) redisTemplate.execute((RedisCallback<Boolean>) connection -> {
                Object nativeConnection = connection.getNativeConnection();
                Long result = (Long) ((Jedis) nativeConnection).eval(REDIS_RELEASE_LUA,
                        Collections.singletonList(key),
                        Collections.singletonList(value));
                return result != null && result == 1;
            });
        } catch (Throwable e) {
            log.error("RedisDaoImpl 执行 deleteIfEqual(String {}, String {}) 异常:{}", key, value, e);
            return false;
        }

    }
}
