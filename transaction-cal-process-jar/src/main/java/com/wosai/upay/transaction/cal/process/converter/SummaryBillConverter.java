package com.wosai.upay.transaction.cal.process.converter;

import com.alibaba.fastjson.JSON;
import com.wosai.itsys.cornucopia.admin.domain.response.ImportTaskResponse;
import com.wosai.itsys.cornucopia.admin.domain.response.ImportTemplateRuleResponse;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2;
import com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill;
import com.wosai.upay.transaction.cal.process.model.dto.BillOutputV2;
import com.wosai.upay.transaction.cal.process.model.dto.SummaryBillRemark;
import com.wosai.upay.transaction.cal.process.model.dto.TbSourceBillInfoDTO;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import com.wosai.upay.transaction.cal.process.model.response.SummaryBillResponse;
import com.wosai.upay.transaction.cal.process.processor.SourceBill;
import com.wosai.upay.transaction.cal.process.util.CurrencyUtils;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Mapper(componentModel = "spring")
public interface SummaryBillConverter {
    SummaryBillConverter INSTANCE = Mappers.getMapper(SummaryBillConverter.class);

    @Mappings({
            @Mapping(source = "tradeMonth", target = "tradeMonth", qualifiedByName = "dateToString"),
            @Mapping(source = "entryMonth", target = "entryMonth", qualifiedByName = "dateToString"),
            @Mapping(source = "sourceSettlementAmount", target = "totalSettlementAmount", qualifiedByName = "fenToYuan"),
            @Mapping(source = "sourceValidTradeNum", target = "totalSettlementLineCount"),
            @Mapping(source = "sourceMerchantNumber", target = "totalSourceMerchantCount"),
            @Mapping(source = "pushStatus", target = "fulfillmentPushStatus"),
            @Mapping(source = "remark", target = "remark", qualifiedByName = "convertRemark"),
            @Mapping(source = "ncName", target = "ncName"),
            @Mapping(source = "companyName", target = "companyName"),
            @Mapping(source = "propertyId", target = "propertyId")
    })
    SummaryBillResponse doToVo(TbSummaryBill tbSummaryBill);

    @Mappings({
            @Mapping(source = "importTemplateRuleResponse.name", target = "importTemplateRuleName"),
            @Mapping(source = "importTaskResponse.num", target = "number"),
            @Mapping(source = "importTaskStatus", target = "status"),
    })
    SummaryBillResponse.RelatedImportTask convertRelatedImportTask(ImportTaskResponse importTaskResponse, ImportTemplateRuleResponse importTemplateRuleResponse, SummaryBillRemark.SummaryBillImportTaskStatus importTaskStatus);

    SummaryBillResponse.RelatedImportTask convertRelatedImportTask(SummaryBillRemark.SummaryBillImportTask summaryBillImportTask);

    List<SummaryBillResponse.RelatedImportTask> convertRelatedImportTaskList(List<SummaryBillRemark.SummaryBillImportTask> summaryBillImportTask);

    @Mappings({
            @Mapping(source = "importTemplateRuleResponse.name", target = "importTemplateRuleName"),
            @Mapping(source = "importTaskResponse.num", target = "number"),
            @Mapping(target = "status", expression = "java(SummaryBillImportTaskStatus.SUCCESS)")
    })
    SummaryBillRemark.SummaryBillImportTask convertSummaryBillImportTask(ImportTaskResponse importTaskResponse, ImportTemplateRuleResponse importTemplateRuleResponse, Integer innerTaskId);

    @Named("fenToYuan")
    default String fenToYuan(Long fen) {
        return CurrencyUtils.fenToYua(fen);
    }

    @Named("dateToString")
    default String dateToString(java.util.Date date) {
        return DateTimeUtil.convertToYyyyMm(date);
    }

    @Named("convertRemark")
    default SummaryBillRemark convertRemark(String remark) {
        if (StringUtil.isEmpty(remark)) {
            return null;
        }
        return JSON.parseObject(remark, SummaryBillRemark.class);
    }

    List<SummaryBillResponse> doListToVoList(List<TbSummaryBill> tbSummaryBills);

    @Mappings({
            @Mapping(source = "tbSourceBillInfoDTO.remark", target = "remark", qualifiedByName = "convertRemarkToString"),
            @Mapping(source = "tbSourceBillInfoDTO.detailList", target = "detailList", qualifiedByName = "dtoToDo"),
            @Mapping(source = "tbSourceBillInfoDTO.ncName", target = "ncName"),
            @Mapping(source = "tbSourceBillInfoDTO.companyName", target = "companyName"),
            @Mapping(source = "tbSourceBillInfoDTO.propertyId", target = "propertyId")
    })
    SourceBill dtoToDo(TbSourceBillInfoDTO tbSourceBillInfoDTO, Integer sourceBillType);

    @Named("convertRemarkToString")
    default String convertRemarkToString(SummaryBillRemark remark) {
        if (remark == null) {
            return null;
        }
        return JSON.toJSONString(remark);
    }

    @Named("truncateFeeRate")
    default String truncateFeeRate(String feeRate) {
        if (feeRate != null && feeRate.length() > 6) {
            return feeRate.substring(0, 6);
        }
        return feeRate;
    }

    @Mappings({
            @Mapping(source = "detail", target = "extraParams", qualifiedByName = "convertExtraParams"),
            @Mapping(source = "sourceMerchantFeeRate", target = "sourceMerchantFeeRate", qualifiedByName = "truncateFeeRate"),
            @Mapping(source = "sourceSettlementFeeRate", target = "sourceSettlementFeeRate", qualifiedByName = "truncateFeeRate")
    })
    SourceBill.Detail dtoToDo(TbSourceBillInfoDTO.Detail detail);

    @Named("convertExtraParams")
    default Map<java.lang.String,java.lang.Object> convertExtraParams(TbSourceBillInfoDTO.Detail tbSourceBillInfoDTO) {
        if (tbSourceBillInfoDTO == null) {
            return null;
        }
        return new HashMap<String, Object>() {{
            put("extraParam1", tbSourceBillInfoDTO.getExtraParam1());
            put("extraParam2", tbSourceBillInfoDTO.getExtraParam2());
            put("extraParam3", tbSourceBillInfoDTO.getExtraParam3());
            put("extraParam4", tbSourceBillInfoDTO.getExtraParam4());
            put("extraParam5", tbSourceBillInfoDTO.getExtraParam5());
            put("trade_type", tbSourceBillInfoDTO.getSourceMerchantId());
            put("is_single_channel_trade", tbSourceBillInfoDTO.getIsSingleChannelTrade());
            put("settlement_state", tbSourceBillInfoDTO.getSettlementState());
            put("merchantLevel", tbSourceBillInfoDTO.getMerchantLevel());
        }};
    }

    @Mappings({
            @Mapping(source = "tbBillOutput.sourceSettlementAmount", target = "isSettlement", qualifiedByName = "isSettlement"),
            @Mapping(source = "summaryBillName", target = "summaryBillName"),
            @Mapping(source = "summaryBillStatus", target = "status"),
            @Mapping(source = "tbBillOutput.tradeDateMonth", target = "tradeMonth", qualifiedByName = "dateToString"),
            @Mapping(source = "tbBillOutput.isImport", target = "isImport", qualifiedByName = "booleanToString"),
            @Mapping(source = "tbBillOutput.sourceValidTradeNum", target = "sourceTradeNum"),
            @Mapping(source = "tbBillOutput.sourceValidTradeAmount", target = "sourceTradeAmount"),
            @Mapping(source = "tbBillOutput.sourceValidRefundNum", target = "sourceRefundNum"),
            @Mapping(source = "tbBillOutput.sourceValidRefundAmount", target = "sourceRefundAmount"),
    })
    BillOutputV2 doToBillOutputV2(TbBillOutputV2 tbBillOutput, String summaryBillName, SummaryBillStatus summaryBillStatus);

    @Named("booleanToString")
    default String booleanToString(Boolean bool) {
        return bool ? "1": "0";
    }

    @Named("isSettlement")
    default boolean isSettlement(Long sourceSettlementAmount) {
        return sourceSettlementAmount != null && sourceSettlementAmount != 0L;
    }
}
