package com.wosai.upay.transaction.cal.process.scheduler;


import com.wosai.upay.transaction.cal.process.constant.StatementType;
import com.wosai.upay.transaction.cal.process.interceptor.CalculateMethodTimeConsuming;
import com.wosai.upay.transaction.cal.process.service.TaskSchedulerServiceImpl;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import com.wosai.upay.task.center.model.StatementTaskLog;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
public class StatementTaskScheduler {

    @Autowired
    private TaskSchedulerServiceImpl statementTaskService;

    @Scheduled(cron = "0 0 2 * * ?")
    @CalculateMethodTimeConsuming(name = "对账单推送总耗时")
    public void sendStatements() {
        long startTime =System.currentTimeMillis();
        log.info("普通对账单开始推送");
        long timeStart = DateTimeUtil.getYesterdayStart().getTimeInMillis();
        long timeEnd = DateTimeUtil.getTodayStart().getTimeInMillis();

        statementTaskService.sendStatements(timeStart, timeEnd, StatementTaskLog.TYPE_TRANSACTION);
        statementTaskService.sendStatements(timeStart, timeEnd, StatementTaskLog.TYPE_TRANSACTION_GROUP);
        statementTaskService.sendStatements(timeStart, timeEnd, StatementType.TYPE_SHARING_BILL_PAYER);
        statementTaskService.sendStatements(timeStart, timeEnd, StatementType.TYPE_SHARING_BILL_RECEIVER);
        statementTaskService.sendStatements(timeStart, timeEnd, StatementType.TYPE_MERCHANT_AUTHORIZE);
        log.info("普通对账单推送结束");
        long endTime =System.currentTimeMillis();
        log.info("普通对账单推送总耗时-{} ms", endTime - startTime);
    }


//    @Scheduled(cron = "0 0 4 * * ?")
    @CalculateMethodTimeConsuming(name = "Isv对账单推送总耗时")
    public void sendIsvStatements() {
        long timeStart = DateTimeUtil.getYesterdayStart().getTimeInMillis();
        long timeEnd = DateTimeUtil.getTodayStart().getTimeInMillis();

        statementTaskService.sendStatements(timeStart, timeEnd, StatementType.TYPE_ISV);
    }


    @Scheduled(cron = "0 0 5 * * ?")
    @CalculateMethodTimeConsuming(name = "Isv对账单推送总耗时 新版本")
    public void sendIsvStatementsNew() {
        long startTime =System.currentTimeMillis();
        log.info("isv对账单开始推送");
        long timeStart = DateTimeUtil.getYesterdayStart().getTimeInMillis();
        long timeEnd = DateTimeUtil.getTodayStart().getTimeInMillis();
        statementTaskService.sendStatementsForIsv(timeStart, timeEnd);
        log.info("isv对账单结束推送");
        long endTime =System.currentTimeMillis();
        log.info("isv对账单推送总耗时-{} ms", endTime - startTime);
    }
}
