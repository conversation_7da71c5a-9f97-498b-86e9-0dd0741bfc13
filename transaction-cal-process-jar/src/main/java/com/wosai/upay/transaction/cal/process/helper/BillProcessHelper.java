package com.wosai.upay.transaction.cal.process.helper;

import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.job.model.MerchantParamReq;
import com.wosai.upay.job.model.dto.MerchantProviderParamsDto;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.transaction.cal.process.mapper.TbSqbBillMonthMapper;
import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput;
import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2;
import com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonth;
import com.wosai.upay.transaction.cal.process.model.domain.TbSqbBillMonthExample;
import com.wosai.upay.transaction.cal.process.model.enums.SourceBillType;
import com.wosai.upay.transaction.cal.process.processor.SourceBill;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.ParseException;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * BillProcessHelper
 *
 * <AUTHOR>
 * @date 2019-10-23 12:19
 */
@Slf4j
@Component
public class BillProcessHelper {
    @Autowired
    private MerchantProviderParamsService merchantProviderParamsService;

    @Autowired
    private MerchantService merchantService;

    private final TbSqbBillMonthMapper tbSqbBillMonthMapper;

    //当上游账单匹配收钱吧流水为空时， 需要根据pid查询收钱吧商户sn的账单类型
    private static List<Integer> needQueryMerchantSnTypeList = Arrays.asList(
            SourceBillType.TYPE_ALIPAY_DIRECT_TRADE,
            SourceBillType.TYPE_ALIPAY_KOUBEI,
            SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT,
            SourceBillType.TYPE_WEIXIN_DIRECT_TRADE,
            SourceBillType.TYPE_WEIXIN_SCHOOL_CAFETERIA,
            SourceBillType.TYPE_ALIPAY_SCHOOL,
            SourceBillType.TYPE_ALIPAY_SCHOOL_V2,
            SourceBillType.TYPE_WEIXIN_PUBLIC_HOSPITAL,
            SourceBillType.TYPE_ALIPAY_INDIRECT_TRADE,
            SourceBillType.TYPE_WEIXIN_SCHOOL_DIRECT_CAFETERIA,
            SourceBillType.TYPE_ALIPAY_TUANCAN,
            SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT,
            SourceBillType.TYPE_ALIPAY_EDU,
            SourceBillType.TYPE_WECHAT_INDIRECT_A,
            SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE,
            SourceBillType.TYPE_ALIPAY_HOSPITAL,
            SourceBillType.TYPE_ALIPAY_HBFQ_TX
    );

    public BillProcessHelper(TbSqbBillMonthMapper tbSqbBillMonthMapper) {
        this.tbSqbBillMonthMapper = tbSqbBillMonthMapper;
    }

    /**
     * 字符串 yyyymm 格式的日期转为 java.util.Date 格式
     *
     * @param yyyymm 字符串格式日期
     * @return java.util.Date
     */
    public Date yyyymmToDate(String yyyymm) {
        try {
            return DateUtils.parseDate(yyyymm, "yyyyMM");
        } catch (ParseException e) {
            log.error("日期格式不正确", e);
            throw new CommonInvalidParameterException("日期格式不正确");
        }
    }

    /**
     * 月份，将格式为 201909 转化为 2019-09
     *
     * @param yyyymm 示例：201909
     * @return 示例：2019-09
     */
    public static String yyyymmWithLine(String yyyymm) {
        return yyyymm.substring(0, 4) + "-" + yyyymm.substring(4);
    }


    /**
     * 拆分上游账单：按账期内收钱吧商户的交易量占比拆分
     *
     * @param taskId             任务id
     * @param tradeMonth         账单月份
     * @param billType           账单类型
     * @param tbSqbBillMonthList 收钱吧账单列表
     * @param detail             上游账单行详情（被拆分者）
     * @return 拆分后的上游账单列表
     */
    @Deprecated
    private List<TbBillOutput> splitSourceBillBySqbTradeAmountRatio(int taskId,
                                                                    Date tradeMonth,
                                                                    int billType,
                                                                    List<TbSqbBillMonth> tbSqbBillMonthList,
                                                                    SourceBill.Detail detail) {
        // 收钱吧账单列表合计有效交易金额
        BigDecimal sqbValidTradeAmountTotal = BigDecimal.valueOf(tbSqbBillMonthList.stream().mapToLong(TbSqbBillMonth::getSqbTradeMoney).sum());
        // 收钱吧账单列表合计有效退款金额
        BigDecimal sqbValidRefundAmountTotal = BigDecimal.valueOf(tbSqbBillMonthList.stream().mapToLong(TbSqbBillMonth::getSqbRefundMoney).sum());

        // 如有 收钱吧账单列表合计有效交易金额 - 收钱吧账单列表合计有效退款金额 为 0，则按 匹配到多个收钱吧商户时，平均分配
        if (sqbValidTradeAmountTotal.subtract(sqbValidRefundAmountTotal).compareTo(BigDecimal.ZERO) == 0) {
            return this.splitSourceBillBySqbMerchantNum(taskId, tradeMonth, billType, tbSqbBillMonthList, detail);
        }

        List<TbBillOutput> insertParamList = new ArrayList<>();

        // 前 n - 1 项累计值
        int sourceValidTradeNumCumulative = 0;
        long sourceValidTradeAmountCumulative = 0L;
        int sourceValidRefundNumCumulative = 0;
        long sourceValidRefundAmountCumulative = 0L;
        long sourceSettlementBasisCumulative = 0L;
        long sourceSettlementAmountCumulative = 0L;
        BigDecimal sourceValidTradeAmountRatioCumulative = new BigDecimal(0);

        for (int i = 0; i < tbSqbBillMonthList.size(); i++) {
            // 前 n - 1 项
            if (i < tbSqbBillMonthList.size() - 1) {
                TbBillOutput insertParam = convertToTbBillOutput(detail, tbSqbBillMonthList.get(i), taskId, tradeMonth, billType);

                // 收钱吧每条交易占比（精确度：0.0001）= (sqb有效支付金额 - sqb有效退款金额) / (sqb合计有效支付金额 - sqb合计有效退款金额)
                BigDecimal sqbValidTradeAmountRatio;
                if (sqbValidTradeAmountTotal.subtract(sqbValidRefundAmountTotal).compareTo(BigDecimal.ZERO) == 0) {
                    sqbValidTradeAmountRatio = BigDecimal.ZERO;
                } else {
                    sqbValidTradeAmountRatio = BigDecimal.valueOf(tbSqbBillMonthList.get(i).getSqbTradeMoney()).subtract(BigDecimal.valueOf(tbSqbBillMonthList.get(i).getSqbRefundMoney()))
                            .divide(sqbValidTradeAmountTotal.subtract(sqbValidRefundAmountTotal), 4, RoundingMode.FLOOR);
                }
                sourceValidTradeAmountRatioCumulative = sourceValidTradeAmountRatioCumulative.add(sqbValidTradeAmountRatio);
                insertParam.setSourceValidTradeAmountRatio(sqbValidTradeAmountRatio.toPlainString());

                // 有效交易笔数
                int currentValidSourceTradeNum = BigDecimal.valueOf(detail.getSourceValidTradeNum()).multiply(sqbValidTradeAmountRatio).intValue();
                sourceValidTradeNumCumulative += currentValidSourceTradeNum;
                insertParam.setSourceValidTradeNum(currentValidSourceTradeNum);

                // 有效交易金额
                long currentValidSourceTradeAmount = BigDecimal.valueOf(detail.getSourceValidTradeAmount()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceValidTradeAmountCumulative += currentValidSourceTradeAmount;
                insertParam.setSourceValidTradeAmount(currentValidSourceTradeAmount);

                // 有效退款笔数
                int currentSourceValidRefundNum = BigDecimal.valueOf(detail.getSourceValidRefundNum()).multiply(sqbValidTradeAmountRatio).intValue();
                sourceValidRefundNumCumulative += currentSourceValidRefundNum;
                insertParam.setSourceValidRefundNum(currentSourceValidRefundNum);

                // 有效退款金额
                long currentSourceValidRefundAmount = BigDecimal.valueOf(detail.getSourceValidRefundAmount()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceValidRefundAmountCumulative += currentSourceValidRefundAmount;
                insertParam.setSourceValidRefundAmount(currentSourceValidRefundAmount);

                // 结算依据
                long currentSourceSettlementBasis = BigDecimal.valueOf(detail.getSourceSettlementBasis()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceSettlementBasisCumulative += currentSourceSettlementBasis;
                insertParam.setSourceSettlementBasis(currentSourceSettlementBasis);

                // 应结算总金额
                long currentSourceSettlementAmount = BigDecimal.valueOf(detail.getSourceSettlementAmount()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceSettlementAmountCumulative += currentSourceSettlementAmount;
                insertParam.setSourceSettlementAmount(currentSourceSettlementAmount);
                insertParam.setServiceProviderId(detail.getServiceProviderId());
                insertParam.setExtraParams(detail.getExtraParams());
                insertParamList.add(insertParam);
            } else { // 第 n 项
                TbBillOutput insertParam = convertToTbBillOutput(detail, tbSqbBillMonthList.get(i), taskId, tradeMonth, billType);
                insertParam.setSourceValidTradeAmountRatio(BigDecimal.valueOf(1).subtract(sourceValidTradeAmountRatioCumulative).toPlainString());
                insertParam.setSourceValidTradeNum(detail.getSourceValidTradeNum() - sourceValidTradeNumCumulative); // 有效交易笔数
                insertParam.setSourceValidTradeAmount(detail.getSourceValidTradeAmount() - sourceValidTradeAmountCumulative);// 有效交易金额
                insertParam.setSourceValidRefundNum(detail.getSourceValidRefundNum() - sourceValidRefundNumCumulative); // 有效退款笔数
                insertParam.setSourceValidRefundAmount(detail.getSourceValidRefundAmount() - sourceValidRefundAmountCumulative); // 有效退款金额
                insertParam.setSourceSettlementBasis(detail.getSourceSettlementBasis() - sourceSettlementBasisCumulative); // 结算依据
                insertParam.setSourceSettlementAmount(detail.getSourceSettlementAmount() - sourceSettlementAmountCumulative); // 应结算总金额
                insertParam.setServiceProviderId(detail.getServiceProviderId());
                insertParam.setExtraParams(detail.getExtraParams());

                insertParamList.add(insertParam);
            }
        }

        return insertParamList;
    }

    private List<TbBillOutputV2> splitSourceBillBySqbTradeAmountRatioV2(int taskId,
                                                                        Date tradeMonth,
                                                                        int billType,
                                                                        List<TbSqbBillMonth> tbSqbBillMonthList,
                                                                        SourceBill.Detail detail) {
        // 收钱吧账单列表合计有效交易金额
        BigDecimal sqbValidTradeAmountTotal = BigDecimal.valueOf(tbSqbBillMonthList.stream().mapToLong(TbSqbBillMonth::getSqbTradeMoney).sum());
        // 收钱吧账单列表合计有效退款金额
        BigDecimal sqbValidRefundAmountTotal = BigDecimal.valueOf(tbSqbBillMonthList.stream().mapToLong(TbSqbBillMonth::getSqbRefundMoney).sum());

        // 如有 收钱吧账单列表合计有效交易金额 - 收钱吧账单列表合计有效退款金额 为 0，则按 匹配到多个收钱吧商户时，平均分配
        if (sqbValidTradeAmountTotal.subtract(sqbValidRefundAmountTotal).compareTo(BigDecimal.ZERO) == 0) {
            return this.splitSourceBillBySqbMerchantNumV2(taskId, tradeMonth, billType, tbSqbBillMonthList, detail);
        }

        List<TbBillOutputV2> insertParamList = new ArrayList<>();

        // 前 n - 1 项累计值
        int sourceValidTradeNumCumulative = 0;
        long sourceValidTradeAmountCumulative = 0L;
        int sourceValidRefundNumCumulative = 0;
        long sourceValidRefundAmountCumulative = 0L;
        long sourceSettlementBasisCumulative = 0L;
        long sourceSettlementAmountCumulative = 0L;
        BigDecimal sourceValidTradeAmountRatioCumulative = new BigDecimal(0);

        for (int i = 0; i < tbSqbBillMonthList.size(); i++) {
            // 前 n - 1 项
            if (i < tbSqbBillMonthList.size() - 1) {
                TbBillOutputV2 insertParam = convertToTbBillOutputV2(detail, tbSqbBillMonthList.get(i), taskId, tradeMonth, billType);

                // 收钱吧每条交易占比（精确度：0.0001）= (sqb有效支付金额 - sqb有效退款金额) / (sqb合计有效支付金额 - sqb合计有效退款金额)
                BigDecimal sqbValidTradeAmountRatio;
                if (sqbValidTradeAmountTotal.subtract(sqbValidRefundAmountTotal).compareTo(BigDecimal.ZERO) == 0) {
                    sqbValidTradeAmountRatio = BigDecimal.ZERO;
                } else {
                    sqbValidTradeAmountRatio = BigDecimal.valueOf(tbSqbBillMonthList.get(i).getSqbTradeMoney()).subtract(BigDecimal.valueOf(tbSqbBillMonthList.get(i).getSqbRefundMoney()))
                            .divide(sqbValidTradeAmountTotal.subtract(sqbValidRefundAmountTotal), 4, RoundingMode.FLOOR);
                }
                sourceValidTradeAmountRatioCumulative = sourceValidTradeAmountRatioCumulative.add(sqbValidTradeAmountRatio);
                insertParam.setSourceValidTradeAmountRatio(sqbValidTradeAmountRatio.toPlainString());

                // 有效交易笔数
                int currentValidSourceTradeNum = BigDecimal.valueOf(detail.getSourceValidTradeNum()).multiply(sqbValidTradeAmountRatio).intValue();
                sourceValidTradeNumCumulative += currentValidSourceTradeNum;
                insertParam.setSourceValidTradeNum(currentValidSourceTradeNum);

                // 有效交易金额
                long currentValidSourceTradeAmount = BigDecimal.valueOf(detail.getSourceValidTradeAmount()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceValidTradeAmountCumulative += currentValidSourceTradeAmount;
                insertParam.setSourceValidTradeAmount(currentValidSourceTradeAmount);

                // 有效退款笔数
                int currentSourceValidRefundNum = BigDecimal.valueOf(detail.getSourceValidRefundNum()).multiply(sqbValidTradeAmountRatio).intValue();
                sourceValidRefundNumCumulative += currentSourceValidRefundNum;
                insertParam.setSourceValidRefundNum(currentSourceValidRefundNum);

                // 有效退款金额
                long currentSourceValidRefundAmount = BigDecimal.valueOf(detail.getSourceValidRefundAmount()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceValidRefundAmountCumulative += currentSourceValidRefundAmount;
                insertParam.setSourceValidRefundAmount(currentSourceValidRefundAmount);

                // 结算依据
                long currentSourceSettlementBasis = BigDecimal.valueOf(detail.getSourceSettlementBasis()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceSettlementBasisCumulative += currentSourceSettlementBasis;
                insertParam.setSourceSettlementBasis(currentSourceSettlementBasis);

                // 应结算总金额
                long currentSourceSettlementAmount = BigDecimal.valueOf(detail.getSourceSettlementAmount()).multiply(sqbValidTradeAmountRatio).longValue();
                sourceSettlementAmountCumulative += currentSourceSettlementAmount;
                insertParam.setSourceSettlementAmount(currentSourceSettlementAmount);
                insertParam.setServiceProviderId(detail.getServiceProviderId());
                insertParam.setExtraParamsForMap(detail.getExtraParams());
                insertParamList.add(insertParam);
            } else { // 第 n 项
                TbBillOutputV2 insertParam = convertToTbBillOutputV2(detail, tbSqbBillMonthList.get(i), taskId, tradeMonth, billType);
                insertParam.setSourceValidTradeAmountRatio(BigDecimal.valueOf(1).subtract(sourceValidTradeAmountRatioCumulative).toPlainString());
                insertParam.setSourceValidTradeNum(detail.getSourceValidTradeNum() - sourceValidTradeNumCumulative); // 有效交易笔数
                insertParam.setSourceValidTradeAmount(detail.getSourceValidTradeAmount() - sourceValidTradeAmountCumulative);// 有效交易金额
                insertParam.setSourceValidRefundNum(detail.getSourceValidRefundNum() - sourceValidRefundNumCumulative); // 有效退款笔数
                insertParam.setSourceValidRefundAmount(detail.getSourceValidRefundAmount() - sourceValidRefundAmountCumulative); // 有效退款金额
                insertParam.setSourceSettlementBasis(detail.getSourceSettlementBasis() - sourceSettlementBasisCumulative); // 结算依据
                insertParam.setSourceSettlementAmount(detail.getSourceSettlementAmount() - sourceSettlementAmountCumulative); // 应结算总金额
                insertParam.setServiceProviderId(detail.getServiceProviderId());
                insertParam.setExtraParamsForMap(detail.getExtraParams());
                insertParam.setMerchantCommission(detail.getMerchantCommission());

                insertParamList.add(insertParam);
            }
        }
        insertParamList.get(0).compensateParamFromSourceBillDetail(detail);

        return insertParamList;
    }

    /**
     * 拆分上游账单：按账期内收钱吧商户数平均拆分
     *
     * @param taskId             任务id
     * @param tradeMonth         账单月份
     * @param billType           账单类型
     * @param tbSqbBillMonthList 收钱吧账单列表
     * @param detail             上游账单行详情（被拆分者）
     * @return 拆分后的上游账单列表
     */
    @Deprecated
    private List<TbBillOutput> splitSourceBillBySqbMerchantNum(int taskId,
                                                               Date tradeMonth,
                                                               int billType,
                                                               List<TbSqbBillMonth> tbSqbBillMonthList,
                                                               SourceBill.Detail detail) {
        List<TbBillOutput> insertParamList = new ArrayList<>();

        // 收钱吧账单为空，则直接填入一个默认值
        if (CollectionUtils.isEmpty(tbSqbBillMonthList)) {
            TbBillOutput insertParam = convertToTbBillOutput(detail, new TbSqbBillMonth(), taskId, tradeMonth, billType);
            insertParam.setSourceValidTradeAmountRatio("1");
            insertParam.setExtraParams(detail.getExtraParams());
            if (needQueryMerchantSnTypeList.contains(billType)) {
                MerchantParamReq merchantParamReq = new MerchantParamReq();
                merchantParamReq.setPay_merchant_id(detail.getSourceMerchantId());
                try {
                    List<MerchantProviderParamsDto> merchantProviderParams = merchantProviderParamsService.getMerchantProviderParams(merchantParamReq);
                    if (CollectionUtils.isNotEmpty(merchantProviderParams)) {
                        insertParam.setSqbMerchantSn(merchantProviderParams.get(0).getMerchant_sn());
                    }
                } catch (Exception e) {
                    log.warn("获取商户交易参数异常, sourceMerchantId:{}", detail.getSourceMerchantId(), e);
                }
            }
            insertParamList.add(insertParam);
            return insertParamList;
        }

        int sqbMerchantNum = tbSqbBillMonthList.size(); // 对应收钱吧商户量

        // 收钱吧每条交易占比（精确度：0.0001）
        BigDecimal sourceValidTradeAmountRatioAvg = BigDecimal.valueOf(1).divide(BigDecimal.valueOf(sqbMerchantNum), 4, RoundingMode.FLOOR);

        // 平均值
        int sourceValidTradeNumAvg = BigDecimal.valueOf(detail.getSourceValidTradeNum()).multiply(sourceValidTradeAmountRatioAvg).intValue();
        long sourceValidTradeAmountAvg = BigDecimal.valueOf(detail.getSourceValidTradeAmount()).multiply(sourceValidTradeAmountRatioAvg).longValue();
        int sourceValidRefundNumAvg = BigDecimal.valueOf(detail.getSourceValidRefundNum()).multiply(sourceValidTradeAmountRatioAvg).intValue();
        long sourceValidRefundAmountAvg = BigDecimal.valueOf(detail.getSourceValidRefundAmount()).multiply(sourceValidTradeAmountRatioAvg).longValue();
        long sourceSettlementBasisAvg = BigDecimal.valueOf(detail.getSourceSettlementBasis()).multiply(sourceValidTradeAmountRatioAvg).longValue();
        long sourceSettlementAmountAvg = BigDecimal.valueOf(detail.getSourceSettlementAmount()).multiply(sourceValidTradeAmountRatioAvg).longValue();

        for (int i = 0; i < sqbMerchantNum; i++) {
            // 前 n - 1 项
            TbBillOutput insertParam = convertToTbBillOutput(detail, tbSqbBillMonthList.get(i), taskId, tradeMonth, billType);
            if (i < sqbMerchantNum - 1) {
                insertParam.setSourceValidTradeAmountRatio(sourceValidTradeAmountRatioAvg.toPlainString());
                insertParam.setSourceValidTradeNum(sourceValidTradeNumAvg); // 有效交易笔数
                insertParam.setSourceValidTradeAmount(sourceValidTradeAmountAvg);// 有效交易金额
                insertParam.setSourceValidRefundNum(sourceValidRefundNumAvg); // 有效退款笔数
                insertParam.setSourceValidRefundAmount(sourceValidRefundAmountAvg); // 有效退款金额
                insertParam.setSourceSettlementBasis(sourceSettlementBasisAvg); // 结算依据
                insertParam.setSourceSettlementAmount(sourceSettlementAmountAvg); // 应结算总金额
                insertParam.setServiceProviderId(detail.getServiceProviderId());
            } else { // 第 n 项
                insertParam.setSourceValidTradeAmountRatio(BigDecimal.valueOf(1).subtract(sourceValidTradeAmountRatioAvg.multiply(BigDecimal.valueOf(i))).toPlainString());
                insertParam.setSourceValidTradeNum(detail.getSourceValidTradeNum() - (sourceValidTradeNumAvg * i)); // 有效交易笔数
                insertParam.setSourceValidTradeAmount(detail.getSourceValidTradeAmount() - (sourceValidTradeAmountAvg * i));// 有效交易金额
                insertParam.setSourceValidRefundNum(detail.getSourceValidRefundNum() - (sourceValidRefundNumAvg * i)); // 有效退款笔数
                insertParam.setSourceValidRefundAmount(detail.getSourceValidRefundAmount() - (sourceValidRefundAmountAvg * i)); // 有效退款金额
                insertParam.setSourceSettlementBasis(detail.getSourceSettlementBasis() - (sourceSettlementBasisAvg * i)); // 结算依据
                insertParam.setSourceSettlementAmount(detail.getSourceSettlementAmount() - (sourceSettlementAmountAvg * i)); // 应结算总金额
                insertParam.setServiceProviderId(detail.getServiceProviderId());
            }
            insertParam.setExtraParams(detail.getExtraParams());
            insertParamList.add(insertParam);
        }

        return insertParamList;
    }

    private List<TbBillOutputV2> splitSourceBillBySqbMerchantNumV2(int taskId,
                                                                   Date tradeMonth,
                                                                   int billType,
                                                                   List<TbSqbBillMonth> tbSqbBillMonthList,
                                                                   SourceBill.Detail detail) {
        List<TbBillOutputV2> insertParamList = new ArrayList<>();

        // 收钱吧账单为空，则直接填入一个默认值
        if (CollectionUtils.isEmpty(tbSqbBillMonthList)) {
            TbBillOutputV2 insertParam = convertToTbBillOutputV2(detail, new TbSqbBillMonth(), taskId, tradeMonth, billType);
            insertParam.setSourceValidTradeAmountRatio("1");
            insertParam.setExtraParamsForMap(detail.getExtraParams());
            if (needQueryMerchantSnTypeList.contains(billType)) {
                MerchantParamReq merchantParamReq = new MerchantParamReq();
                merchantParamReq.setPay_merchant_id(detail.getSourceMerchantId());
                try {
                    List<MerchantProviderParamsDto> merchantProviderParams = merchantProviderParamsService.getMerchantProviderParams(merchantParamReq);
                    if (CollectionUtils.isNotEmpty(merchantProviderParams)) {
                        insertParam.setSqbMerchantSn(merchantProviderParams.get(0).getMerchant_sn());
                    }
                } catch (Exception e) {
                    log.warn("获取商户交易参数异常, sourceMerchantId:{}", detail.getSourceMerchantId(), e);
                }
            }
            insertParamList.add(insertParam);
            insertParamList.get(0).compensateParamFromSourceBillDetail(detail);
            return insertParamList;
        }

        int sqbMerchantNum = tbSqbBillMonthList.size(); // 对应收钱吧商户量

        // 收钱吧每条交易占比（精确度：0.0001）
        BigDecimal sourceValidTradeAmountRatioAvg = BigDecimal.valueOf(1).divide(BigDecimal.valueOf(sqbMerchantNum), 4, RoundingMode.FLOOR);

        // 平均值
        int sourceValidTradeNumAvg = BigDecimal.valueOf(detail.getSourceValidTradeNum()).multiply(sourceValidTradeAmountRatioAvg).intValue();
        long sourceValidTradeAmountAvg = BigDecimal.valueOf(detail.getSourceValidTradeAmount()).multiply(sourceValidTradeAmountRatioAvg).longValue();
        int sourceValidRefundNumAvg = BigDecimal.valueOf(detail.getSourceValidRefundNum()).multiply(sourceValidTradeAmountRatioAvg).intValue();
        long sourceValidRefundAmountAvg = BigDecimal.valueOf(detail.getSourceValidRefundAmount()).multiply(sourceValidTradeAmountRatioAvg).longValue();
        long sourceSettlementBasisAvg = BigDecimal.valueOf(detail.getSourceSettlementBasis()).multiply(sourceValidTradeAmountRatioAvg).longValue();
        long sourceSettlementAmountAvg = BigDecimal.valueOf(detail.getSourceSettlementAmount()).multiply(sourceValidTradeAmountRatioAvg).longValue();

        for (int i = 0; i < sqbMerchantNum; i++) {
            // 前 n - 1 项
            TbBillOutputV2 insertParam = convertToTbBillOutputV2(detail, tbSqbBillMonthList.get(i), taskId, tradeMonth, billType);
            if (i < sqbMerchantNum - 1) {
                insertParam.setSourceValidTradeAmountRatio(sourceValidTradeAmountRatioAvg.toPlainString());
                insertParam.setSourceValidTradeNum(sourceValidTradeNumAvg); // 有效交易笔数
                insertParam.setSourceValidTradeAmount(sourceValidTradeAmountAvg);// 有效交易金额
                insertParam.setSourceValidRefundNum(sourceValidRefundNumAvg); // 有效退款笔数
                insertParam.setSourceValidRefundAmount(sourceValidRefundAmountAvg); // 有效退款金额
                insertParam.setSourceSettlementBasis(sourceSettlementBasisAvg); // 结算依据
                insertParam.setSourceSettlementAmount(sourceSettlementAmountAvg); // 应结算总金额
                insertParam.setServiceProviderId(detail.getServiceProviderId());
            } else { // 第 n 项
                insertParam.setSourceValidTradeAmountRatio(BigDecimal.valueOf(1).subtract(sourceValidTradeAmountRatioAvg.multiply(BigDecimal.valueOf(i))).toPlainString());
                insertParam.setSourceValidTradeNum(detail.getSourceValidTradeNum() - (sourceValidTradeNumAvg * i)); // 有效交易笔数
                insertParam.setSourceValidTradeAmount(detail.getSourceValidTradeAmount() - (sourceValidTradeAmountAvg * i));// 有效交易金额
                insertParam.setSourceValidRefundNum(detail.getSourceValidRefundNum() - (sourceValidRefundNumAvg * i)); // 有效退款笔数
                insertParam.setSourceValidRefundAmount(detail.getSourceValidRefundAmount() - (sourceValidRefundAmountAvg * i)); // 有效退款金额
                insertParam.setSourceSettlementBasis(detail.getSourceSettlementBasis() - (sourceSettlementBasisAvg * i)); // 结算依据
                insertParam.setSourceSettlementAmount(detail.getSourceSettlementAmount() - (sourceSettlementAmountAvg * i)); // 应结算总金额
                insertParam.setServiceProviderId(detail.getServiceProviderId());
            }
            insertParam.setExtraParamsForMap(detail.getExtraParams());
            insertParamList.add(insertParam);

            insertParamList.get(0).compensateParamFromSourceBillDetail(detail);
        }

        return insertParamList;
    }

    /**
     * 支付宝直连账单匹配
     *
     * @param taskId     任务id
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForAlipayDirect(int taskId,
                                                       String tradeMonth,
                                                       List<SourceBill.Detail> detailList, int tbBillType) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            List<TbSqbBillMonth> tbSqbBillMonthList;
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            // 当上游账单门店ID/二级商户号不为空时，取上游账单的商户PID、门店ID/二级商户号 去匹配收钱吧侧的直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid、外部门店号=门店ID/二级商户号）
            if (StringUtils.isNotEmpty(detail.getSourceLevel2MerchantId())) {
                example.or()
                        .andBillTypeEqualTo(tbBillType)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId())
                        .andDimensionEqualTo(detail.getSourceLevel2MerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

                // 如根据商户PID、门店ID/二级商户号查询不到收钱吧侧交易，则用商户PID去匹配收钱侧直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid）
                if (CollectionUtils.isEmpty(tbSqbBillMonthList)) {
                    example = new TbSqbBillMonthExample();
                    example.or()
                            .andBillTypeEqualTo(tbBillType)
                            .andTradeMonthEqualTo(tradeMonthWithLine)
                            .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                    tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
                }
            } else {
                // 当上游账单门店ID/二级商户号为空，则用商户PID去匹配收钱侧直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid）。
                example.or()
                        .andBillTypeEqualTo(tbBillType)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
            }
            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), tbBillType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    /**
     * 支付宝直连账单匹配
     *
     * @param taskId     任务id
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    public List<TbBillOutputV2> billMatchForAlipayDirectV2(int taskId,
                                                           String tradeMonth,
                                                           List<SourceBill.Detail> detailList, int tbBillType) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            List<TbSqbBillMonth> tbSqbBillMonthList;
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            // 当上游账单门店ID/二级商户号不为空时，取上游账单的商户PID、门店ID/二级商户号 去匹配收钱吧侧的直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid、外部门店号=门店ID/二级商户号）
            if (StringUtils.isNotEmpty(detail.getSourceLevel2MerchantId())) {
                example.or()
                        .andBillTypeEqualTo(tbBillType)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId())
                        .andDimensionEqualTo(detail.getSourceLevel2MerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

                // 如根据商户PID、门店ID/二级商户号查询不到收钱吧侧交易，则用商户PID去匹配收钱侧直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid）
                if (CollectionUtils.isEmpty(tbSqbBillMonthList)) {
                    example = new TbSqbBillMonthExample();
                    example.or()
                            .andBillTypeEqualTo(tbBillType)
                            .andTradeMonthEqualTo(tradeMonthWithLine)
                            .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                    tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
                }
            } else {
                // 当上游账单门店ID/二级商户号为空，则用商户PID去匹配收钱侧直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid）。
                example.or()
                        .andBillTypeEqualTo(tbBillType)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
            }
            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), tbBillType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    /**
     * 支付宝口碑账单匹配
     *
     * @param taskId     任务id
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    public List<TbBillOutput> billMatchForAlipayKoubei(int taskId,
                                                       String tradeMonth,
                                                       List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            List<TbSqbBillMonth> tbSqbBillMonthList;
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            //  当上游账单门店ID/二级商户号不为空，取上游账单的商户PID、门店ID/二级商户号 去匹配收钱吧侧的直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid、外部门店号=门店ID/二级商户号）
            if (StringUtils.isNotEmpty(detail.getSourceLevel2MerchantId())) {
                example.or()
                        .andBillTypeEqualTo(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId())
                        .andDimensionEqualTo(detail.getSourceLevel2MerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

                // 如根据商户PID、门店ID/二级商户号查询不到收钱吧侧交易，则用商户PID去匹配收钱侧直连支付宝交易（汇总后的口碑）（条件：收钱吧流水中的pid=上游商户pid）
                if (CollectionUtils.isEmpty(tbSqbBillMonthList)) {
                    example = new TbSqbBillMonthExample();
                    example.or()
                            .andBillTypeEqualTo(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE)
                            .andTradeMonthEqualTo(tradeMonthWithLine)
                            .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                    tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
                }
            } else {
                // 当上游账单门店ID/二级商户号为空，则用商户PID去匹配收钱侧直连支付宝交易（（条件：收钱吧流水中的pid=上游商户pid）
                example.or()
                        .andBillTypeEqualTo(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
            }

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), SourceBillType.TYPE_ALIPAY_KOUBEI, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    public List<TbBillOutputV2> billMatchForAlipayKoubeiV2(int taskId,
                                                           String tradeMonth,
                                                           List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            List<TbSqbBillMonth> tbSqbBillMonthList;
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            //  当上游账单门店ID/二级商户号不为空，取上游账单的商户PID、门店ID/二级商户号 去匹配收钱吧侧的直连支付宝交易（条件：收钱吧流水中的pid=上游商户pid、外部门店号=门店ID/二级商户号）
            if (StringUtils.isNotEmpty(detail.getSourceLevel2MerchantId())) {
                example.or()
                        .andBillTypeEqualTo(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId())
                        .andDimensionEqualTo(detail.getSourceLevel2MerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

                // 如根据商户PID、门店ID/二级商户号查询不到收钱吧侧交易，则用商户PID去匹配收钱侧直连支付宝交易（汇总后的口碑）（条件：收钱吧流水中的pid=上游商户pid）
                if (CollectionUtils.isEmpty(tbSqbBillMonthList)) {
                    example = new TbSqbBillMonthExample();
                    example.or()
                            .andBillTypeEqualTo(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE)
                            .andTradeMonthEqualTo(tradeMonthWithLine)
                            .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                    tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
                }
            } else {
                // 当上游账单门店ID/二级商户号为空，则用商户PID去匹配收钱侧直连支付宝交易（（条件：收钱吧流水中的pid=上游商户pid）
                example.or()
                        .andBillTypeEqualTo(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE)
                        .andTradeMonthEqualTo(tradeMonthWithLine)
                        .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
                tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);
            }

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), SourceBillType.TYPE_ALIPAY_KOUBEI, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    /**
     * 支付宝花呗账单匹配
     *
     * @param taskId     任务id
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @param sourceType
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForAlipayHuabei(int taskId,
                                                       String tradeMonth,
                                                       List<SourceBill.Detail> detailList, int sourceType) {
        List<TbBillOutput> tbBillOutputListResult = new ArrayList<>();

        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        Date tradeMonthDate = yyyymmToDate(tradeMonth);

//        // 获取支付宝直连账单列表
//        List<TbSqbBillMonth> alipayDirectList = this.tbSqbBillMonthMapper.listSqbBillMonth(tradeMonthWithLine, SourceBillType.TYPE_ALIPAY_DIRECT_TRADE, null, null);
//        Map<String, TbSqbBillMonth> alipayDirectListMap = alipayDirectList.stream().collect(Collectors.toMap(TbSqbBillMonth::getSourceMerchantId, Function.identity(), (key1, key2) -> key1)); // key = 上游商户id，value = TbSqbBillMonth
//        // 获取支付宝账单列表
        int alipayType;
        if (sourceType == SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT) {
            alipayType = SourceBillType.TYPE_ALIPAY_HUABEI_FQ_INDIRECT;
        } else {
            alipayType = SourceBillType.TYPE_ALIPAY_HUABEI_FQ_DIRECT;
        }
        // 找出收钱吧账单。
        List<TbSqbBillMonth> alipayIndirectList = this.getSqbBillList(tradeMonthWithLine, alipayType);
        //根据merchantId 分组
        Map<String, List<TbSqbBillMonth>> alipayIndirectListMap = alipayIndirectList.stream().collect(Collectors.groupingBy(TbSqbBillMonth::getSourceMerchantId)); // key = 上游商户id，value = TbSqbBillMonth

        // 循环上游账单列表
        for (SourceBill.Detail detail : detailList) {
//            /*
//             * 判断间直间连商户：商户pid=2088621838709497 为间连账单明细，商户pid≠2088621838709497 为直连账单明细
//             * 如为直连账单明细：用商户PID去匹配出收钱侧收钱吧商户号即可，不需要匹配交易数据
//             * 如为间连账单明细：取上游账单的门店ID/二级商户id作为上游商户id去匹配出收钱侧收钱吧商户号即可，不需要匹配交易数据
//             */
//             取消这个判断
//            TbSqbBillMonth tbSqbBillMonth;
//            // 支付宝间联
//            if ("2088621838709497".equals(detail.getSourceMerchantId())) {
//                tbSqbBillMonth = alipayIndirectListMap.get(detail.getSourceLevel2MerchantId());
//            } else { // 支付宝直连
            List<TbSqbBillMonth> tbSqbBillMonth = alipayIndirectListMap.get(detail.getSourceMerchantId());
//            }

            if (tbSqbBillMonth == null) {
                tbSqbBillMonth = new ArrayList<>();
            }

            // 交易平均分配
            Collection<TbSqbBillMonth> collect = tbSqbBillMonth.stream()
                    .collect(Collectors.toMap(t -> t.getSourceMerchantId() + t.getSqbMerchantId(),
                            Function.identity(),
                            (t1, t2) -> {
                                t1.setSqbRefundMoney(t1.getSqbRefundMoney() + t2.getSqbRefundMoney());
                                t1.setSqbTradeMoney(t1.getSqbTradeMoney() + t2.getSqbTradeMoney());
                                t1.setSqbRefundNum(t1.getSqbRefundNum() + t2.getSqbRefundNum());
                                t1.setSqbTradeNum(t1.getSqbTradeNum() + t2.getSqbTradeNum());
                                return t1;
                            })).values();
            ArrayList<TbSqbBillMonth> sumTbSqbBillMonths = new ArrayList<>(collect);

            List<TbBillOutput> list = splitSourceBillBySqbMerchantNum(taskId, tradeMonthDate, sourceType,
                    sumTbSqbBillMonths, detail);
            tbBillOutputListResult.addAll(list);
        }

        return tbBillOutputListResult;
    }

    public List<TbBillOutputV2> billMatchForAlipayHuabeiV2(int taskId,
                                                           String tradeMonth,
                                                           List<SourceBill.Detail> detailList, int sourceType) {
        List<TbBillOutputV2> tbBillOutputListResult = new ArrayList<>();

        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        Date tradeMonthDate = yyyymmToDate(tradeMonth);

//        // 获取支付宝直连账单列表
//        List<TbSqbBillMonth> alipayDirectList = this.tbSqbBillMonthMapper.listSqbBillMonth(tradeMonthWithLine, SourceBillType.TYPE_ALIPAY_DIRECT_TRADE, null, null);
//        Map<String, TbSqbBillMonth> alipayDirectListMap = alipayDirectList.stream().collect(Collectors.toMap(TbSqbBillMonth::getSourceMerchantId, Function.identity(), (key1, key2) -> key1)); // key = 上游商户id，value = TbSqbBillMonth
//        // 获取支付宝账单列表
        int alipayType;
        if (sourceType == SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT) {
            alipayType = SourceBillType.TYPE_ALIPAY_HUABEI_FQ_INDIRECT;
        } else {
            alipayType = SourceBillType.TYPE_ALIPAY_HUABEI_FQ_DIRECT;
        }
        // 找出收钱吧账单。
        List<TbSqbBillMonth> alipayIndirectList = this.getSqbBillList(tradeMonthWithLine, alipayType);
        //根据merchantId 分组
        Map<String, List<TbSqbBillMonth>> alipayIndirectListMap = alipayIndirectList.stream().collect(Collectors.groupingBy(TbSqbBillMonth::getSourceMerchantId)); // key = 上游商户id，value = TbSqbBillMonth

        // 循环上游账单列表
        for (SourceBill.Detail detail : detailList) {
//            /*
//             * 判断间直间连商户：商户pid=2088621838709497 为间连账单明细，商户pid≠2088621838709497 为直连账单明细
//             * 如为直连账单明细：用商户PID去匹配出收钱侧收钱吧商户号即可，不需要匹配交易数据
//             * 如为间连账单明细：取上游账单的门店ID/二级商户id作为上游商户id去匹配出收钱侧收钱吧商户号即可，不需要匹配交易数据
//             */
//             取消这个判断
//            TbSqbBillMonth tbSqbBillMonth;
//            // 支付宝间联
//            if ("2088621838709497".equals(detail.getSourceMerchantId())) {
//                tbSqbBillMonth = alipayIndirectListMap.get(detail.getSourceLevel2MerchantId());
//            } else { // 支付宝直连
            List<TbSqbBillMonth> tbSqbBillMonth = alipayIndirectListMap.get(detail.getSourceMerchantId());
//            }

            if (tbSqbBillMonth == null) {
                tbSqbBillMonth = new ArrayList<>();
            }

            // 交易平均分配
            Collection<TbSqbBillMonth> collect = tbSqbBillMonth.stream()
                    .collect(Collectors.toMap(t -> t.getSourceMerchantId() + t.getSqbMerchantId(),
                            Function.identity(),
                            (t1, t2) -> {
                                t1.setSqbRefundMoney(t1.getSqbRefundMoney() + t2.getSqbRefundMoney());
                                t1.setSqbTradeMoney(t1.getSqbTradeMoney() + t2.getSqbTradeMoney());
                                t1.setSqbRefundNum(t1.getSqbRefundNum() + t2.getSqbRefundNum());
                                t1.setSqbTradeNum(t1.getSqbTradeNum() + t2.getSqbTradeNum());
                                return t1;
                            })).values();
            ArrayList<TbSqbBillMonth> sumTbSqbBillMonths = new ArrayList<>(collect);

            List<TbBillOutputV2> list = splitSourceBillBySqbMerchantNumV2(taskId, tradeMonthDate, sourceType,
                    sumTbSqbBillMonths, detail);
            tbBillOutputListResult.addAll(list);
        }

        return tbBillOutputListResult;
    }

    /**
     * 获取类型获取sqb账单列表
     *
     * @param tradeMonthWithLine
     * @return
     */
    public List<TbSqbBillMonth> getSqbBillList(String tradeMonthWithLine, int sourceBillType) {
        List<TbSqbBillMonth> alipayIndirectList = new ArrayList<>();
        int offset = 0;
        int size = 50000; // 每次获取 5W 条数据
        List<TbSqbBillMonth> part;
        do {
            part = this.tbSqbBillMonthMapper.listSqbBillMonth(tradeMonthWithLine, sourceBillType, offset, size);
            alipayIndirectList.addAll(part);
            offset += size;
        } while (CollectionUtils.isNotEmpty(part));

        return alipayIndirectList;
    }


    /**
     * 支付宝校园账单匹配
     *
     * @param taskId     任务id
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForAlipaySchool(int taskId,
                                                       String tradeMonth,
                                                       int sourceBillType,
                                                       List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的PID去匹配收钱吧侧的交易
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            example.or()
                    .andBillTypeIn(Arrays.asList(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE, SourceBillType.TYPE_ALIPAY_INDIRECT_TRADE)) // 支付宝直连和间连
                    .andTradeMonthEqualTo(tradeMonthWithLine)
                    .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
            List<TbSqbBillMonth> tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配（对于结算依据是笔数的，也按照交易量占比拆分）
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), sourceBillType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    public List<TbBillOutputV2> billMatchForAlipaySchoolV2(int taskId,
                                                           String tradeMonth,
                                                           int sourceBillType,
                                                           List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的PID去匹配收钱吧侧的交易
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            example.or()
                    .andBillTypeIn(Arrays.asList(SourceBillType.TYPE_ALIPAY_DIRECT_TRADE, SourceBillType.TYPE_ALIPAY_INDIRECT_TRADE)) // 支付宝直连和间连
                    .andTradeMonthEqualTo(tradeMonthWithLine)
                    .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
            List<TbSqbBillMonth> tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配（对于结算依据是笔数的，也按照交易量占比拆分）
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), sourceBillType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    /**
     * 微信直连账单匹配
     *
     * @param taskId     任务id
     * @param billType   账单类型
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForWeixinDirect(int taskId,
                                                       int billType,
                                                       String tradeMonth,
                                                       List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            example.or()
                    .andBillTypeEqualTo(billType)
                    .andTradeMonthEqualTo(tradeMonthWithLine)
                    .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
            List<TbSqbBillMonth> tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    public List<TbBillOutputV2> billMatchForWeixinDirectV2(int taskId,
                                                           int billType,
                                                           String tradeMonth,
                                                           List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            example.or()
                    .andBillTypeEqualTo(billType)
                    .andTradeMonthEqualTo(tradeMonthWithLine)
                    .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
            List<TbSqbBillMonth> tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    /**
     * 微信间连A账单匹配
     *
     * @param taskId     任务id
     * @param billType   账单类型
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForWechatIndirect_A(int taskId,
                                                           int billType,
                                                           String tradeMonth,
                                                           List<SourceBill.Detail> detailList) {
        // 目前处理规则同微信直连
        return this.billMatchForWeixinDirect(taskId, billType, tradeMonth, detailList);
    }

    public List<TbBillOutputV2> billMatchForWechatIndirect_AV2(int taskId,
                                                               int billType,
                                                               String tradeMonth,
                                                               List<SourceBill.Detail> detailList) {
        // 目前处理规则同微信直连
        return this.billMatchForWeixinDirectV2(taskId, billType, tradeMonth, detailList);
    }

    /**
     * 微信校园账单匹配
     *
     * @param taskId     任务id
     * @param billType   账单类型
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForWeixinSchool(int taskId,
                                                       int billType,
                                                       String tradeMonth,
                                                       List<SourceBill.Detail> detailList) {
        // 目前处理规则同微信直连
        return this.billMatchForWeixinDirect(taskId, billType, tradeMonth, detailList);
    }

    public List<TbBillOutputV2> billMatchForWeixinSchoolV2(int taskId,
                                                           int billType,
                                                           String tradeMonth,
                                                           List<SourceBill.Detail> detailList) {
        // 目前处理规则同微信直连
        return this.billMatchForWeixinDirectV2(taskId, billType, tradeMonth, detailList);
    }

    /**
     * 微信公立医院账单匹配
     *
     * @param taskId     任务id
     * @param billType   账单类型
     * @param tradeMonth 账单月份，格式：yyyymm
     * @param detailList 解析后上游账单列表
     * @return 匹配后的账单列表
     */
    @Deprecated
    public List<TbBillOutput> billMatchForWeixinPublicHospital(int taskId,
                                                               int billType,
                                                               String tradeMonth,
                                                               List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            example.or()
                    .andBillTypeEqualTo(SourceBillType.TYPE_WEIXIN_DIRECT_TRADE) // 去匹配收钱吧侧微信直连的账单！！！
                    .andTradeMonthEqualTo(tradeMonthWithLine)
                    .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
            List<TbSqbBillMonth> tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    public List<TbBillOutputV2> billMatchForWeixinPublicHospitalV2(int taskId,
                                                                   int billType,
                                                                   String tradeMonth,
                                                                   List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            TbSqbBillMonthExample example = new TbSqbBillMonthExample();
            example.or()
                    .andBillTypeEqualTo(SourceBillType.TYPE_WEIXIN_DIRECT_TRADE) // 去匹配收钱吧侧微信直连的账单！！！
                    .andTradeMonthEqualTo(tradeMonthWithLine)
                    .andSourceMerchantIdEqualTo(detail.getSourceMerchantId());
            List<TbSqbBillMonth> tbSqbBillMonthList = this.tbSqbBillMonthMapper.selectByExample(example);

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    /**
     * 将上游商户账单和对应收钱吧账单的字段拼接在一起
     *
     * @param detail         上游账单行明细
     * @param tbSqbBillMonth 收钱吧商户账单：这里的收钱吧清算笔数和清算金额是没有的，需要在本方法中计算
     * @return 输出账单
     */
    @Deprecated
    private static TbBillOutput convertToTbBillOutput(SourceBill.Detail detail, TbSqbBillMonth tbSqbBillMonth, int taskId, Date tradeMonth, int sourceBillType) {
        TbBillOutput insertParam = new TbBillOutput();
        // 基础信息
        insertParam.setSourceBillInputTaskId(taskId);
        insertParam.setTradeDateMonth(tradeMonth);
        insertParam.setSourceBillType(sourceBillType);

        insertParam.setSubNum(detail.getSubNum());

        // 商户id
        // 如果是支付宝花呗间联，则把二级商户信息做为商户信息
        if (SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT == sourceBillType && "2088621838709497".equals(detail.getSourceMerchantId())) {
            insertParam.setSourceMerchantId(detail.getSourceLevel2MerchantId());
            insertParam.setSourceMerchantName(detail.getSourceLevel2MerchantName());
        } else {
            insertParam.setSourceMerchantId(detail.getSourceMerchantId());
            insertParam.setSourceMerchantName(detail.getSourceMerchantName());
            insertParam.setSourceLevel2MerchantId(detail.getSourceLevel2MerchantId());
            insertParam.setSourceLevel2MerchantName(detail.getSourceLevel2MerchantName());
        }
        insertParam.setSqbMerchantId(tbSqbBillMonth.getSqbMerchantId());
        insertParam.setSqbMerchantSn(tbSqbBillMonth.getSqbMerchantSn());

        // 维度（度量）
        insertParam.setDimension(detail.getDimension());

        // 上游交易信息（注意拆分计算情况）
        insertParam.setSourceValidTradeNum(detail.getSourceValidTradeNum());
        insertParam.setSourceValidTradeAmount(detail.getSourceValidTradeAmount());
        insertParam.setSourceValidRefundNum(detail.getSourceValidRefundNum());
        insertParam.setSourceValidRefundAmount(detail.getSourceValidRefundAmount());

        // 有效交易金额占比 设置默认 0
        if (insertParam.getSourceValidTradeAmountRatio() == null) {
            insertParam.setSourceValidTradeAmountRatio("0.0");
        }

        // 上游结算信息
        insertParam.setSourceSettlementBasisType(detail.getSourceSettlementBasisType());
        insertParam.setSourceSettlementBasis(detail.getSourceSettlementBasis());
        insertParam.setSourceMerchantFeeRate(detail.getSourceMerchantFeeRate());
        insertParam.setSourceSettlementFeeRate(detail.getSourceSettlementFeeRate());
        insertParam.setSourceSettlementAmount(detail.getSourceSettlementAmount());
        insertParam.setCappingRate(detail.getCappingRate());

        // 收钱吧交易信息
        insertParam.setSqbTradeNum(tbSqbBillMonth.getSqbTradeNum() != null ? tbSqbBillMonth.getSqbTradeNum() : 0);
        insertParam.setSqbTradeAmount(tbSqbBillMonth.getSqbTradeMoney() != null ? tbSqbBillMonth.getSqbTradeMoney() : 0L);
        insertParam.setSqbRefundNum(tbSqbBillMonth.getSqbRefundNum() != null ? tbSqbBillMonth.getSqbRefundNum() : 0);
        insertParam.setSqbRefundAmount(tbSqbBillMonth.getSqbRefundMoney() != null ? tbSqbBillMonth.getSqbRefundMoney() : 0L);
        // 清算笔数 = 有效交易笔数 - 有效退款笔数
        insertParam.setSqbClearingNum(insertParam.getSqbTradeNum() - insertParam.getSqbRefundNum());
        // 清算金额 = 有效交易金额 - 有效退款金额
        insertParam.setSqbClearingAmount(insertParam.getSqbTradeAmount() - insertParam.getSqbRefundAmount());
        insertParam.setRemark(detail.getRemark());
        insertParam.setServiceProviderId(detail.getServiceProviderId());

        return insertParam;
    }

    /**
     * 将上游商户账单和对应收钱吧账单的字段拼接在一起
     *
     * @param detail         上游账单行明细
     * @param tbSqbBillMonth 收钱吧商户账单：这里的收钱吧清算笔数和清算金额是没有的，需要在本方法中计算
     * @return 输出账单
     */
    private TbBillOutputV2 convertToTbBillOutputV2(SourceBill.Detail detail, TbSqbBillMonth tbSqbBillMonth, int taskId, Date tradeMonth, Integer sourceBillType) {
        TbBillOutputV2 insertParam = new TbBillOutputV2();
        // 基础信息
        insertParam.setSourceBillInputTaskId(taskId);
        insertParam.setTradeDateMonth(tradeMonth);
        insertParam.setSourceBillType(sourceBillType);

        insertParam.setSubNum(detail.getSubNum());

        // 商户id
        // 如果是支付宝花呗间联，则把二级商户信息做为商户信息
        if (Objects.equals(SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT, sourceBillType) && "2088621838709497".equals(detail.getSourceMerchantId())) {
            insertParam.setSourceMerchantId(detail.getSourceLevel2MerchantId());
            insertParam.setSourceMerchantName(detail.getSourceLevel2MerchantName());
        } else {
            insertParam.setSourceMerchantId(detail.getSourceMerchantId());
            insertParam.setSourceMerchantName(detail.getSourceMerchantName());
            insertParam.setSourceLevel2MerchantId(detail.getSourceLevel2MerchantId());
            insertParam.setSourceLevel2MerchantName(detail.getSourceLevel2MerchantName());
        }
        if (StringUtils.isNotEmpty(tbSqbBillMonth.getSqbMerchantId())) {
            insertParam.setSqbMerchantId(tbSqbBillMonth.getSqbMerchantId());
            insertParam.setSqbMerchantSn(tbSqbBillMonth.getSqbMerchantSn());
        } else if (StringUtils.isNotEmpty(detail.getSqbMerchantSn())) {
            Map merchant = merchantService.getMerchantBySn(detail.getSqbMerchantSn());
            if (merchant != null) {
                insertParam.setSqbMerchantSn(detail.getSqbMerchantSn());
            }
        }

        // 维度（度量）
        insertParam.setDimension(detail.getDimension());

        // 上游交易信息（注意拆分计算情况）
        insertParam.setSourceValidTradeNum(detail.getSourceValidTradeNum());
        insertParam.setSourceValidTradeAmount(detail.getSourceValidTradeAmount());
        insertParam.setSourceValidRefundNum(detail.getSourceValidRefundNum());
        insertParam.setSourceValidRefundAmount(detail.getSourceValidRefundAmount());

        // 有效交易金额占比 设置默认 0
        if (insertParam.getSourceValidTradeAmountRatio() == null) {
            insertParam.setSourceValidTradeAmountRatio("0.0");
        }

        // 上游结算信息
        insertParam.setSourceSettlementBasisType(detail.getSourceSettlementBasisType());
        insertParam.setSourceSettlementBasis(detail.getSourceSettlementBasis());
        insertParam.setSourceMerchantFeeRate(detail.getSourceMerchantFeeRate());
        insertParam.setSourceSettlementFeeRate(detail.getSourceSettlementFeeRate());
        insertParam.setSourceSettlementAmount(detail.getSourceSettlementAmount());
        insertParam.setCappingRate(detail.getCappingRate());

        // 收钱吧交易信息
        insertParam.setSqbTradeNum(tbSqbBillMonth.getSqbTradeNum() != null ? tbSqbBillMonth.getSqbTradeNum() : 0);
        insertParam.setSqbTradeAmount(tbSqbBillMonth.getSqbTradeMoney() != null ? tbSqbBillMonth.getSqbTradeMoney() : 0L);
        insertParam.setSqbRefundNum(tbSqbBillMonth.getSqbRefundNum() != null ? tbSqbBillMonth.getSqbRefundNum() : 0);
        insertParam.setSqbRefundAmount(tbSqbBillMonth.getSqbRefundMoney() != null ? tbSqbBillMonth.getSqbRefundMoney() : 0L);
        // 清算笔数 = 有效交易笔数 - 有效退款笔数
        insertParam.setSqbClearingNum(insertParam.getSqbTradeNum() - insertParam.getSqbRefundNum());
        // 清算金额 = 有效交易金额 - 有效退款金额
        insertParam.setSqbClearingAmount(insertParam.getSqbTradeAmount() - insertParam.getSqbRefundAmount());
        insertParam.setRemark(detail.getRemark());
        insertParam.setServiceProviderId(detail.getServiceProviderId());
        insertParam.setMerchantCommission(detail.getMerchantCommission());

        return insertParam;
    }

    @Deprecated
    public List<TbBillOutput> billMatchForWeixinOasis(int taskId, int billType, String tradeMonth, List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();

        // 获取支付宝蓝海账单列表
        List<TbSqbBillMonth> alipayLanhaiList = this.getSqbBillList(tradeMonthWithLine, SourceBillType.TYPE_WECHAT_OASIS_TRADE);
        Multimap<String, TbSqbBillMonth> sqbList = alipayLanhaiList.stream()
                .collect(() -> {
                    Multimap<String, TbSqbBillMonth> map = ArrayListMultimap.create();
                    return map;
                }, (map, tbSqbBillMonth) -> {
                    map.put(tbSqbBillMonth.getSourceMerchantId(), tbSqbBillMonth);
                }, (stringTbSqbBillMonthMultimap, stringTbSqbBillMonthMultimap2) -> {
                });

        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            List<TbSqbBillMonth> tbSqbBillMonthList = Lists.newArrayList(sqbList.get(detail.getSourceMerchantId()));

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    public List<TbBillOutputV2> billMatchForWeixinOasisV2(int taskId, int billType, String tradeMonth, List<SourceBill.Detail> detailList) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();

        // 获取支付宝蓝海账单列表
        List<TbSqbBillMonth> alipayLanhaiList = this.getSqbBillList(tradeMonthWithLine, SourceBillType.TYPE_WECHAT_OASIS_TRADE);
        Multimap<String, TbSqbBillMonth> sqbList = alipayLanhaiList.stream()
                .collect(() -> {
                    Multimap<String, TbSqbBillMonth> map = ArrayListMultimap.create();
                    return map;
                }, (map, tbSqbBillMonth) -> {
                    map.put(tbSqbBillMonth.getSourceMerchantId(), tbSqbBillMonth);
                }, (stringTbSqbBillMonthMultimap, stringTbSqbBillMonthMultimap2) -> {
                });

        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            List<TbSqbBillMonth> tbSqbBillMonthList = Lists.newArrayList(sqbList.get(detail.getSourceMerchantId()));

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;
    }

    @Deprecated
    public List<TbBillOutput> billMatchSplitSqbTradeAmount(int taskId, int billType, String tradeMonth, List<SourceBill.Detail> detailList,
                                                           int sourceBillType) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
//        Date tradeMonthDate = yyyymmToDate(tradeMonth);

        // 获取指定类型的账单列表
        List<TbSqbBillMonth> sqbBillList = this.getSqbBillList(tradeMonthWithLine, sourceBillType);

        Map<String, List<TbSqbBillMonth>> collect = sqbBillList.stream()
                .collect(Collectors.groupingBy(TbSqbBillMonth::getSourceMerchantId));
        // key = 上游商户id，value = TbSqbBillMonth

        List<TbBillOutput> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            List<TbSqbBillMonth> tbSqbBillMonthList = Optional.ofNullable(collect.get(detail.getSourceMerchantId())).orElse(new ArrayList<>());

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatio(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;

    }

    public List<TbBillOutputV2> billMatchSplitSqbTradeAmountV2(int taskId, int billType, String tradeMonth, List<SourceBill.Detail> detailList,
                                                               int sourceBillType) {
        String tradeMonthWithLine = yyyymmWithLine(tradeMonth);
//        Date tradeMonthDate = yyyymmToDate(tradeMonth);

        // 获取指定类型的账单列表
        List<TbSqbBillMonth> sqbBillList = this.getSqbBillList(tradeMonthWithLine, sourceBillType);

        Map<String, List<TbSqbBillMonth>> collect = sqbBillList.stream()
                .collect(Collectors.groupingBy(TbSqbBillMonth::getSourceMerchantId));
        // key = 上游商户id，value = TbSqbBillMonth

        List<TbBillOutputV2> tbBillOutputListAll = new ArrayList<>();
        for (SourceBill.Detail detail : detailList) {
            // 取上游账单的特约商户号去匹配收钱吧侧的交易
            List<TbSqbBillMonth> tbSqbBillMonthList = Optional.ofNullable(collect.get(detail.getSourceMerchantId())).orElse(new ArrayList<>());

            // 匹配到多个收钱吧商户时，按账期内收钱吧商户的交易量占比进行分配
            tbBillOutputListAll.addAll(this.splitSourceBillBySqbTradeAmountRatioV2(taskId, yyyymmToDate(tradeMonth), billType, tbSqbBillMonthList, detail));
        }

        return tbBillOutputListAll;

    }

    public List<TbBillOutputV2> buildDefaultBillOutputWithoutMatch(int taskId, Integer billType, String tradeMonth, List<SourceBill.Detail> detailList) {
        List<TbBillOutputV2> insertParamList = new ArrayList<>();

        detailList.forEach(detail -> {
            // 收钱吧账单为空，则直接填入一个默认值
            TbBillOutputV2 insertParam = convertToTbBillOutputV2(detail, new TbSqbBillMonth(), taskId, yyyymmToDate(tradeMonth), billType);
            insertParam.setSourceValidTradeAmountRatio("1");
            insertParam.setExtraParamsForMap(detail.getExtraParams());
            if (needQueryMerchantSnTypeList.contains(billType)) {
                MerchantParamReq merchantParamReq = new MerchantParamReq();
                merchantParamReq.setPay_merchant_id(detail.getSourceMerchantId());
                try {
                    List<MerchantProviderParamsDto> merchantProviderParams = merchantProviderParamsService.getMerchantProviderParams(merchantParamReq);
                    if (CollectionUtils.isNotEmpty(merchantProviderParams)) {
                        insertParam.setSqbMerchantSn(merchantProviderParams.get(0).getMerchant_sn());
                    }
                } catch (Exception e) {
                    log.warn("获取商户交易参数异常, sourceMerchantId:{}", detail.getSourceMerchantId(), e);
                }
            }
            insertParam.compensateParamFromSourceBillDetail(detail);
            insertParamList.add(insertParam);
        });
        return insertParamList;
    }
}
