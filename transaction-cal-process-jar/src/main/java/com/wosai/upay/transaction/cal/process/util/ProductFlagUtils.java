package com.wosai.upay.transaction.cal.process.util;

import java.util.HashMap;
import java.util.Map;

public final class ProductFlagUtils {

    private static final Map<String, String> PRODUCT_FLAG_MAP = new HashMap<String, String>(){{
        put("a1", "久久折优惠");
        put("a2", "红包优惠");
        put("a3", "花呗分期");
        put("a4", "扫码点餐");
        put("a5", "电饱饱");
    }};

    public static String getProductFlagDesc(String code) {
        String value = PRODUCT_FLAG_MAP.get(code);

        return value != null ? value : "";
    }


}