package com.wosai.upay.transaction.cal.process.businesslog;

import java.util.Arrays;
import java.util.List;

public enum TemplateCodeEnum {

    MERCHANT_AUTHORIZE_CONFIG("KOD3MBNWV8T3", "merchant_authorize", Arrays.asList("status"));

    private final String code;

    private final String tableName;

    private final List<String> filedNameList;

    TemplateCodeEnum(String code, String tableName, List<String> filedNameList) {
        this.code = code;
        this.tableName = tableName;
        this.filedNameList = filedNameList;
    }

    public String getCode() {
        return code;
    }

    public String getTableName() {
        return tableName;
    }

    public List<String> getFiledNameList() {
        return filedNameList;
    }
}
