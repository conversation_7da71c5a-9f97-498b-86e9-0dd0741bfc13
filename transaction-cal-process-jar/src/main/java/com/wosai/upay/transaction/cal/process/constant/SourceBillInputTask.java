package com.wosai.upay.transaction.cal.process.constant;

/**
 * SourceBillInputTask
 *
 * <AUTHOR>
 * @date 2019-09-11 16:54
 * @see <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=128450799">上游账单处理技术方案</a>
 */
public interface SourceBillInputTask {

    int STATUS_NEW_TASK = 1; // 新建任务
    int PROCESSING = 2; // 任务正在处理中
    int TO_BE_CONFIRMED = 3; // 待导入确认
    int CANCELLED = 4; // 已取消导入
    int SUCCESS = 5; // 任务处理成功
    int FAILURE = 6; // 任务处理失败

}
