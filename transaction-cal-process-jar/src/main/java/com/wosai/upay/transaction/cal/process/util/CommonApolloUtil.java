package com.wosai.upay.transaction.cal.process.util;

import com.alibaba.fastjson.JSONArray;
import com.ctrip.framework.apollo.ConfigService;

import java.util.List;

/**
 * <AUTHOR>
 * @Date 4:31 下午
 */
public class CommonApolloUtil {

    public static List<String> getComboIds() {
        String comboIds = ConfigService.getAppConfig().getProperty("fee_rate_deficit_combo_ids", "[]");
        return JSONArray.parseArray(comboIds, String.class);
    }

    public static List<String> getSendTo() {
        String sendTo = ConfigService.getAppConfig().getProperty("fee_rate_deficit_send_to", "[]");
        return JSONArray.parseArray(sendTo, String.class);
    }

    public static List<String> getAlarmPhonums() {
        String phonums = ConfigService.getAppConfig().getProperty("phoneNums", "[]");
        return JSONArray.parseArray(phonums, String.class);
    }

    public static boolean getAlarmFlag() {
        return ConfigService.getAppConfig().getBooleanProperty("alarmFlag", true);
    }

    public static long getDingAmountThreshold() {
        return ConfigService.getAppConfig().getLongProperty("ding_amount_threshold", 10000l);
    }


    public static List<String> getStatementVersion() {
        String comboIds = ConfigService.getAppConfig().getProperty("statement_version", "[]");
        return JSONArray.parseArray(comboIds, String.class);
    }

    public static String getSettlementState() {
        String settlementState = ConfigService.getAppConfig().getProperty("settlement_state", "是否结算");
        return settlementState;
    }
}
