package com.wosai.upay.transaction.cal.process.util;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

import java.util.concurrent.*;

@Component
@Slf4j
public class VariableThreadUtil implements InitializingBean {
    //根据apollo进行变变动
    private ThreadPoolExecutor pushThreadPool;


    private static Config config = ConfigService.getAppConfig();
    private static final String PUSH_THREAD_NUM_KEY = "push_thread_num";
    private static final int DEFAULT_THREAD_NUM = 2;
    private static int THEAD_NUM = DEFAULT_THREAD_NUM;

    @Override
    public void afterPropertiesSet() throws Exception {
        pushThreadPool = new ThreadPoolExecutor(THEAD_NUM, THEAD_NUM,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());

        loadPushThread();
        config.addChangeListener(o->{
            if(o.isChanged(PUSH_THREAD_NUM_KEY)){
                loadPushThread();
            }
        });
    }

    public ExecutorService getPushExecutorService(){
        return pushThreadPool;
    }

    private void loadPushThread(){
        int before = THEAD_NUM;
        THEAD_NUM = config.getIntProperty(PUSH_THREAD_NUM_KEY, DEFAULT_THREAD_NUM);
        log.info("推送线程池数变化:before {},after {}", before, THEAD_NUM);
        //线程池内部会处理好当前状态做到平滑修改,若调小的话会慢慢中断闲置的线程
        pushThreadPool.setCorePoolSize(THEAD_NUM);
        pushThreadPool.setMaximumPoolSize(THEAD_NUM);
    }

    public static void main(String[] args) throws InterruptedException {
        ThreadPoolExecutor pushThreadPool = new ThreadPoolExecutor(2, 2,
                0L, TimeUnit.MILLISECONDS,
                new LinkedBlockingQueue<Runnable>());

        for (int i = 0; i < 1000; i++) {
            int finalI = i;
            pushThreadPool.submit(() -> {
                System.out.println(Thread.currentThread().getName() + " task " + finalI);
                if(finalI % 2 == 0){
                    throw new RuntimeException("test error");
                }
            });

        }
        Thread.sleep(100000l);

    }
}
