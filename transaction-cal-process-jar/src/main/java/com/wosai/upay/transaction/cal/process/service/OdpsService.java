package com.wosai.upay.transaction.cal.process.service;

import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.TableTunnel;
import com.aliyun.odps.tunnel.io.TunnelRecordReader;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileWriter;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.stream.Collectors;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2022/4/2.
 */
@Component
@Slf4j
public class OdpsService {
    @Value("${odps.accessId}")
    private String accessId;
    @Value("${odps.accessKey}")
    private String accessKey;
    @Value("${odps.endPoint}")
    private String endPoint;
    @Value("${odps.projectName}")
    private String projectName;

    public static Odps odps;

    @PostConstruct
    private void init() {
        Account account = new AliyunAccount(accessId, accessKey);
        odps = new Odps(account);
        odps.setEndpoint(endPoint);
        odps.setDefaultProject(projectName);
        log.info("【OdpsUtils初始化】accessId: {}, accessKey: {}, endPoint: {}, projectName: {}" , accessId, "*", endPoint, projectName);
    }

    @SneakyThrows
    public File getCsvFile(String querySql){
        String table = "Tmp_" + UUID.randomUUID().toString().replace("-", "_");
        String sql = String.format("create table %s lifecycle 1 as %s", table, querySql);
        log.info("odps sql {}", sql);
        //run sql
        Instance i = SQLTask.run(odps, sql);
        i.waitForSuccess();
        //download data
        return downloadCsv(table);
    }

    @SneakyThrows
    /**
     *  获取sql的执行结果，适用于少量数据
     */
    public List<Map<String,Object>> getResult(String sql){
        Instance i = SQLTask.run(odps, sql);
        i.waitForSuccess();
        List<Record> result = SQLTask.getResult(i);
        if(result == null){
            return Collections.emptyList();
        }
        return result.stream().map(r -> {
            Map<String,Object> map = new LinkedHashMap<>();
            Column[] columns = r.getColumns();
            for (Column column : columns) {
                String columnName = column.getName();
                map.put(columnName, parseRecordColumnValue(r.get(columnName)));
            }
            return map;
        }).collect(Collectors.toList());
    }

    @SneakyThrows
    public File downloadCsv(String table){
        TableTunnel tunnel = new TableTunnel(odps);
        TableTunnel.DownloadSession downloadSession = tunnel.createDownloadSession(projectName, table);
        long count = downloadSession.getRecordCount();
        if (count == 0) {
            log.info("no data");
            return null;
        }
        CSVFormat csvFormat = CSVFormat.DEFAULT;
        String fileFullPath = "../tmp" + UUID.randomUUID() + ".csv";
        CSVPrinter csv = new CSVPrinter(new FileWriter(fileFullPath), csvFormat);
        TunnelRecordReader reader = downloadSession.openRecordReader(0, count);
        boolean headHasWrite = false;
        while(true){
            Record record = reader.read();
            if(record == null){
                break;
            }
            if(!headHasWrite){
                //write head
                record.getColumns();
                List<String> columNames = Arrays.stream(record.getColumns()).map(c -> c.getName()).collect(Collectors.toList());
                csv.printRecord(columNames);
                headHasWrite = true;
            }
            List<Object> columns = Arrays.stream(record.toArray()).map(c -> parseRecordColumnValue(c)).collect(Collectors.toList());
            csv.printRecord(columns);
        }
        csv.flush();
        return new File(fileFullPath);
    }


    private Object parseRecordColumnValue(Object columnValue){
        if(columnValue == null){
            return null;
        }
        Object value = columnValue;
        if (columnValue instanceof byte[]) {
            value = new String((byte[]) columnValue);
        } else if (columnValue instanceof java.sql.Date) {
            value = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date(((Date) columnValue).getTime()));
        } else if(columnValue instanceof Date){
            value =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(columnValue);
        }
        return value.toString().replace(",", "");
    }

}
