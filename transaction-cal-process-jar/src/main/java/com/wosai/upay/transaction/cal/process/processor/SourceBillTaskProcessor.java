package com.wosai.upay.transaction.cal.process.processor;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.StringUtil;
import com.google.common.collect.ArrayListMultimap;
import com.google.common.collect.Multimap;
import com.google.gson.Gson;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.itsys.cornucopia.admin.domain.response.ImportTaskResponse;
import com.wosai.itsys.cornucopia.admin.domain.response.ImportTemplateRuleResponse;
import com.wosai.itsys.cornucopia.admin.domain.response.PolicyResponse;
import com.wosai.itsys.cornucopia.admin.service.IImportTaskService;
import com.wosai.itsys.cornucopia.admin.service.IImportTemplateRuleRpcService;
import com.wosai.itsys.cornucopia.admin.service.IPolicyService;
import com.wosai.upay.transaction.cal.process.constant.SourceBillInputTask;
import com.wosai.upay.transaction.cal.process.converter.SummaryBillConverter;
import com.wosai.upay.transaction.cal.process.helper.BillProcessHelper;
import com.wosai.upay.transaction.cal.process.mapper.*;
import com.wosai.upay.transaction.cal.process.model.domain.*;
import com.wosai.upay.transaction.cal.process.model.dto.SummaryBillRemark;
import com.wosai.upay.transaction.cal.process.model.dto.TbSourceBillInfoDTO;
import com.wosai.upay.transaction.cal.process.model.dto.TradeInfoDto;
import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SourceBillType;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import com.wosai.upay.transaction.cal.process.processor.parse.CommonSourceBillParser;
import com.wosai.upay.transaction.cal.process.util.OssUtils;
import com.wosai.upay.transaction.cal.process.util.SnowFlakeUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.stereotype.Component;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

import javax.annotation.Resource;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.text.DecimalFormat;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * SourceBillTaskProcessor
 *
 * <AUTHOR>
 * @date 2019-09-12 14:46
 */
@Slf4j
@Component
public class SourceBillTaskProcessor {

    private final DataSourceTransactionManager transactionManager;

    private final Gson gson;
    private final BillProcessHelper billProcessHelper;

    private final TbSourceBillInputTaskMapper tbSourceBillInputTaskMapper;
    private final TbBillOutputMapper tbBillOutputMapper;
    private final TbSourceBillMapper tbSourceBillMapper;
    private final TbSourceBillV2Mapper tbSourceBillV2Mapper;
    private final CommonSourceBillParser commonSourceBillParser;
    private final TbBillOutputV2Mapper tbBillOutputV2Mapper;
    @Value("${file.path.bill}")
    private String filePathBill;
    private final OssUtils ossUtils;
    private final TbSummaryBillMapper tbSummaryBillMapper;
    @Resource
    private SnowFlakeUtil snowFlakeUtil;
    @Resource
    private IPolicyService iPolicyService;
    @Resource
    private IImportTaskService iImportTaskService;
    @Resource
    private IImportTemplateRuleRpcService importTemplateRuleService;

    public SourceBillTaskProcessor(DataSourceTransactionManager transactionManager,
                                   Gson gson,
                                   BillProcessHelper billProcessHelper,
                                   TbSourceBillInputTaskMapper tbSourceBillInputTaskMapper,
                                   TbBillOutputMapper tbBillOutputMapper,
                                   TbSourceBillMapper tbSourceBillMapper, CommonSourceBillParser commonSourceBillParser, OssUtils ossUtils,
                                   TbSummaryBillMapper tbSummaryBillMapper,
                                   TbBillOutputV2Mapper billOutputV2Mapper,
                                   TbSourceBillV2Mapper tbSourceBillV2Mapper) {
        this.transactionManager = transactionManager;
        this.gson = gson;
        this.billProcessHelper = billProcessHelper;
        this.tbSourceBillInputTaskMapper = tbSourceBillInputTaskMapper;
        this.tbBillOutputMapper = tbBillOutputMapper;
        this.tbSourceBillMapper = tbSourceBillMapper;
        this.commonSourceBillParser = commonSourceBillParser;
        this.ossUtils = ossUtils;
        this.tbSummaryBillMapper = tbSummaryBillMapper;
        this.tbBillOutputV2Mapper = billOutputV2Mapper;
        this.tbSourceBillV2Mapper = tbSourceBillV2Mapper;
    }

    /**
     * 上游账单任务处理<br/>
     * 步骤如下：<br/>
     * 1. 解析上游账单并存储到数据库；<br/>
     * 2. 将每条上游账单分别匹配收钱吧账单；<br/>
     * 2.1如果出现一对多的账单，将上游账单拆分；<br/>
     *
     * @param tbSourceBillInputTask 上游账单任务
     * @see <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********">上游账单处理方案文档</a>
     */
    public void process(TbSourceBillInputTask tbSourceBillInputTask) {
        log.info("上游账单任务开始处理: {}", JSON.toJSONString(tbSourceBillInputTask));
        if (tbSourceBillInputTask == null) {
            log.warn("上游账单任务为空");
            return;
        }

        // 判断任务状态是否为【新建任务】或【处理失败的任务】
        TbSourceBillInputTaskWithBLOBs task = this.tbSourceBillInputTaskMapper.selectByPrimaryKey(tbSourceBillInputTask.getId());
        if (task == null) {
            log.info("上游账单任务为空");
            return;
        }
        int taskId = task.getId();
        int billType = task.getSourceBillType();
        if (task.getTaskStatus() != SourceBillInputTask.STATUS_NEW_TASK && task.getTaskStatus() != SourceBillInputTask.FAILURE) {
            log.error("上游账单任务不能被处理，因为只能处理状态为新建、处理失败的任务: {}", gson.toJson(tbSourceBillInputTask));
            return;
        }

        log.info("重新获取到的上游账单任务: {}", JSON.toJSONString(task));
        // 设置任务处理状态为处理中
        TbSourceBillInputTaskWithBLOBs updateParam = new TbSourceBillInputTaskWithBLOBs();
        updateParam.setId(tbSourceBillInputTask.getId());
        updateParam.setTaskStatus(SourceBillInputTask.PROCESSING);
        this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);

        // 主动提交/回滚事务
        DefaultTransactionDefinition transDefinition = new DefaultTransactionDefinition();
        transDefinition.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transStatus = this.transactionManager.getTransaction(transDefinition);
        try {
            // ----------------------- 1. 解析上游账单并存储到数据库 -----------------------
            // 解析后的账单 Table
            SourceBill sourceBill = commonSourceBillParser.parse(task);
            // 解析后端账单列表
            List<SourceBill.Detail> sourceBillDetailList = sourceBill.getDetailList();
            // 账单月份：yyyymm
            String tradeMonth = sourceBill.getTradeMonth();
            // 账单月份：java.util.Date
            Date tradeMonthDate = this.billProcessHelper.yyyymmToDate(tradeMonth);
            sourceBill = null;
            log.info("任务id:{} 上游账单解析完成，共 {} 行，下一步将解析后的原始账单表写入数据库", taskId, sourceBillDetailList.size());

            // 将解析后的原始账单写入数据库
            List<SourceBill.Detail> detailListTemp = new ArrayList<>(2000); // 2000 条
            for (int i = 1; i <= sourceBillDetailList.size(); i++) {
                detailListTemp.add(sourceBillDetailList.get(i - 1));
                // 2000 条
                if (i % 2000 == 0 || i == sourceBillDetailList.size()) {
                    log.info("原始账单表写入数据库 第: {} 批", i);
                    tbSourceBillMapper.batchInsert(task.getId(), tradeMonthDate, billType, detailListTemp);
                    detailListTemp = new ArrayList<>(2000); // 置空
                }
            }
            detailListTemp = null; // help GC
            log.info("任务id:{} 已将解析后的上游账单原始表写入数据库，下一步将进入账单匹配环节", taskId);
            // ----------------------- 2. 将每条上游账单分别匹配收钱吧账单 -----------------------
            List<TbBillOutput> billOutputList;
            switch (billType) {
                case SourceBillType.TYPE_ALIPAY_DIRECT_TRADE:
                case SourceBillType.TYPE_ALIPAY_OFFLINE_MINI_PROGRAM_BASIC_PAYMENT:
                    billOutputList = this.billProcessHelper.billMatchForAlipayDirect(taskId, tradeMonth, sourceBillDetailList, billType);
                    break;
                case SourceBillType.TYPE_ALIPAY_KOUBEI:
                    billOutputList = this.billProcessHelper.billMatchForAlipayKoubei(taskId, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT:
                    billOutputList = this.billProcessHelper.billMatchForAlipayHuabei(taskId, tradeMonth, sourceBillDetailList, SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT);
                    break;
                case SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT:
                    billOutputList = this.billProcessHelper.billMatchForAlipayHuabei(taskId, tradeMonth, sourceBillDetailList, SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT);
                    break;
                case SourceBillType.TYPE_WECHAT_INDIRECT_A:
                    billOutputList = this.billProcessHelper.billMatchForWechatIndirect_A(taskId, billType, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_WEIXIN_DIRECT_TRADE:
                    billOutputList = this.billProcessHelper.billMatchForWeixinDirect(taskId, billType, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_WEIXIN_SCHOOL_CAFETERIA:
                    billOutputList = this.billProcessHelper.billMatchForWeixinSchool(taskId, billType, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_WEIXIN_PUBLIC_HOSPITAL:
                    billOutputList = this.billProcessHelper.billMatchForWeixinPublicHospital(taskId, billType, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_ALIPAY_SCHOOL:
                    billOutputList = this.billProcessHelper.billMatchForAlipaySchool(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_SCHOOL, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_WECHAT_OASIS_TRADE:
                    billOutputList = this.billProcessHelper.billMatchForWeixinOasis(taskId, billType, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_ALIPAY_BLUE_OCEAN_TRADE:
                    billOutputList = this.billProcessHelper.billMatchSplitSqbTradeAmount(taskId, billType, tradeMonth, sourceBillDetailList, SourceBillType.TYPE_ALIPAY_BLUE_OCEAN_TRADE);
                    break;
                case SourceBillType.TYPE_WEIXIN_SCHOOL_DIRECT_CAFETERIA:
                    billOutputList = this.billProcessHelper.billMatchForWeixinSchool(taskId, billType, tradeMonth, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_ALIPAY_TUANCAN:
                    billOutputList = this.billProcessHelper.billMatchForAlipaySchool(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_TUANCAN, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_ALIPAY_EDU:
                    billOutputList = this.billProcessHelper.billMatchForAlipaySchool(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_EDU, sourceBillDetailList);
                    break;
                case SourceBillType.TYPE_ALIPAY_K12:
                case SourceBillType.TYPE_LKL:
                case SourceBillType.TYPE_TL:
                case SourceBillType.TYPE_ALIPAY_DRAGONFLY:
                case SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE:
                case SourceBillType.TYPE_ALIPAY_HOSPITAL:
                    //合并上游账单相同的商户号的
                    sourceBillDetailList = new ArrayList<>(sourceBillDetailList.stream().collect(Collectors.toMap(detail -> detail.getSourceMerchantId(), detail -> detail, (detail1, detail2) -> {
                        SourceBill.Detail finalDetail = new SourceBill.Detail();
                        finalDetail.setSubNum(detail1.getSubNum());
                        finalDetail.setSourceMerchantId(detail1.getSourceMerchantId());
                        finalDetail.setSourceMerchantName(detail1.getSourceMerchantName());
                        finalDetail.setSourceLevel2MerchantId(detail1.getSourceLevel2MerchantId());
                        finalDetail.setSourceLevel2MerchantName(detail1.getSourceLevel2MerchantName());
                        finalDetail.setDimension((NumberUtils.toLong(detail1.getDimension()) + NumberUtils.toLong(detail2.getDimension())) + "");
                        finalDetail.setSourceValidTradeNum(detail1.getSourceValidTradeNum() + detail2.getSourceValidTradeNum());
                        finalDetail.setSourceValidTradeAmount(detail1.getSourceValidTradeAmount() + detail2.getSourceValidTradeAmount());
                        finalDetail.setSourceValidRefundAmount(detail1.getSourceValidRefundAmount() + detail2.getSourceValidRefundAmount());
                        finalDetail.setSourceValidRefundNum(detail1.getSourceValidRefundNum() + detail2.getSourceValidRefundNum());
                        finalDetail.setSourceSettlementBasisType(detail1.getSourceSettlementBasisType());
                        finalDetail.setSourceSettlementBasis(detail1.getSourceSettlementBasis() + detail2.getSourceSettlementBasis());
                        //            finalDetail.setSourceMerchantFeeRate(detail1.getSourceMerchantFeeRate());
                        //            finalDetail.setSourceSettlementFeeRate(detail1.getSourceMerchantFeeRate());
                        //            finalDetail.setCappingRate(detail1.getCappingRate());
                        finalDetail.setSourceSettlementAmount(detail1.getSourceSettlementAmount() + detail2.getSourceSettlementAmount());
                        if (SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE == billType) {
                            if (finalDetail.getSourceSettlementAmount() == 0 || finalDetail.getSourceSettlementBasis() == 0) {
                                finalDetail.setSourceSettlementFeeRate(detail1.getSourceSettlementFeeRate());
                            } else {
                                finalDetail.setSourceSettlementFeeRate(String.valueOf(BigDecimal.valueOf(finalDetail.getSourceSettlementAmount().doubleValue() / finalDetail.getSourceSettlementBasis().doubleValue()).setScale(4, RoundingMode.HALF_UP).doubleValue()));
                            }
                        }
                        finalDetail.setRemark(detail1.getRemark());
                        finalDetail.setServiceProviderId(detail1.getServiceProviderId());
                        return finalDetail;
                    })).values());
                    billOutputList = this.billProcessHelper.billMatchSplitSqbTradeAmount(taskId, billType, tradeMonth, sourceBillDetailList, billType);
                    break;
                case SourceBillType.TYPE_ALIPAY_SCHOOL_V2:
                    billOutputList = this.billProcessHelper.billMatchForAlipaySchool(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_SCHOOL_V2, sourceBillDetailList);
                    break;
                default:
                    throw new CommonInvalidParameterException("不支持该类型的账单匹配，类型: " + billType);
            }

            log.info("任务id: {} 账单匹配完成,下一步将写入数据库", taskId);

            // 将匹配后的账单列表批量写入数据库
            List<TbBillOutput> billOutputListTemp = new ArrayList<>(2000); // 2000 条一提交
            for (int i = 1; i <= billOutputList.size(); i++) {
                billOutputListTemp.add(billOutputList.get(i - 1));
                // 2000 条一提交
                if (i % 2000 == 0 || i == billOutputList.size()) {
                    log.info("拆分后的账单写入数据库 第: {} 批", i);
                    this.tbBillOutputMapper.batchInsert(billOutputListTemp);
                    billOutputListTemp = new ArrayList<>(2000); // 置空
                }
            }

            log.info("任务id: {} 将拆分后的账单写入数据库完成，下一步将统一合计交易信息", taskId);

            // 统计交易信息
            TbSourceBillExample countExample = new TbSourceBillExample();
            countExample.or().andSourceBillInputTaskIdEqualTo(taskId);
            long sourceBillRecordNumber = this.tbSourceBillMapper.countByExample(countExample); // 上游账单文件记录数

            TradeInfoDto tradeInfo = this.tbBillOutputMapper.statisticalTradeTotal(taskId);
            tradeInfo.setSourceBillRecordNumber(sourceBillRecordNumber);
            TbSourceBillInputTaskWithBLOBs taskUpdateParam = new TbSourceBillInputTaskWithBLOBs();
            taskUpdateParam.setId(task.getId());
            taskUpdateParam.setExtra(gson.toJson(tradeInfo).getBytes());
            this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(taskUpdateParam);
            log.info("任务id: {} 统计任务中账单的交易信息完成", taskId);

            // 双向对比生成对比文件
            String reconciliationfile = reconciliationfile(billType, sourceBillDetailList, tradeMonth);
            if (StringUtils.isNotBlank(reconciliationfile)) {
                updateParam.setExtra(gson.toJson(tradeInfo.setReconciliationFile(reconciliationfile)).getBytes());
            }

            // 设置任务处理状态为待确认导入状态
            updateParam.setTaskStatus(SourceBillInputTask.TO_BE_CONFIRMED);
            this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);

            transactionManager.commit(transStatus);

            billOutputList = null; // help GC
            billOutputListTemp = null;
            sourceBillDetailList = null;


            log.info("任务id: {} 任务处理成功", taskId);

        } catch (Exception e) {
            log.error("上游账单处理失败,{}: {}", e.getMessage(), e);
            transactionManager.rollback(transStatus);
            // 设置任务处理`状态为处理失败，error_info
            updateParam.setTaskStatus(SourceBillInputTask.FAILURE);
            if (e.getMessage() != null) {
                if (e.getMessage().length() > 3000) {
                    updateParam.setErrorInfo(e.getMessage().substring(0, 3000).getBytes());
                } else {
                    updateParam.setErrorInfo(e.getMessage().getBytes());
                }
            }
            this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);
        }
    }

    public TbSummaryBill convertToSummaryBill(SourceBill sourceBill, String policyNameOrNcName, ImportTaskResponse importTaskResponse, ImportTemplateRuleResponse importTemplateRuleResponse, TbSourceBillInfoDTO tbSourceBillInfoDTO) {
        TbSummaryBill tbSummaryBill = new TbSummaryBill();
        tbSummaryBill.setTradeMonth(this.billProcessHelper.yyyymmToDate(sourceBill.getTradeMonth()));
        if (StringUtils.isNotBlank(tbSourceBillInfoDTO.getEntryMonth())) {
            tbSummaryBill.setEntryMonth(this.billProcessHelper.yyyymmToDate(tbSourceBillInfoDTO.getEntryMonth()));
        }
        tbSummaryBill.setSn(String.valueOf(snowFlakeUtil.nextId(12)));
        tbSummaryBill.setPolicyId(sourceBill.getPolicyId());
        tbSummaryBill.setSourceBillType(sourceBill.getSourceBillType());
        tbSummaryBill.setNcName(sourceBill.getNcName());
        tbSummaryBill.setCompanyName(sourceBill.getCompanyName());
        tbSummaryBill.setPropertyId(sourceBill.getPropertyId());
        tbSummaryBill.setServerChannelCode(sourceBill.getServiceProviderId());
        tbSummaryBill.setRemark(JSON.toJSONString(new SummaryBillRemark(Stream.of(
                new SummaryBillRemark.SummaryBillImportTask(importTaskResponse.getTaskSn(), SummaryBillRemark.SummaryBillImportTaskStatus.SUCCESS, new Date().toLocaleString(), new Date().toLocaleString(), String.valueOf(sourceBill.getDetailList().size()), importTemplateRuleResponse.getName())
        ).collect(Collectors.toList()))));
        tbSummaryBill.setStatus(SummaryBillStatus.PROCESSING);
        tbSummaryBill.setPushStatus(FulfillmentPushStatus.INIT);
        tbSummaryBill.generateName(policyNameOrNcName);
        tbSummaryBill.setSourceMerchantNumber((long) sourceBill.getDetailList().size());
        return tbSummaryBill;
    }

    public TbSummaryBill convertToSummaryBill(TbSummaryBill tbSummaryBill, SourceBill sourceBill, ImportTaskResponse importTaskResponse, ImportTemplateRuleResponse importTemplateRuleResponse) {
        if (StringUtil.isNotEmpty(tbSummaryBill.getRemark())
                && StringUtils.isNotEmpty(sourceBill.getTaskId())
                && CollectionUtils.isNotEmpty(tbSummaryBill.queryRemarkObj().getTaskList())) {
            SummaryBillRemark resultRemark = tbSummaryBill.queryRemarkObj();
            resultRemark.addTask(importTaskResponse.getTaskSn(), String.valueOf(sourceBill.getDetailList().size()), importTemplateRuleResponse.getName());
            tbSummaryBill.setRemark(JSON.toJSONString(resultRemark));
            tbSummaryBill.setStatus(SummaryBillStatus.PROCESSING);
            tbSummaryBill.setSourceMerchantNumber(tbSummaryBill.getSourceMerchantNumber() + (long) sourceBill.getDetailList().size());

            // Update nc code, company code and propertyId if source bill has them
            if (StringUtils.isNotEmpty(sourceBill.getNcName())) {
                tbSummaryBill.setNcName(sourceBill.getNcName());
            }
            if (StringUtils.isNotEmpty(sourceBill.getCompanyName())) {
                tbSummaryBill.setCompanyName(sourceBill.getCompanyName());
            }
            if (StringUtils.isNotEmpty(sourceBill.getPropertyId())) {
                tbSummaryBill.setPropertyId(sourceBill.getPropertyId());
            }
        }

        return tbSummaryBill;
    }

    /**
     * 上游账单任务处理<br/>
     * 步骤如下：<br/>
     * 1. 解析上游账单并存储到数据库；<br/>
     * 2. 将每条上游账单分别匹配收钱吧账单；<br/>
     * 2.1如果出现一对多的账单，将上游账单拆分；<br/>
     *
     * @param tbSourceBillInfoDTO 解析完成的上游账单数据
     * @see <a href="https://confluence.wosai-inc.com/pages/viewpage.action?pageId=*********">上游账单处理方案文档</a>
     */
    public void process(TbSourceBillInfoDTO tbSourceBillInfoDTO, TbSourceBillType tbSourceBillType) {
        SourceBill sourceBill = SummaryBillConverter.INSTANCE.dtoToDo(tbSourceBillInfoDTO, Optional.ofNullable(tbSourceBillType).map(TbSourceBillType::getId).orElse(null));
        Integer taskId = tbSourceBillInfoDTO.getInnerTaskId();
        Integer billType = sourceBill.getSourceBillType();

        List<ImportTaskResponse> importTaskResponses = iImportTaskService.getImportTaskListByTaskSnList(Stream.of(sourceBill.getTaskId()).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(importTaskResponses)) {
            throw new CommonInvalidParameterException("未找到对应的导入任务");
        }
        List<ImportTemplateRuleResponse> importTemplateRuleResponses = importTemplateRuleService.getByTemplateCodeList(importTaskResponses.stream().map(ImportTaskResponse::getTemplateCode).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(importTemplateRuleResponses)) {
            throw new CommonInvalidParameterException("未找到对应的导入模板规则");
        }

        // 设置任务处理状态为处理中
        TbSourceBillInputTaskWithBLOBs updateParam = new TbSourceBillInputTaskWithBLOBs();
        updateParam.setId(tbSourceBillInfoDTO.getInnerTaskId());
        updateParam.setTaskStatus(SourceBillInputTask.PROCESSING);
        this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);

        // 主动提交/回滚事务
        DefaultTransactionDefinition transDefinition = new DefaultTransactionDefinition();
        transDefinition.setPropagationBehavior(DefaultTransactionDefinition.PROPAGATION_REQUIRES_NEW);
        TransactionStatus transStatus = this.transactionManager.getTransaction(transDefinition);
        try {
            // ----------------------- 1. 解析上游账单并存储到数据库 -----------------------
            // 解析后端账单列表
            List<SourceBill.Detail> sourceBillDetailList = sourceBill.getDetailList();
            // 账单月份：yyyymm
            String tradeMonth = sourceBill.getTradeMonth();
            // 账单月份：java.util.Date
            Date tradeMonthDate = this.billProcessHelper.yyyymmToDate(tradeMonth);
            PolicyResponse policyResponse = null;
            if (StringUtils.isNotEmpty(sourceBill.getPolicyId())) {
                policyResponse = iPolicyService.queryPolicyById(Long.valueOf(sourceBill.getPolicyId()));
            }

            TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
            TbSummaryBillExample.Criteria criteria = tbSummaryBillExample.createCriteria().andTradeMonthEqualTo(tradeMonthDate);
            criteria.andStatusIn(Stream.of(SummaryBillStatus.PROCESSING.getCode(), SummaryBillStatus.INIT.getCode()).collect(Collectors.toList()));
            if (StringUtils.isNotEmpty(sourceBill.getPolicyId())) {
                criteria.andPolicyIdEqualTo(sourceBill.getPolicyId());
            }
            if (StringUtils.isNotEmpty(sourceBill.getPropertyId())) {
                criteria.andPropertyIdEqualTo(sourceBill.getPropertyId());
            }
            if (StringUtils.isNotEmpty(sourceBill.getNcName())) {
                criteria.andNcNameEqualTo(sourceBill.getNcName());
            }
            if (StringUtils.isNotEmpty(sourceBill.getCompanyName())) {
                criteria.andCompanyNameEqualTo(sourceBill.getCompanyName());
            }
            if (StringUtils.isNotEmpty(tbSourceBillInfoDTO.getEntryMonth())) {
                criteria.andEntryMonthEqualTo(this.billProcessHelper.yyyymmToDate(tbSourceBillInfoDTO.getEntryMonth()));
            }
            TbSummaryBill tbSummaryBill = null;
            List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
            if (CollectionUtils.isEmpty(tbSummaryBills)) {
                tbSummaryBill = convertToSummaryBill(sourceBill, policyResponse != null ? policyResponse.getName() : sourceBill.getNcName(), importTaskResponses.get(0), importTemplateRuleResponses.get(0), tbSourceBillInfoDTO);
            } else {
                tbSummaryBill = convertToSummaryBill(tbSummaryBills.get(0), sourceBill, importTaskResponses.get(0), importTemplateRuleResponses.get(0));
            }
            tbSummaryBillMapper.insertSelectiveOrUpdate(tbSummaryBill);
            log.info("任务id:{} 上游账单解析完成，共 {} 行，下一步将解析后的原始账单表写入数据库", taskId, sourceBillDetailList.size());

            // 将解析后的原始账单写入数据库
            List<SourceBill.Detail> detailListTemp = new ArrayList<>(2000); // 2000 条
            for (int i = 1; i <= sourceBillDetailList.size(); i++) {
                detailListTemp.add(sourceBillDetailList.get(i - 1));
                // 2000 条
                if (i % 2000 == 0 || i == sourceBillDetailList.size()) {
                    log.info("原始账单表写入数据库 第: {} 批", i);
                    tbSourceBillV2Mapper.batchInsert(Integer.valueOf(taskId), tradeMonthDate, billType, detailListTemp);
                    detailListTemp = new ArrayList<>(2000); // 置空
                }
            }
            detailListTemp = null; // help GC
            log.info("任务id:{} 已将解析后的上游账单原始表写入数据库，下一步将进入账单匹配环节", taskId);
            // ----------------------- 2. 将每条上游账单分别匹配收钱吧账单 -----------------------
            List<TbBillOutputV2> billOutputList;
            if (billType == null) {
                billOutputList = this.billProcessHelper.buildDefaultBillOutputWithoutMatch(taskId, billType, tradeMonth, sourceBillDetailList);
            } else {
                switch (billType) {
                    case SourceBillType.TYPE_ALIPAY_DIRECT_TRADE:
                    case SourceBillType.TYPE_ALIPAY_OFFLINE_MINI_PROGRAM_BASIC_PAYMENT:
                        billOutputList = this.billProcessHelper.billMatchForAlipayDirectV2(taskId, tradeMonth, sourceBillDetailList, billType);
                        break;
                    case SourceBillType.TYPE_ALIPAY_KOUBEI:
                        billOutputList = this.billProcessHelper.billMatchForAlipayKoubeiV2(taskId, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT:
                        billOutputList = this.billProcessHelper.billMatchForAlipayHuabeiV2(taskId, tradeMonth, sourceBillDetailList, SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT);
                        break;
                    case SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT:
                        billOutputList = this.billProcessHelper.billMatchForAlipayHuabeiV2(taskId, tradeMonth, sourceBillDetailList, SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT);
                        break;
                    case SourceBillType.TYPE_WECHAT_INDIRECT_A:
                        billOutputList = this.billProcessHelper.billMatchForWechatIndirect_AV2(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_WEIXIN_DIRECT_TRADE:
                        billOutputList = this.billProcessHelper.billMatchForWeixinDirectV2(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_WEIXIN_SCHOOL_CAFETERIA:
                        billOutputList = this.billProcessHelper.billMatchForWeixinSchoolV2(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_WEIXIN_PUBLIC_HOSPITAL:
                        billOutputList = this.billProcessHelper.billMatchForWeixinPublicHospitalV2(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_ALIPAY_SCHOOL:
                        billOutputList = this.billProcessHelper.billMatchForAlipaySchoolV2(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_SCHOOL, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_WECHAT_OASIS_TRADE:
                        billOutputList = this.billProcessHelper.billMatchForWeixinOasisV2(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_ALIPAY_BLUE_OCEAN_TRADE:
                        billOutputList = this.billProcessHelper.billMatchSplitSqbTradeAmountV2(taskId, billType, tradeMonth, sourceBillDetailList, SourceBillType.TYPE_ALIPAY_BLUE_OCEAN_TRADE);
                        break;
                    case SourceBillType.TYPE_WEIXIN_SCHOOL_DIRECT_CAFETERIA:
                        billOutputList = this.billProcessHelper.billMatchForWeixinSchoolV2(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_ALIPAY_TUANCAN:
                        billOutputList = this.billProcessHelper.billMatchForAlipaySchoolV2(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_TUANCAN, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_ALIPAY_EDU:
                        billOutputList = this.billProcessHelper.billMatchForAlipaySchoolV2(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_EDU, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_ALIPAY_K12:
                    case SourceBillType.TYPE_LKL:
                    case SourceBillType.TYPE_TL:
                    case SourceBillType.TYPE_ALIPAY_DRAGONFLY:
                    case SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE:
                    case SourceBillType.TYPE_ALIPAY_HOSPITAL:
                        //合并上游账单相同的商户号的
                        sourceBillDetailList = new ArrayList<>(sourceBillDetailList.stream().collect(Collectors.toMap(detail -> detail.getSourceMerchantId(), detail -> detail, (detail1, detail2) -> {
                            SourceBill.Detail finalDetail = new SourceBill.Detail();
                            finalDetail.setSubNum(detail1.getSubNum());
                            finalDetail.setSourceMerchantId(detail1.getSourceMerchantId());
                            finalDetail.setSourceMerchantName(detail1.getSourceMerchantName());
                            finalDetail.setSourceLevel2MerchantId(detail1.getSourceLevel2MerchantId());
                            finalDetail.setSourceLevel2MerchantName(detail1.getSourceLevel2MerchantName());
                            finalDetail.setDimension((NumberUtils.toLong(detail1.getDimension()) + NumberUtils.toLong(detail2.getDimension())) + "");
                            finalDetail.setSourceValidTradeNum(detail1.getSourceValidTradeNum() + detail2.getSourceValidTradeNum());
                            finalDetail.setSourceValidTradeAmount(detail1.getSourceValidTradeAmount() + detail2.getSourceValidTradeAmount());
                            finalDetail.setSourceValidRefundAmount(detail1.getSourceValidRefundAmount() + detail2.getSourceValidRefundAmount());
                            finalDetail.setSourceValidRefundNum(detail1.getSourceValidRefundNum() + detail2.getSourceValidRefundNum());
                            finalDetail.setSourceSettlementBasisType(detail1.getSourceSettlementBasisType());
                            finalDetail.setSourceSettlementBasis(detail1.getSourceSettlementBasis() + detail2.getSourceSettlementBasis());
                            //            finalDetail.setSourceMerchantFeeRate(detail1.getSourceMerchantFeeRate());
                            //            finalDetail.setSourceSettlementFeeRate(detail1.getSourceMerchantFeeRate());
                            //            finalDetail.setCappingRate(detail1.getCappingRate());
                            finalDetail.setSourceSettlementAmount(detail1.getSourceSettlementAmount() + detail2.getSourceSettlementAmount());
                            if (SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE == billType) {
                                if (finalDetail.getSourceSettlementAmount() == 0 || finalDetail.getSourceSettlementBasis() == 0) {
                                    finalDetail.setSourceSettlementFeeRate(detail1.getSourceSettlementFeeRate());
                                } else {
                                    finalDetail.setSourceSettlementFeeRate(String.valueOf(BigDecimal.valueOf(finalDetail.getSourceSettlementAmount().doubleValue() / finalDetail.getSourceSettlementBasis().doubleValue()).setScale(4, RoundingMode.HALF_UP).doubleValue()));
                                }
                            }
                            finalDetail.setRemark(detail1.getRemark());
                            finalDetail.setServiceProviderId(detail1.getServiceProviderId());
                            return finalDetail;
                        })).values());
                        billOutputList = this.billProcessHelper.billMatchSplitSqbTradeAmountV2(taskId, billType, tradeMonth, sourceBillDetailList, billType);
                        break;
                    case SourceBillType.TYPE_ALIPAY_SCHOOL_V2:
                        billOutputList = this.billProcessHelper.billMatchForAlipaySchoolV2(taskId, tradeMonth, SourceBillType.TYPE_ALIPAY_SCHOOL_V2, sourceBillDetailList);
                        break;
                    case SourceBillType.TYPE_DEFAULT_BILL_TYPE:
                        billOutputList = this.billProcessHelper.buildDefaultBillOutputWithoutMatch(taskId, billType, tradeMonth, sourceBillDetailList);
                        break;
                    default:
                        throw new CommonInvalidParameterException("不支持该类型的账单匹配，类型: " + billType);
                }
            }

            log.info("任务id: {} 账单匹配完成,下一步将写入数据库", taskId);

            // 将匹配后的账单列表批量写入数据库
            List<TbBillOutputV2> billOutputListTemp = new ArrayList<>(2000); // 2000 条一提交
            for (int i = 1; i <= billOutputList.size(); i++) {
                billOutputList.get(i - 1).setSummaryBillSn(tbSummaryBill.getSn());
                billOutputList.get(i - 1).setImportTaskSn(sourceBill.getTaskId());
                billOutputListTemp.add(billOutputList.get(i - 1));
                tbSummaryBill.setSourceValidTradeAmount(tbSummaryBill.getSourceValidTradeAmount() + billOutputList.get(i - 1).getSourceValidTradeAmount());
                tbSummaryBill.setSourceValidTradeNum(tbSummaryBill.getSourceValidTradeNum() + billOutputList.get(i - 1).getSourceValidTradeNum());
                tbSummaryBill.setSourceSettlementAmount(tbSummaryBill.getSourceSettlementAmount() + billOutputList.get(i - 1).getSourceSettlementAmount());
                tbSummaryBill.setSourceValidRefundNum(tbSummaryBill.getSourceValidRefundNum() + billOutputList.get(i - 1).getSourceValidRefundNum());
                tbSummaryBill.setSourceValidRefundAmount(tbSummaryBill.getSourceValidRefundAmount() + billOutputList.get(i - 1).getSourceValidRefundAmount());

                // 2000 条一提交
                if (i % 2000 == 0 || i == billOutputList.size()) {
                    log.info("拆分后的账单写入数据库 第: {} 批", i);
                    this.tbBillOutputV2Mapper.batchInsert(billOutputListTemp);
                    billOutputListTemp = new ArrayList<>(2000); // 置空
                }
            }
            tbSummaryBill.setStatus(SummaryBillStatus.INIT);
            tbSummaryBillMapper.insertSelectiveOrUpdate(tbSummaryBill);

            // 设置任务处理状态为待确认导入状态
            updateParam.setTaskStatus(SourceBillInputTask.TO_BE_CONFIRMED);
            this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);

            transactionManager.commit(transStatus);

            billOutputList = null; // help GC
            billOutputListTemp = null;
            sourceBillDetailList = null;


            log.info("任务id: {} 任务处理成功", taskId);

        } catch (Exception e) {
            log.error("上游账单处理失败,{}: {}", e.getMessage(), e);
            transactionManager.rollback(transStatus);
            throw new RuntimeException("上游账单处理失败，" + e.getMessage());
        }
    }

    private String reconciliationfile(int billType, List<SourceBill.Detail> sourceBillDetailList, String tradeMonth) {
        if (billType == SourceBillType.TYPE_LKL || billType == SourceBillType.TYPE_TL) {
            log.info("双向对比生成对比文件");
            //通联与拉卡拉计算交易笔数不太一致， 拉卡拉有效笔数= 支付+退款。 通联有效笔数= 支付
            int modifier = billType == SourceBillType.TYPE_LKL ? 1 : 0;
            List<TbSqbBillMonth> sqbBillList = billProcessHelper.getSqbBillList(BillProcessHelper.yyyymmWithLine(tradeMonth), billType);
            Multimap<String, TbSqbBillMonth> sqbBillMapList = sqbBillList
                    .stream()
                    .collect(ArrayListMultimap::create, (map, tbSqbBillMonth) -> map.put(tbSqbBillMonth.getSourceMerchantId(), tbSqbBillMonth), (stringTbSqbBillMonthMultimap, stringTbSqbBillMonthMultimap2) -> {
                    });
            Multimap<String, SourceBill.Detail> outBillList = sourceBillDetailList
                    .stream()
                    .collect(ArrayListMultimap::create, (map, tbSqbBillMonth) -> map.put(tbSqbBillMonth.getSourceMerchantId(), tbSqbBillMonth), (stringTbSqbBillMonthMultimap, stringTbSqbBillMonthMultimap2) -> {
                    });
            List<ReconciliationEntity> reconciliationList = new ArrayList<>();
            //外部匹配收钱吧
            for (SourceBill.Detail detail : sourceBillDetailList) {
                ArrayList<TbSqbBillMonth> sqbMap = new ArrayList<>(sqbBillMapList.get(detail.getSourceMerchantId()));
                if (sqbMap.size() > 1) {
                    throw new RuntimeException("拉卡拉账单关联收钱吧匹配到多个商户!");
                } else if (sqbMap.size() == 1) {
                    //生成关联数据
                    TbSqbBillMonth sqbBill = sqbMap.get(0);
                    ReconciliationEntity entity = new ReconciliationEntity();
                    entity.setSourceMerchantId(detail.getSourceMerchantId());
                    entity.setSourceMerchantName(detail.getSourceMerchantName());
                    entity.setSqbMerchantId(sqbBill.getSqbMerchantId());
                    entity.setSqbMerchantSn(sqbBill.getSqbMerchantSn());
                    entity.setSourceValidTradeNum(detail.getSourceValidTradeNum());
                    entity.setSqbTradeNum(sqbBill.getSqbTradeNum() + sqbBill.getSqbRefundNum() * modifier);
                    entity.setSourceValidTradeAmount(detail.getSourceValidTradeAmount());
                    entity.setSqbTradeMoney(sqbBill.getSqbTradeMoney() - sqbBill.getSqbRefundMoney());
                    entity.setSourceSettlementAmount(detail.getSourceSettlementAmount());
                    entity.setSqbSettlementAmount(((Double) NumberUtils.toDouble(sqbBill.getDimension())).longValue());
                    entity.setUnionServiceAmount(Long.parseLong(Optional.ofNullable(detail.getDimension()).orElse("0")));
                    reconciliationList.add(entity);
                } else {
                    //生成单侧数据
                    ReconciliationEntity entity = new ReconciliationEntity();
                    entity.setSourceMerchantId(detail.getSourceMerchantId());
                    entity.setSourceMerchantName(detail.getSourceMerchantName());
                    entity.setSourceValidTradeNum(detail.getSourceValidTradeNum());
                    entity.setSourceValidTradeAmount(detail.getSourceValidTradeAmount());
                    entity.setSourceSettlementAmount(detail.getSourceSettlementAmount());
                    entity.setUnionServiceAmount(Long.parseLong(Optional.ofNullable(detail.getDimension()).orElse("0")));
                    reconciliationList.add(entity);
                }
            }
            //收钱吧匹配外部
            for (TbSqbBillMonth tbSqbBillMonth : sqbBillList) {
                if (!outBillList.keySet().contains(tbSqbBillMonth.getSourceMerchantId())) {
                    //收钱吧存在, 外部不存在
                    ReconciliationEntity entity = new ReconciliationEntity();
                    entity.setSourceMerchantId(tbSqbBillMonth.getSourceMerchantId());
                    entity.setSqbMerchantId(tbSqbBillMonth.getSqbMerchantId());
                    entity.setSqbMerchantSn(tbSqbBillMonth.getSqbMerchantSn());
                    entity.setSqbTradeNum(tbSqbBillMonth.getSqbTradeNum() + tbSqbBillMonth.getSqbRefundNum() * modifier);
                    entity.setSqbTradeMoney(tbSqbBillMonth.getSqbTradeMoney() - tbSqbBillMonth.getSqbRefundMoney());
                    entity.setSqbSettlementAmount(NumberUtils.toLong(tbSqbBillMonth.getDimension()));
                    reconciliationList.add(entity);
                }
            }
            log.info("asyncDoExportBillList: 开始写入csv");
            // 写入 CSV
            CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader("上游商户号", "上游商户名称", "收钱吧商户号", "上游账单交易笔数", "收钱吧有效交易笔数", "上游账单交易金额（分）", "收钱吧有效交易金额（分）", "上游账单应结算总金额（分）", "收钱吧应结算总金额（分）", "银联品牌服务费", "上游较收钱吧交易笔数差异(笔)", "上游较收钱吧交易金额差异(分)", "交易金额差异百分比", "上游较收钱吧应结算总金额差异(分)", "应结算总金额差异百分比");
            //汇总数据
            Integer sumSourceValidTradeNum = 0;
            Long sumSourceValidTradeAmount = 0L;
            Integer sumSqbTradeNum = 0;
            Long sumSqbTradeMoney = 0L;
            Long sumSourceSettlementAmount = 0L;
            Long sumSqbSettlementAmount = 0L;
            List<List<String>> list = new ArrayList<>(20000);
            // 这是写入CSV的代码
            String fileFullPath = filePathBill + UUID.randomUUID().toString() + ".csv";
            try (Writer writer = new FileWriter(fileFullPath); CSVPrinter printer = new CSVPrinter(writer, csvFormat)) {
                for (ReconciliationEntity t : reconciliationList) {
                    //增加汇总数据
                    sumSourceValidTradeNum = sumSourceValidTradeNum + (t.getSourceValidTradeNum() == null ? 0 : t.getSourceValidTradeNum());
                    sumSourceValidTradeAmount = sumSourceValidTradeAmount + (t.getSourceValidTradeAmount() == null ? 0 : t.getSourceValidTradeAmount());
                    sumSqbTradeNum = sumSqbTradeNum + (t.getSqbTradeNum() == null ? 0 : t.getSqbTradeNum());
                    sumSqbTradeMoney = sumSqbTradeMoney + (t.getSqbTradeMoney() == null ? 0 : t.getSqbTradeMoney());
                    sumSourceSettlementAmount = sumSourceSettlementAmount + (t.getSourceSettlementAmount() == null ? 0 : t.getSourceSettlementAmount());
                    sumSqbSettlementAmount = sumSqbSettlementAmount + (t.getSqbSettlementAmount() == null ? 0 : t.getSqbSettlementAmount());

                }

                for (ReconciliationEntity t : reconciliationList) {
                    log.info("writer csv: {}", t);
                    List<String> csvLine = new ArrayList<>();
                    csvLine.add(t.getSourceMerchantId());
                    csvLine.add(t.getSourceMerchantName());
                    csvLine.add(t.getSqbMerchantSn());
                    csvLine.add(t.getSourceValidTradeNum() == null ? "0" : t.getSourceValidTradeNum().toString());
                    csvLine.add(t.getSqbTradeNum() == null ? "0" : t.getSqbTradeNum().toString());
                    csvLine.add(t.getSourceValidTradeAmount() == null ? "0" : t.getSourceValidTradeAmount().toString());
                    csvLine.add(t.getSqbTradeMoney() == null ? "0" : t.getSqbTradeMoney().toString());
                    csvLine.add(t.getSourceSettlementAmount() == null ? "0" : t.getSourceSettlementAmount().toString());
                    csvLine.add(t.getSqbSettlementAmount() == null ? "0" : t.getSqbSettlementAmount().toString());
                    csvLine.add(t.getUnionServiceAmount() == null ? "0" : t.getUnionServiceAmount().toString());

                    //上游较收钱吧交易笔数差异=上游账单交易笔数-收钱吧有效交易笔数
                    csvLine.add(String.valueOf(Integer.parseInt(csvLine.get(3)) - Integer.parseInt(csvLine.get(4))));

                    //上游较收钱吧交易金额差异=上游账单交易金额-收钱吧有效交易金额
                    csvLine.add(String.valueOf(Long.parseLong(csvLine.get(5)) - Long.parseLong(csvLine.get(6))));

                    //交易金额差异百分比=（上游账单交易金额-收钱吧有效交易金额）/ 本月收钱吧有效交易总金额
                    double amountDiffPercent = Long.parseLong(csvLine.get(csvLine.size() - 1)) / sumSqbTradeMoney.doubleValue() * 100000 * 100;
                    csvLine.add(new DecimalFormat("#.##").format(amountDiffPercent) + "%");

                    //上游较收钱吧应结算总金额差异=上游账单应结算总金额-收钱吧应结算总金额
                    csvLine.add(String.valueOf(Long.parseLong(csvLine.get(7)) - Long.parseLong(csvLine.get(8))));

                    //应结算总金额差异百分比=（上游账单应结算总金额-收钱吧应结算总金额）/ 本月收钱吧应结算总金额
                    double settlementAmountDiffPercent = Long.parseLong(csvLine.get(csvLine.size() - 1)) / sumSqbSettlementAmount.doubleValue() * 100000 * 100;
                    csvLine.add(new DecimalFormat("#.##").format(settlementAmountDiffPercent) + "%");

                    try {
                        printer.printRecord(csvLine);
                    } catch (IOException e) {
                        log.error("", e);
                    }
                }

                printer.printRecord("交易笔数差", "交易笔数百分差（上游账单交易总笔数-收钱吧有效交易总笔数）/ 收钱吧有效交易总笔数", "交易金额差", "交易金额百分差（上游账单交易总金额-收钱吧有效交易总金额）/收钱吧有效交易总金额", "应结算总金额差", "应结算总金额百分差（上游账单应结算总金额-收钱吧应结算总金额）/收钱吧应结算总金额");
                printer.printRecord(sumSourceValidTradeNum - sumSqbTradeNum, (sumSourceValidTradeNum - sumSqbTradeNum) / sumSqbTradeNum.doubleValue(),
                        sumSourceValidTradeAmount - sumSqbTradeMoney, (sumSourceValidTradeAmount - sumSqbTradeMoney) / sumSqbTradeMoney.doubleValue(),
                        sumSourceSettlementAmount - sumSqbSettlementAmount, (sumSourceSettlementAmount - sumSqbSettlementAmount) / sumSqbSettlementAmount.doubleValue());
                writer.flush();
            } catch (Exception e) {
                log.error("写入文件异常:", e);
            }
            log.info("开始上传oss: {}", fileFullPath);
            // 上传到 OSS
            String fileName = ossUtils.sendToOssPrivate(fileFullPath, true);
            if (StringUtils.isEmpty(fileName)) {
                log.error("message={}", "文件上传阿里云OSS失败");
            } else {
                log.info("操作成功={}", fileName);
                return fileName;
            }
        }
        return null;

    }


}
