package com.wosai.upay.transaction.cal.process.util;

import com.jcraft.jsch.*;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.util.UriUtils;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by lihebin on 05/03/2018.
 */
public class FtpUtil {

    private final static Logger log = LoggerFactory.getLogger(FtpUtil.class);


    public static boolean uploadFileSFTP(String host, String username, String password, int port, String path, String sftpFileName, InputStream input) {

        boolean success = false;

        Session session = null;
        Channel channel = null;
        try {
            JSch ssh = new JSch();
            // ssh.setKnownHosts("src/test/resources/files/priv");
            session = ssh.getSession(username, host, port);
            session.setPassword(password);
            // TODO set no host key checking : just pour le test
            session.setConfig("StrictHostKeyChecking", "no");
            session.connect();
            channel = session.openChannel("sftp");
            channel.connect();
            ChannelSftp sftp = (ChannelSftp) channel;
            log.info("Connexion SFTP sur " + host + " a ete etablir. ");
            String[] folders = path.split("/");
            for (String folder : folders) {
                if (folder.length() > 0) {
                    try {
                        sftp.cd(folder);
                    } catch (SftpException e) {
                        sftp.mkdir(folder);
                        sftp.cd(folder);
                    }
                }
            }
//            sftp.cd(path);
//            sftp.setFilenameEncoding("GBK");
            sftp.put(input, sftpFileName);

            success = true;
        } catch (Exception e) {
            log.error("Error: " ,e);
        } finally {
            if (channel != null) {
                channel.disconnect();
            }
            if (session != null) {
                session.disconnect();
            }
        }

        return success;
    }


    public static boolean uploadFileFTP(String host, String username, String password, int port, String path, String filename, InputStream input) {
        log.info("filename:{}", filename);

        String LOCAL_CHARSET = "GBK";
        String SERVER_CHARSET = "ISO-8859-1";
        boolean success = false;
        FTPClient ftp = new FTPClient();
        try {
            int reply;
            // on connecte le serveur ftp
            if (port == 0) {
                ftp.connect(host);
            } else {
                ftp.connect(host, port);
            }

            ftp.login(username, password);

            if (FTPReply.isPositiveCompletion(ftp.sendCommand(
                    "OPTS UTF8", "ON"))) {// 开启服务器对UTF-8的支持，如果服务器支持就用UTF-8编码，否则就使用本地编码（GBK）.
                LOCAL_CHARSET = "UTF-8";
            }
            ftp.setBufferSize(1024);
            ftp.setControlEncoding(LOCAL_CHARSET);
            reply = ftp.getReplyCode();
            log.info("reply:{}", reply);
            // on verifie si la connexion est bon
            if (!FTPReply.isPositiveCompletion(reply)) {
                ftp.disconnect();
                return success;
            }
            //检查上传路径是否存在 如果不存在返回false
            boolean flag = ftp.changeWorkingDirectory(path);
            if (!flag) {
                //创建上传的路径  该方法只能创建一级目录，在这里如果/home/<USER>
                boolean dirExists = true;

                //tokenize the string and attempt to change into each directory level.  If you cannot, then start creating.
                String[] directories = path.split("/");
                for (String dir : directories) {
                    if (!dir.isEmpty()) {
                        if (dirExists) {
                            dirExists = ftp.changeWorkingDirectory(dir);
                        }
                        if (!dirExists) {
                            if (!ftp.makeDirectory(dir)) {
                                throw new IOException("Unable to create remote directory '" + dir + "'.  error='" + ftp.getReplyString() + "'");
                            }
                            if (!ftp.changeWorkingDirectory(dir)) {
                                throw new IOException("Unable to change into newly created remote directory '" + dir + "'.  error='" + ftp.getReplyString() + "'");
                            }
                        }
                    }
                }

            }


            ftp.changeWorkingDirectory(path);
            //指定上传文件的类型  二进制文件

            ftp.setFileType(FTP.BINARY_FILE_TYPE);
            ftp.setDataTimeout(1000 * 60 * 20);
            ftp.enterLocalPassiveMode();
            ftp.setUseEPSVwithIPv4(false);
            for (int i = 0; i < 5; i++) {
                if (success) {
                    break;
                }
                try {
                    success = ftp.storeFile(new String(filename.getBytes(LOCAL_CHARSET),
                            SERVER_CHARSET), input);
                } catch (Exception e) {
                    log.error("Error: " + e.getMessage());
                }
            }

            log.info("result:{},{},{}", success, ftp.getReplyCode(), ftp.getReplyString());

            input.close();
            ftp.logout();
            log.info("success: " + filename + " to " + host);

        } catch (Exception e) {
            log.error("Error: ", e);
        } finally {
            if (ftp.isConnected()) {
                try {
                    ftp.disconnect();
                } catch (Exception ioe) {
                    log.error("Error: " + ioe.getMessage());
                }
            }
        }
        return success;
    }

    public static void main(String[] args) throws IOException {
//        File f = new File("test.txt");
////        f.();
//        InputStream inputStream = new FileInputStream(f);
        InputStream inputStream = new URL("http://wosai-images.oss-cn-hangzhou.aliyuncs.com/portal/transaction/42d25177-197d-41d7-95cd-8c33d05823fb/%E4%BC%81%E4%B8%9A%E6%89%A7%E7%85%A7%E5%AF%B9%E5%85%AC%E8%B4%A6%E6%88%B7web_%E5%AF%B9%E8%B4%A6%E5%8D%95%E6%B1%87%E6%80%BB_2019-10-23_2019-10-24.xlsx")
                .openStream();
//        uploadFileFTP("*************", "ftptest", "lSZVdTfOD1hSVaF2", 5188
//                , "task/21680002930062/20191023", "aaaa", inputStream);
        String filename = "哈哈哈哈哈bb/bb";
        filename = filename.replace("/","");
        System.out.println(filename);
        uploadFileSFTP("192.168.103.26","sftptest","hwr7KX90eG1wEGk9",5837,
                "/sh_pl_sftp",filename,inputStream);
    }

}
