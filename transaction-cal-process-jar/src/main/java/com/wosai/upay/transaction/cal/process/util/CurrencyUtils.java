package com.wosai.upay.transaction.cal.process.util;

import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * 货币工具类
 *
 * <AUTHOR>
 * @date 2019-09-17 13:31
 */
public final class CurrencyUtils {

    /**
     * String类型的元转long类型的分
     *
     * @param yuan String类型的元（两位小数位）
     * @return long类型的分（无小数位）
     */
    public static long yuanToFen(String yuan) {
        yuan = yuan.replace("\t", "");
        if (StringUtils.isBlank(yuan)) {
            return 0;
        }
        return new BigDecimal(yuan).multiply(new BigDecimal(100)).longValue();
    }

    /**
     * long类型的分转String类型的元
     *
     * @param fen long类型的分（无小数位）
     * @return yuan String类型的元（两位小数位）
     */
    public static String fenToYua(Long fen) {
        if (fen == null) {
            return "0";
        }
        return new BigDecimal(fen).divide(new BigDecimal(100)).setScale(2, RoundingMode.HALF_UP).toString();
    }

    /**
     * String类型的、带逗号的元转long类型的分
     */
    public static long yuanToFenWithoutComma(String yuanWithComma) {
        if (StringUtils.isEmpty(yuanWithComma)) {
            return 0;
        }
        return new BigDecimal(yuanWithComma.replace(",", "")).multiply(new BigDecimal(100)).longValue();
    }

    /**
     * 百分制转小数字符串
     */
    public static String convert100ToDf(String source) {
        if (source == null) {
            return "0";
        }

        return new BigDecimal(source).divide(new BigDecimal(100)).toString();
    }
}
