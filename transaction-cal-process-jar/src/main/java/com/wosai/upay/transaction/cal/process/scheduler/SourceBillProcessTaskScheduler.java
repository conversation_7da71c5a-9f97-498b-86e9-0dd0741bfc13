package com.wosai.upay.transaction.cal.process.scheduler;

import com.github.pagehelper.PageHelper;
import com.wosai.upay.transaction.cal.process.constant.SourceBillInputTask;
import com.wosai.upay.transaction.cal.process.mapper.TbSourceBillInputTaskMapper;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTask;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskExample;
import com.wosai.upay.transaction.cal.process.service.SourceBillServiceImpl;
import com.wosai.upay.transaction.cal.process.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.UUID;

/**
 * SourceBillProcessTaskScheduler
 *
 * <AUTHOR>
 * @date 2019-09-20 09:27
 */
@Slf4j
@Component
public class SourceBillProcessTaskScheduler {

    @Autowired
    private SourceBillServiceImpl sourceBillServiceImpl;

    @Autowired
    private TbSourceBillInputTaskMapper tbSourceBillInputTaskMapper;

    private final RedisUtils redisUtils;

    public SourceBillProcessTaskScheduler(RedisUtils redisUtils) {
        this.redisUtils = redisUtils;
    }

    /**
     * 扫描待处理的上游账单任务
     */
    @Scheduled(fixedDelay = 30000) // 每 5分钟 处理一次
    public void scanTasksTobeProcessed() {
        String lockKey = "transaction-cal-process:" + "scanTasksTobeProcessed";
        String lockValue = UUID.randomUUID().toString();
        log.info("scanTasksTobeProcessed");
        try {
            // 获取锁
            if (!redisUtils.setNX(lockKey, lockValue, 1800)) {
                return;
            }

            PageHelper.startPage(1, 50);
            TbSourceBillInputTaskExample example = new TbSourceBillInputTaskExample();
            // 创建时间在两天以内 - 5分钟以上
            Date createAtStart = new Date(System.currentTimeMillis() - 48 * 3600 * 1000);
            Date createAtEnd = new Date(System.currentTimeMillis() - 300000);
            example.or()
                    .andTaskStatusEqualTo(SourceBillInputTask.STATUS_NEW_TASK)
                    .andCreateAtGreaterThanOrEqualTo(createAtStart)
                    .andCreateAtLessThanOrEqualTo(createAtEnd);
            List<TbSourceBillInputTask> taskList = tbSourceBillInputTaskMapper.selectByExample(example);
            // 加入任务线程池
            taskList.stream().filter(Objects::nonNull).forEach(task -> sourceBillServiceImpl.addSourceBillTaskToPool(task));

        } catch (Throwable t) {
            log.error("schedule scanningTasksTobeProcessed error", t);
        } finally {
            redisUtils.deleteIfEqual(lockKey, lockValue);
        }
    }
}
