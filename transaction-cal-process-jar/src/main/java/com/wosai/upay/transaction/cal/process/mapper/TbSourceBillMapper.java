package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillExample;

import java.util.Date;
import java.util.List;

import com.wosai.upay.transaction.cal.process.processor.SourceBill;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface TbSourceBillMapper {
    long countByExample(TbSourceBillExample example);

    int deleteByExample(TbSourceBillExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TbSourceBill record);

    int insertSelective(TbSourceBill record);

    int batchInsert(@Param("sourceBillInputTaskId") Integer sourceBillInputTaskId,
                    @Param("tradeDateMonth") Date tradeDateMonth,
                    @Param("sourceBillType") Integer sourceBillType,
                    @Param("list") List<SourceBill.Detail> detailList);

    List<TbSourceBill> selectByExample(TbSourceBillExample example);

    TbSourceBill selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TbSourceBill record, @Param("example") TbSourceBillExample example);

    int updateByExample(@Param("record") TbSourceBill record, @Param("example") TbSourceBillExample example);

    int updateByPrimaryKeySelective(TbSourceBill record);

    int updateByPrimaryKey(TbSourceBill record);

    @Update("update tb_source_bill_type set config = #{config} where id = #{type}")
    int updateConfig(@Param("type") int type, @Param("config") byte[] config);
}