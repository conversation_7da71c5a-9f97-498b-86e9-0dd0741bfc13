package com.wosai.upay.transaction.cal.process.util;

import com.aliyun.oss.ClientBuilderConfiguration;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.common.auth.CredentialsProvider;
import com.aliyun.oss.model.*;
import com.wosai.middleware.aliyun.oss.DynamicCredentialsProvider;
import com.wosai.middleware.vault.Vault;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.io.*;
import java.net.URL;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.Date;
import java.util.stream.Collectors;

/**
 * OssUtils
 *
 * <AUTHOR>
 * @date 2019-10-21 14:49
 */
@Slf4j
@Component
public final class OssUtils {

    public static final String STATICS_BUCKET_NAME = "wosai-images";
    @Value("${oss.endpoint.url}")
    public String endpointUrl;

    private static final String SQB_CORE_BUCKET_NAME = "sqb-core";
    private static final String filePrefix = "private-pay-files/";
    private static final String filePathPrefix = "trans-bill";

    private OSSClient ossClient;

    @SneakyThrows
    @PostConstruct
    public void init(){
        Vault vault = Vault.autoload();
        CredentialsProvider credentialsProvider = new DynamicCredentialsProvider(vault);
        ossClient = new OSSClient(endpointUrl, credentialsProvider, new ClientBuilderConfiguration());
    }

    public String sendToOss(String filePath, boolean deleteFileAfterFinished) {
        File file = new File(filePath);
        try (InputStream inputStream = new FileInputStream(file)) {
            ObjectMetadata objectMeta = new ObjectMetadata();
            objectMeta.setContentLength(file.length());
            String key = "source-bill-splitting" + file.getName();
            ossClient.putObject(STATICS_BUCKET_NAME, key, inputStream, objectMeta);

            if (deleteFileAfterFinished) {
                file.deleteOnExit();
            }

            return key;
        } catch (Exception e) {
            log.error(" uploadToOss error", e);
        }
        return null;
    }

    public String sendToOssByFilePath(String filePath, boolean deleteFileAfterFinished) {
        File file = new File(filePath);
        try (InputStream inputStream = new FileInputStream(file)) {
            ObjectMetadata objectMeta = new ObjectMetadata();
            objectMeta.setContentLength(file.length());
            String key = filePathPrefix + filePath;
            ossClient.putObject(STATICS_BUCKET_NAME, key, inputStream, objectMeta);

            if (deleteFileAfterFinished) {
                file.deleteOnExit();
            }

            return key;
        } catch (Exception e) {
            log.error(" uploadToOss error", e);
        }
        return null;
    }

    public String downloadFileToOssByFilePath(String filePath) {
        try {
            String key = filePathPrefix + filePath;
            Path filePathObj = Paths.get(key);
            Path parentDir = filePathObj.getParent();

            if (parentDir != null && !Files.exists(parentDir)) {
                try {
                    Files.createDirectories(parentDir); // 如果目录不存在，则创建目录
                } catch (IOException e) {
                    log.error("创建目录异常:", e);
                }
            }

            DownloadFileRequest request = new DownloadFileRequest(STATICS_BUCKET_NAME, key);
            DownloadFileResult result = ossClient.downloadFile(request);

            if (StringUtils.isNotEmpty(result.getObjectMetadata().getETag())) {
                //下载成功
                return key;
            }
            // 下载失败
            return "";
        } catch (Exception e) {
            log.error(" uploadToOss error", e);
        } catch (Throwable e) {
            log.error(" uploadToOss throwable", e);
        }
        return null;
    }

    public String sendToOssPrivate(String filePath, boolean deleteFileAfterFinished){
        try {
            File file = new File(filePath);
            InputStream inputStream = new FileInputStream(file);
            ObjectMetadata objectMeta = new ObjectMetadata();
            objectMeta.setContentLength(file.length());
            String key = filePrefix + file.getName();
            ossClient.putObject(SQB_CORE_BUCKET_NAME, key, inputStream, objectMeta);
            //是否私有
            ossClient.setObjectAcl(SQB_CORE_BUCKET_NAME, key, CannedAccessControlList.Private);
            if (deleteFileAfterFinished) {
                file.deleteOnExit();
            }
            return file.getName();
        } catch (IOException e) {
            log.error(" uploadToOss error", e);
        }
        return null;
    }


    public String restoreUrl(String file){
        if (StringUtils.isEmpty(file)){
            log.error("文件名或文件url为空");
            throw new RuntimeException("文件名或文件url不能为空");
        }
        if (file.startsWith("http")){
            return file;
        }
        file = filePrefix + file;
        GeneratePresignedUrlRequest generatePresignedUrlRequest = new GeneratePresignedUrlRequest(SQB_CORE_BUCKET_NAME, file);
        generatePresignedUrlRequest.setExpiration(new Date(System.currentTimeMillis() + 600 * 1000));
        URL url = ossClient.generatePresignedUrl(generatePresignedUrlRequest);
        try {
            return URLDecoder.decode(url.toString(), "UTF-8");
        } catch (UnsupportedEncodingException e) {
            throw new RuntimeException("url error");
        }
    }

    public String trimUrl(String urlStr){
        if (!urlStr.contains("?")){
            return urlStr;
        }
        return Arrays.stream(urlStr.split(",")).map(url -> url.substring(url.lastIndexOf("/") + 1, url.lastIndexOf("?"))).collect(Collectors.joining(","));
    }
}
