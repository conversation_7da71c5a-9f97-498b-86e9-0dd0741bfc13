package com.wosai.upay.transaction.cal.process.scheduler;

import com.wosai.upay.transaction.cal.process.service.LarkService;
import com.wosai.upay.transaction.cal.process.service.OdpsService;
import com.wosai.upay.transaction.cal.process.util.CommonApolloUtil;
import com.wosai.upay.transaction.cal.process.util.EmailUtil;
import com.wosai.upay.user.api.util.DateUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 费率倒挂任务
 */
@Slf4j
@Component
public class FeeRateDeficitScheduler {
    private static final String FEE_RATE_DEFICIT_SQL_FORMAT = "SELECT  t1.tsn\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.trade_amount') / 100 AS totalAmt\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.sqb_fee_rate') AS sqb_fee_rate\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.channel_fee_rate') AS channel_fee_rate\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.sqb_fee') AS sqb_fee\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.channel_fee') AS channel_fee\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.provider') as provider\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.merchant_sn') as merchant_sn\n" +
            "        ,GET_JSON_OBJECT(t1.body,'$.combo_id') as combo_id\n" +
            "        ,tc.name as trade_combo_name\n" +
            "        ,m.level1_name\n" +
            "        ,m.org_name_path\n" +
            "        ,m.name\n" +
            "        ,m.industry_level1\n" +
            "        ,m.industry_level2\n" +
            "FROM    wosai_hz_main.ods_reconciliation_report_error_di t1\n" +
            "join wosai_main.dwb_d_crm_merchant m \n" +
            "on GET_JSON_OBJECT(t1.body,'$.merchant_sn') = m.sn \n" +
            "left join  wosai_hz_main.ods_trans_trade_combo_df tc\n" +
            "on    GET_JSON_OBJECT(t1.body,'$.combo_id') = tc.id \n" +
            "LEFT JOIN  wosai_main.dwb_f_trans_transaction t2\n" +
            "on t2.tsn = t1.tsn\n" +
            "WHERE \n" +
            " t1.pt=':task_date'\n" +
            "and m.pt=':task_date'\n" +
            "and t2.pt = ':transaction_date'\n" +
            "and GET_JSON_OBJECT(t2.extra_out_fields,'$.quota_fee_rate_tag') is NULL\n" +
            "AND t1.type = 2\n" +
            "and t2.type not in(11,10,14) \n" +
            "and (tc.id is NULL OR tc.id in(:combo_ids)) \n" +
            "and round(cast(trim(GET_JSON_OBJECT(t1.body,'$.sqb_fee_rate'),'%') AS DOUBLE ) - cast(trim(GET_JSON_OBJECT(t1.body,'$.channel_fee_rate'),'%') AS DOUBLE ) ,2) <  - 0.01;";

    private static final String DATE_FORMAT = "yyyyMMdd";
    private static final long ONE_DAY_TIMESTAMP = 1000l * 60 * 60 * 24;
    private static final String EMAIL_SUBJECT_FORMAT = "[%s]上游费率倒挂统计";
    private static final String EMAIL_CONTENT_FORMAT = "[%s]上游费率倒挂，<font color=#ff0000>总金额%s元</font>，套餐id为[空]或[%s]的流水统计";
    private static final String LARK_CONTENT_FORMAT = "费率倒挂告警：[%s]上游费率倒挂，总金额%s元，套餐id为[空]或[%s]的流水统计";


    @Autowired
    OdpsService odpsService;
    @Autowired
    LarkService larkService;

    @Value("${lark.chatbot.feeInversion}")
    private String robotUrl;

    /**
     * wosai_hz_main.ods_reconciliation_report_error_di表
     */
    @Scheduled(cron = "0 0 16 * * ?")
    public void doTask() {
        long lastDayTimestamp = System.currentTimeMillis() - ONE_DAY_TIMESTAMP;
        String date = new SimpleDateFormat(DATE_FORMAT).format(new Date(lastDayTimestamp));
        doTask(date);
    }

    public void doTask(String date){
        long transactionDateTs = DateUtil.parseDate(date, DATE_FORMAT).getTime() - ONE_DAY_TIMESTAMP;
        String transactionDate = new SimpleDateFormat(DATE_FORMAT).format(new Date(transactionDateTs));

        String comboIds = String.join(",", CommonApolloUtil.getComboIds());
        Map<String, String> params = new HashMap<>();
        params.put(":combo_ids", comboIds);
        params.put(":task_date", date);
        params.put(":transaction_date", transactionDate);
        String sql = FEE_RATE_DEFICIT_SQL_FORMAT;
        for (Map.Entry<String, String> param : params.entrySet()) {
            sql = sql.replaceAll(param.getKey(), param.getValue());
        }
        File file = odpsService.getCsvFile(sql);
        String subject = String.format(EMAIL_SUBJECT_FORMAT, transactionDate);
        String content = null;
        FileInputStream inputStream = null;
        try {
            long balanceAmount = 0;
            inputStream = new FileInputStream(file);
            BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
            //去掉行头
            bufferedReader.readLine();
            while (true){
                String line = bufferedReader.readLine();
                if(StringUtils.isNotEmpty(line)){
                    String[] fields = line.split(",");
                    long sqbFee = com.wosai.mpay.util.StringUtils.yuan2cents(fields[4]);
                    long channelFee = com.wosai.mpay.util.StringUtils.yuan2cents(fields[5]);
                    balanceAmount += (channelFee - sqbFee);
                    continue;
                }
                break;
            }
            content = String.format(EMAIL_CONTENT_FORMAT, transactionDate, com.wosai.mpay.util.StringUtils.cents2yuan(balanceAmount), comboIds);
            String larkContent = String.format(LARK_CONTENT_FORMAT, transactionDate, com.wosai.mpay.util.StringUtils.cents2yuan(balanceAmount), comboIds);
            if (balanceAmount > CommonApolloUtil.getDingAmountThreshold()) {
                LarkService.larkMessage(robotUrl, larkContent);
                if (inputStream != null) {
                    EmailUtil.sendToFile(subject, content, String.join(",", CommonApolloUtil.getSendTo()), null, subject + ".csv", inputStream);
                }else {
                    log.error("费率倒挂统计失败:{}", date);
                }
            }
            log.info("费率倒挂统计信息：", larkContent);
        } catch (FileNotFoundException e) {
            log.error("文件获取错误", e);
        } catch (IOException e) {
            log.error("文件读取异常", e);
        }
    }
}
