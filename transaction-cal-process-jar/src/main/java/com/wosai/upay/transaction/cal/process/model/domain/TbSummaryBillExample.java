package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class TbSummaryBillExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TbSummaryBillExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSnIsNull() {
            addCriterion("sn is null");
            return (Criteria) this;
        }

        public Criteria andSnIsNotNull() {
            addCriterion("sn is not null");
            return (Criteria) this;
        }

        public Criteria andSnEqualTo(String value) {
            addCriterion("sn =", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotEqualTo(String value) {
            addCriterion("sn <>", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThan(String value) {
            addCriterion("sn >", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnGreaterThanOrEqualTo(String value) {
            addCriterion("sn >=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThan(String value) {
            addCriterion("sn <", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLessThanOrEqualTo(String value) {
            addCriterion("sn <=", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnLike(String value) {
            addCriterion("sn like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotLike(String value) {
            addCriterion("sn not like", value, "sn");
            return (Criteria) this;
        }

        public Criteria andSnIn(List<String> values) {
            addCriterion("sn in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotIn(List<String> values) {
            addCriterion("sn not in", values, "sn");
            return (Criteria) this;
        }

        public Criteria andSnBetween(String value1, String value2) {
            addCriterion("sn between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andSnNotBetween(String value1, String value2) {
            addCriterion("sn not between", value1, value2, "sn");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIsNull() {
            addCriterion("policy_id is null");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIsNotNull() {
            addCriterion("policy_id is not null");
            return (Criteria) this;
        }

        public Criteria andPolicyIdEqualTo(String value) {
            addCriterion("policy_id =", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotEqualTo(String value) {
            addCriterion("policy_id <>", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdGreaterThan(String value) {
            addCriterion("policy_id >", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdGreaterThanOrEqualTo(String value) {
            addCriterion("policy_id >=", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLessThan(String value) {
            addCriterion("policy_id <", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLessThanOrEqualTo(String value) {
            addCriterion("policy_id <=", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLike(String value) {
            addCriterion("policy_id like", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotLike(String value) {
            addCriterion("policy_id not like", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIn(List<String> values) {
            addCriterion("policy_id in", values, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotIn(List<String> values) {
            addCriterion("policy_id not in", values, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdBetween(String value1, String value2) {
            addCriterion("policy_id between", value1, value2, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotBetween(String value1, String value2) {
            addCriterion("policy_id not between", value1, value2, "policyId");
            return (Criteria) this;
        }

        public Criteria andTradeMonthIsNull() {
            addCriterion("trade_month is null");
            return (Criteria) this;
        }

        public Criteria andTradeMonthIsNotNull() {
            addCriterion("trade_month is not null");
            return (Criteria) this;
        }

        public Criteria andTradeMonthEqualTo(Date value) {
            addCriterionForJDBCDate("trade_month =", value, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthNotEqualTo(Date value) {
            addCriterionForJDBCDate("trade_month <>", value, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthGreaterThan(Date value) {
            addCriterionForJDBCDate("trade_month >", value, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("trade_month >=", value, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthLessThan(Date value) {
            addCriterionForJDBCDate("trade_month <", value, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("trade_month <=", value, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthIn(List<Date> values) {
            addCriterionForJDBCDate("trade_month in", values, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthNotIn(List<Date> values) {
            addCriterionForJDBCDate("trade_month not in", values, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("trade_month between", value1, value2, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andTradeMonthNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("trade_month not between", value1, value2, "tradeMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthIsNull() {
            addCriterion("entry_month is null");
            return (Criteria) this;
        }

        public Criteria andEntryMonthIsNotNull() {
            addCriterion("entry_month is not null");
            return (Criteria) this;
        }

        public Criteria andEntryMonthEqualTo(Date value) {
            addCriterionForJDBCDate("entry_month =", value, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthNotEqualTo(Date value) {
            addCriterionForJDBCDate("entry_month <>", value, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthGreaterThan(Date value) {
            addCriterionForJDBCDate("entry_month >", value, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("entry_month >=", value, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthLessThan(Date value) {
            addCriterionForJDBCDate("entry_month <", value, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("entry_month <=", value, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthIn(List<Date> values) {
            addCriterionForJDBCDate("entry_month in", values, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthNotIn(List<Date> values) {
            addCriterionForJDBCDate("entry_month not in", values, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("entry_month between", value1, value2, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andEntryMonthNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("entry_month not between", value1, value2, "entryMonth");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIsNull() {
            addCriterion("source_bill_type is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIsNotNull() {
            addCriterion("source_bill_type is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeEqualTo(Integer value) {
            addCriterion("source_bill_type =", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotEqualTo(Integer value) {
            addCriterion("source_bill_type <>", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeGreaterThan(Integer value) {
            addCriterion("source_bill_type >", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_bill_type >=", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeLessThan(Integer value) {
            addCriterion("source_bill_type <", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("source_bill_type <=", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIn(List<Integer> values) {
            addCriterion("source_bill_type in", values, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotIn(List<Integer> values) {
            addCriterion("source_bill_type not in", values, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_type between", value1, value2, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_type not between", value1, value2, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumIsNull() {
            addCriterion("source_valid_trade_num is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumIsNotNull() {
            addCriterion("source_valid_trade_num is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumEqualTo(Integer value) {
            addCriterion("source_valid_trade_num =", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumNotEqualTo(Integer value) {
            addCriterion("source_valid_trade_num <>", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumGreaterThan(Integer value) {
            addCriterion("source_valid_trade_num >", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_valid_trade_num >=", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumLessThan(Integer value) {
            addCriterion("source_valid_trade_num <", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumLessThanOrEqualTo(Integer value) {
            addCriterion("source_valid_trade_num <=", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumIn(List<Integer> values) {
            addCriterion("source_valid_trade_num in", values, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumNotIn(List<Integer> values) {
            addCriterion("source_valid_trade_num not in", values, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_trade_num between", value1, value2, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumNotBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_trade_num not between", value1, value2, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountIsNull() {
            addCriterion("source_valid_trade_amount is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountIsNotNull() {
            addCriterion("source_valid_trade_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountEqualTo(Long value) {
            addCriterion("source_valid_trade_amount =", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountNotEqualTo(Long value) {
            addCriterion("source_valid_trade_amount <>", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountGreaterThan(Long value) {
            addCriterion("source_valid_trade_amount >", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("source_valid_trade_amount >=", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountLessThan(Long value) {
            addCriterion("source_valid_trade_amount <", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountLessThanOrEqualTo(Long value) {
            addCriterion("source_valid_trade_amount <=", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountIn(List<Long> values) {
            addCriterion("source_valid_trade_amount in", values, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountNotIn(List<Long> values) {
            addCriterion("source_valid_trade_amount not in", values, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountBetween(Long value1, Long value2) {
            addCriterion("source_valid_trade_amount between", value1, value2, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountNotBetween(Long value1, Long value2) {
            addCriterion("source_valid_trade_amount not between", value1, value2, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumIsNull() {
            addCriterion("source_valid_refund_num is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumIsNotNull() {
            addCriterion("source_valid_refund_num is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumEqualTo(Integer value) {
            addCriterion("source_valid_refund_num =", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumNotEqualTo(Integer value) {
            addCriterion("source_valid_refund_num <>", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumGreaterThan(Integer value) {
            addCriterion("source_valid_refund_num >", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_valid_refund_num >=", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumLessThan(Integer value) {
            addCriterion("source_valid_refund_num <", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumLessThanOrEqualTo(Integer value) {
            addCriterion("source_valid_refund_num <=", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumIn(List<Integer> values) {
            addCriterion("source_valid_refund_num in", values, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumNotIn(List<Integer> values) {
            addCriterion("source_valid_refund_num not in", values, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_refund_num between", value1, value2, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumNotBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_refund_num not between", value1, value2, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountIsNull() {
            addCriterion("source_valid_refund_amount is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountIsNotNull() {
            addCriterion("source_valid_refund_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountEqualTo(Long value) {
            addCriterion("source_valid_refund_amount =", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountNotEqualTo(Long value) {
            addCriterion("source_valid_refund_amount <>", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountGreaterThan(Long value) {
            addCriterion("source_valid_refund_amount >", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("source_valid_refund_amount >=", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountLessThan(Long value) {
            addCriterion("source_valid_refund_amount <", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountLessThanOrEqualTo(Long value) {
            addCriterion("source_valid_refund_amount <=", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountIn(List<Long> values) {
            addCriterion("source_valid_refund_amount in", values, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountNotIn(List<Long> values) {
            addCriterion("source_valid_refund_amount not in", values, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountBetween(Long value1, Long value2) {
            addCriterion("source_valid_refund_amount between", value1, value2, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountNotBetween(Long value1, Long value2) {
            addCriterion("source_valid_refund_amount not between", value1, value2, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeIsNull() {
            addCriterion("source_settlement_basis_type is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeIsNotNull() {
            addCriterion("source_settlement_basis_type is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeEqualTo(String value) {
            addCriterion("source_settlement_basis_type =", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotEqualTo(String value) {
            addCriterion("source_settlement_basis_type <>", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeGreaterThan(String value) {
            addCriterion("source_settlement_basis_type >", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeGreaterThanOrEqualTo(String value) {
            addCriterion("source_settlement_basis_type >=", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeLessThan(String value) {
            addCriterion("source_settlement_basis_type <", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeLessThanOrEqualTo(String value) {
            addCriterion("source_settlement_basis_type <=", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeLike(String value) {
            addCriterion("source_settlement_basis_type like", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotLike(String value) {
            addCriterion("source_settlement_basis_type not like", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeIn(List<String> values) {
            addCriterion("source_settlement_basis_type in", values, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotIn(List<String> values) {
            addCriterion("source_settlement_basis_type not in", values, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeBetween(String value1, String value2) {
            addCriterion("source_settlement_basis_type between", value1, value2, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotBetween(String value1, String value2) {
            addCriterion("source_settlement_basis_type not between", value1, value2, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisIsNull() {
            addCriterion("source_settlement_basis is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisIsNotNull() {
            addCriterion("source_settlement_basis is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisEqualTo(Long value) {
            addCriterion("source_settlement_basis =", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisNotEqualTo(Long value) {
            addCriterion("source_settlement_basis <>", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisGreaterThan(Long value) {
            addCriterion("source_settlement_basis >", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisGreaterThanOrEqualTo(Long value) {
            addCriterion("source_settlement_basis >=", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisLessThan(Long value) {
            addCriterion("source_settlement_basis <", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisLessThanOrEqualTo(Long value) {
            addCriterion("source_settlement_basis <=", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisIn(List<Long> values) {
            addCriterion("source_settlement_basis in", values, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisNotIn(List<Long> values) {
            addCriterion("source_settlement_basis not in", values, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisBetween(Long value1, Long value2) {
            addCriterion("source_settlement_basis between", value1, value2, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisNotBetween(Long value1, Long value2) {
            addCriterion("source_settlement_basis not between", value1, value2, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountIsNull() {
            addCriterion("source_settlement_amount is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountIsNotNull() {
            addCriterion("source_settlement_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountEqualTo(Long value) {
            addCriterion("source_settlement_amount =", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountNotEqualTo(Long value) {
            addCriterion("source_settlement_amount <>", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountGreaterThan(Long value) {
            addCriterion("source_settlement_amount >", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("source_settlement_amount >=", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountLessThan(Long value) {
            addCriterion("source_settlement_amount <", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountLessThanOrEqualTo(Long value) {
            addCriterion("source_settlement_amount <=", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountIn(List<Long> values) {
            addCriterion("source_settlement_amount in", values, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountNotIn(List<Long> values) {
            addCriterion("source_settlement_amount not in", values, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountBetween(Long value1, Long value2) {
            addCriterion("source_settlement_amount between", value1, value2, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountNotBetween(Long value1, Long value2) {
            addCriterion("source_settlement_amount not between", value1, value2, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberIsNull() {
            addCriterion("source_merchant_number is null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberIsNotNull() {
            addCriterion("source_merchant_number is not null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberEqualTo(Long value) {
            addCriterion("source_merchant_number =", value, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberNotEqualTo(Long value) {
            addCriterion("source_merchant_number <>", value, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberGreaterThan(Long value) {
            addCriterion("source_merchant_number >", value, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("source_merchant_number >=", value, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberLessThan(Long value) {
            addCriterion("source_merchant_number <", value, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberLessThanOrEqualTo(Long value) {
            addCriterion("source_merchant_number <=", value, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberIn(List<Long> values) {
            addCriterion("source_merchant_number in", values, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberNotIn(List<Long> values) {
            addCriterion("source_merchant_number not in", values, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberBetween(Long value1, Long value2) {
            addCriterion("source_merchant_number between", value1, value2, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNumberNotBetween(Long value1, Long value2) {
            addCriterion("source_merchant_number not between", value1, value2, "sourceMerchantNumber");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeIsNull() {
            addCriterion("server_channel_code is null");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeIsNotNull() {
            addCriterion("server_channel_code is not null");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeEqualTo(String value) {
            addCriterion("server_channel_code =", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeNotEqualTo(String value) {
            addCriterion("server_channel_code <>", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeGreaterThan(String value) {
            addCriterion("server_channel_code >", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeGreaterThanOrEqualTo(String value) {
            addCriterion("server_channel_code >=", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeLessThan(String value) {
            addCriterion("server_channel_code <", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeLessThanOrEqualTo(String value) {
            addCriterion("server_channel_code <=", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeLike(String value) {
            addCriterion("server_channel_code like", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeNotLike(String value) {
            addCriterion("server_channel_code not like", value, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeIn(List<String> values) {
            addCriterion("server_channel_code in", values, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeNotIn(List<String> values) {
            addCriterion("server_channel_code not in", values, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeBetween(String value1, String value2) {
            addCriterion("server_channel_code between", value1, value2, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andServerChannelCodeNotBetween(String value1, String value2) {
            addCriterion("server_channel_code not between", value1, value2, "serverChannelCode");
            return (Criteria) this;
        }

        public Criteria andNcNameIsNull() {
            addCriterion("nc_name is null");
            return (Criteria) this;
        }

        public Criteria andNcNameIsNotNull() {
            addCriterion("nc_name is not null");
            return (Criteria) this;
        }

        public Criteria andNcNameEqualTo(String value) {
            addCriterion("nc_name =", value, "ncName");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNull() {
            addCriterion("company_name is null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameIsNotNull() {
            addCriterion("company_name is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyNameEqualTo(String value) {
            addCriterion("company_name =", value, "companyName");
            return (Criteria) this;
        }

        public Criteria andPropertyIdIsNull() {
            addCriterion("property_id is null");
            return (Criteria) this;
        }

        public Criteria andPropertyIdIsNotNull() {
            addCriterion("property_id is not null");
            return (Criteria) this;
        }

        public Criteria andPropertyIdEqualTo(String value) {
            addCriterion("property_id =", value, "propertyId");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(String value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(String value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(String value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(String value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(String value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(String value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLike(String value) {
            addCriterion("status like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotLike(String value) {
            addCriterion("status not like", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<String> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<String> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(String value1, String value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(String value1, String value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andPushStatusIsNull() {
            addCriterion("push_status is null");
            return (Criteria) this;
        }

        public Criteria andPushStatusIsNotNull() {
            addCriterion("push_status is not null");
            return (Criteria) this;
        }

        public Criteria andPushStatusEqualTo(String value) {
            addCriterion("push_status =", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusNotEqualTo(String value) {
            addCriterion("push_status <>", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusGreaterThan(String value) {
            addCriterion("push_status >", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusGreaterThanOrEqualTo(String value) {
            addCriterion("push_status >=", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusLessThan(String value) {
            addCriterion("push_status <", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusLessThanOrEqualTo(String value) {
            addCriterion("push_status <=", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusLike(String value) {
            addCriterion("push_status like", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusNotLike(String value) {
            addCriterion("push_status not like", value, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusIn(List<String> values) {
            addCriterion("push_status in", values, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusNotIn(List<String> values) {
            addCriterion("push_status not in", values, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusBetween(String value1, String value2) {
            addCriterion("push_status between", value1, value2, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andPushStatusNotBetween(String value1, String value2) {
            addCriterion("push_status not between", value1, value2, "pushStatus");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Date value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Date value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Date value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Date value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Date value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Date> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Date> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Date value1, Date value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Date value1, Date value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Date value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Date value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Date value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Date value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Date value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Date value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Date> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Date> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Date value1, Date value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Date value1, Date value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}