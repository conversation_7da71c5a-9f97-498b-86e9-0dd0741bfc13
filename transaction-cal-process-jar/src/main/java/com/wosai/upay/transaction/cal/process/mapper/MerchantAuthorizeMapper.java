package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize;
import com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorizeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface MerchantAuthorizeMapper {
    long countByExample(MerchantAuthorizeExample example);

    int deleteByExample(MerchantAuthorizeExample example);

    int deleteByPrimaryKey(Long id);

    int insert(MerchantAuthorize record);

    int insertSelective(MerchantAuthorize record);

    List<MerchantAuthorize> selectByExample(MerchantAuthorizeExample example);

    MerchantAuthorize selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") MerchantAuthorize record, @Param("example") MerchantAuthorizeExample example);

    int updateByExample(@Param("record") MerchantAuthorize record, @Param("example") MerchantAuthorizeExample example);

    int updateByPrimaryKeySelective(MerchantAuthorize record);

    int updateByPrimaryKey(MerchantAuthorize record);

    List<MerchantAuthorize> selectByExampleWithPage(@Param("example") MerchantAuthorizeExample example,
                                                    @Param("offset") int offset,
                                                    @Param("limit") int limit);

}