package com.wosai.upay.transaction.cal.process.scheduler;

import com.wosai.upay.transaction.cal.process.service.LakalaIsvDataService;
import com.wosai.upay.transaction.cal.process.service.LarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import java.text.SimpleDateFormat;
import java.util.Date;


/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2022/4/8.
 */

@Slf4j
@Component
public class LakalaIsvDataScheduler {
    @Autowired
    private LakalaIsvDataService lakalaIsvDataService;
    @Autowired
    private LarkService larkService;
    @Value("${lakala.send-data-enable}")
    private boolean lakalaSendDataEnable;


    @Scheduled(cron = "0 30 7 * * ?")
    public void send(){
        if(!lakalaSendDataEnable){
            return;
        }
        String date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        try{
            lakalaIsvDataService.send(date);
        }catch (Exception e){
            larkService.larkMessage("拉卡拉isv每日数据上送异常" + e.getMessage());
            log.error("拉卡拉isv每日数据上送异常 " + e.getMessage(), e);
        }
    }
}
