package com.wosai.upay.transaction.cal.process.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Encoder;

import javax.activation.DataHandler;
import javax.activation.DataSource;
import javax.activation.MimeType;
import javax.mail.*;
import javax.mail.internet.*;
import javax.mail.util.ByteArrayDataSource;
import java.io.InputStream;
import java.util.Date;
import java.util.Properties;

/**
 * Created by Administrator on 2017/1/3.
 */
public class EmailUtil {
    private final static Logger log = LoggerFactory.getLogger(EmailUtil.class);

//    static Config config = ConfigService.getAppConfig();

    /**
     * @param subject 标题
     * @param content 内容
     * @param to      收件人
     */
    public static void sendToFile(String subject, String content, String to, String copyto, String fileName, InputStream... files) {
        if (to == null || to.equals("") || content == null || content.equals("")) {
            return;
        }
        try {
            Session session = sendConfig();
            String from = "<EMAIL>";
            String password = "sMIzB0AEP9oh1MX6";
            log.info(String.format("sending email from %s to %s with content %s", from, to, content));
            //2.根据配置创建会话对象，用于和邮件服务器交互
            //3.创建一封邮件
            MimeMessage message = createMimeMessageFile(session, from, to, copyto, subject, content, fileName, files);
            sendEndConfig(session, message, from, password);
            log.info("邮件发送成功 to" + to);
        } catch (MessagingException e) {
            log.warn("邮件发送失败 to " + to, e);
        }
    }


    private static void sendEndConfig(Session session, MimeMessage message, String from, String password) throws MessagingException {
        //4.根据Session获取邮件传输对象

        Transport transport = session.getTransport();

        //5.使用邮箱账号和密码连接邮件服务器
        //这里认证的邮箱必须与message中的发件人邮箱一致，否则报错
        transport.connect(from, password);

        //6.发送邮件，发到所有的收件地址，message.getAllRecipients()获取到的是在创建邮件对象时添加的所有收件人，抄送人，密送人
        transport.sendMessage(message, message.getAllRecipients());

        //7.关闭连接
        transport.close();

    }


    private static Session sendConfig() {
        Properties props = new Properties();//参数配置
        //1.创建参数配置，用于连接邮箱服务器的参数配置
        String smtpHost = "smtp.feishu.cn";
        String port = "465";
        props.setProperty("mail.transport.protocol", "smtp");//使用的协议（JavaMail规范要求）
        props.setProperty("mail.host", smtpHost);  //发件人的邮箱的SMIP服务器地址
        props.setProperty("mail.port", port);  //发件人的邮箱的SMIP服务器端口
        props.setProperty("mail.smtp.auth", "true"); //请求认证，参数名称与具体实现有关
        props.setProperty("mail.smtp.ssl.enable", "true");
        return Session.getDefaultInstance(props);
    }


    /**
     * @param session
     * @param from
     * @param to
     * @param subject
     * @param content
     * @return
     */
    private static MimeMessage createMimeMessageFile(Session session, String from, String to, String copyto, String subject, String content, String fileName, InputStream... files) throws MessagingException {
        //1.创建一封邮件
        MimeMessage message = new MimeMessage(session);
        Multipart multipart = new MimeMultipart();
        sendToMessage(message, from, to, copyto, subject, content, multipart);
        try {
            //6.添加附件
            for (InputStream emailFile : files) {
                MimeBodyPart fileBody = new MimeBodyPart();
                DataSource source = new ByteArrayDataSource(emailFile, new MimeType().getBaseType());
                fileBody.setDataHandler(new DataHandler(source));
                BASE64Encoder enc = new BASE64Encoder();
                fileBody.setFileName(MimeUtility.encodeText(fileName));
//                fileBody.setFileName(fileName);
                multipart.addBodyPart(fileBody);
            }
        } catch (Exception e) {
            log.error(String.valueOf(e));
        }
        message.setContent(multipart);

        //7.设置发送时间
        message.setSentDate(new Date());

        //8.保存设置
        message.saveChanges();

        return message;
    }


    private static void sendToMessage(MimeMessage message, String from, String to, String copyto, String subject, String content, Multipart multipart) throws MessagingException {
        //2.From:发件人
        message.setFrom(new InternetAddress(from));

        //3.To:收件人(可以增加多个收件人、抄送、密送)
        String[] addrs = to.split(",");


        int count = addrs.length;

        InternetAddress[] address = new InternetAddress[count];
        for (int i = 0; i < count; i++) {
            address[i] = new InternetAddress(addrs[i]);
        }
        message.setRecipients(MimeMessage.RecipientType.TO, address);
        if (!StringUtil.empty(copyto)) {
            String[] copyAddrs = copyto.split(",");
            int copyCount = copyAddrs.length;
            InternetAddress[] copyaddress = new InternetAddress[copyCount];
            for (int i = 0; i < copyCount; i++) {
                copyaddress[i] = new InternetAddress(copyAddrs[i]);
            }
            message.setRecipients(MimeMessage.RecipientType.CC, copyaddress);
        }
        //4.Subject:邮件主题
        message.setSubject(subject, "UTF-8");

        //5.Content:邮件正文（可以使用html标签）
//        message.setContent(content, "text/html;charset=UTF-8");

        BodyPart contentPart = new MimeBodyPart();
        contentPart.setText(content);

        contentPart.setHeader("Content-Type", "text/html;charset=UTF-8");
        multipart.addBodyPart(contentPart);
    }

    public static void main(String[] args) {
//        send("测试", "测试1", "<EMAIL>");
//        sendSubmailCopy("test", "test", "<EMAIL>", "");
//        send("test", "test", "<EMAIL>");
    }


}
