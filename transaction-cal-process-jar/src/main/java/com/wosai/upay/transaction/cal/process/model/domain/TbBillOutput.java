package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.Date;
import java.util.Map;

public class TbBillOutput {
    private Long id;

    private Integer sourceBillInputTaskId;

    private Date tradeDateMonth;

    private Integer sourceBillType;

    private Integer subNum;

    private String sourceMerchantId;

    private String sourceMerchantName;

    private String sourceLevel2MerchantId;

    private String sourceLevel2MerchantName;

    private String sqbMerchantId;

    private String sqbMerchantSn;

    private String dimension;

    private Integer sourceValidTradeNum;

    private Long sourceValidTradeAmount;

    private Integer sourceValidRefundNum;

    private Long sourceValidRefundAmount;

    private String sourceValidTradeAmountRatio;

    private String sourceSettlementBasisType;

    private Long sourceSettlementBasis;

    private String sourceMerchantFeeRate;

    private String sourceSettlementFeeRate;

    private Long sourceSettlementAmount;

    private Integer sqbTradeNum;

    private Long sqbTradeAmount;

    private Integer sqbRefundNum;

    private Long sqbRefundAmount;

    private Integer sqbClearingNum;

    private Long sqbClearingAmount;

    private String remark;

    private Boolean isImport;

    private Date createAt;

    private Date updateAt;

    private String cappingRate;

    private Map<String, Object> extraParams;

    public Map<String, Object> getExtraParams() {
        return extraParams;
    }

    public void setExtraParams(Map<String, Object> extraParams) {
        this.extraParams = extraParams;
    }

    /**
     * 服务商唯一标识（ID/PID/NO）
     */
    private String serviceProviderId;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getSourceBillInputTaskId() {
        return sourceBillInputTaskId;
    }

    public void setSourceBillInputTaskId(Integer sourceBillInputTaskId) {
        this.sourceBillInputTaskId = sourceBillInputTaskId;
    }

    public Date getTradeDateMonth() {
        return tradeDateMonth;
    }

    public void setTradeDateMonth(Date tradeDateMonth) {
        this.tradeDateMonth = tradeDateMonth;
    }

    public Integer getSourceBillType() {
        return sourceBillType;
    }

    public void setSourceBillType(Integer sourceBillType) {
        this.sourceBillType = sourceBillType;
    }

    public Integer getSubNum() {
        return subNum;
    }

    public void setSubNum(Integer subNum) {
        this.subNum = subNum;
    }

    public String getSourceMerchantId() {
        return sourceMerchantId;
    }

    public void setSourceMerchantId(String sourceMerchantId) {
        this.sourceMerchantId = sourceMerchantId;
    }

    public String getSourceMerchantName() {
        return sourceMerchantName;
    }

    public void setSourceMerchantName(String sourceMerchantName) {
        this.sourceMerchantName = sourceMerchantName;
    }

    public String getSourceLevel2MerchantId() {
        return sourceLevel2MerchantId;
    }

    public void setSourceLevel2MerchantId(String sourceLevel2MerchantId) {
        this.sourceLevel2MerchantId = sourceLevel2MerchantId;
    }

    public String getSourceLevel2MerchantName() {
        return sourceLevel2MerchantName;
    }

    public void setSourceLevel2MerchantName(String sourceLevel2MerchantName) {
        this.sourceLevel2MerchantName = sourceLevel2MerchantName;
    }

    public String getSqbMerchantId() {
        return sqbMerchantId;
    }

    public void setSqbMerchantId(String sqbMerchantId) {
        this.sqbMerchantId = sqbMerchantId;
    }

    public String getSqbMerchantSn() {
        return sqbMerchantSn;
    }

    public void setSqbMerchantSn(String sqbMerchantSn) {
        this.sqbMerchantSn = sqbMerchantSn;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Integer getSourceValidTradeNum() {
        return sourceValidTradeNum;
    }

    public void setSourceValidTradeNum(Integer sourceValidTradeNum) {
        this.sourceValidTradeNum = sourceValidTradeNum;
    }

    public Long getSourceValidTradeAmount() {
        return sourceValidTradeAmount;
    }

    public void setSourceValidTradeAmount(Long sourceValidTradeAmount) {
        this.sourceValidTradeAmount = sourceValidTradeAmount;
    }

    public Integer getSourceValidRefundNum() {
        return sourceValidRefundNum;
    }

    public void setSourceValidRefundNum(Integer sourceValidRefundNum) {
        this.sourceValidRefundNum = sourceValidRefundNum;
    }

    public Long getSourceValidRefundAmount() {
        return sourceValidRefundAmount;
    }

    public void setSourceValidRefundAmount(Long sourceValidRefundAmount) {
        this.sourceValidRefundAmount = sourceValidRefundAmount;
    }

    public String getSourceValidTradeAmountRatio() {
        return sourceValidTradeAmountRatio;
    }

    public void setSourceValidTradeAmountRatio(String sourceValidTradeAmountRatio) {
        this.sourceValidTradeAmountRatio = sourceValidTradeAmountRatio;
    }

    public String getSourceSettlementBasisType() {
        return sourceSettlementBasisType;
    }

    public void setSourceSettlementBasisType(String sourceSettlementBasisType) {
        this.sourceSettlementBasisType = sourceSettlementBasisType;
    }

    public Long getSourceSettlementBasis() {
        return sourceSettlementBasis;
    }

    public void setSourceSettlementBasis(Long sourceSettlementBasis) {
        this.sourceSettlementBasis = sourceSettlementBasis;
    }

    public String getSourceMerchantFeeRate() {
        return sourceMerchantFeeRate;
    }

    public void setSourceMerchantFeeRate(String sourceMerchantFeeRate) {
        this.sourceMerchantFeeRate = sourceMerchantFeeRate;
    }

    public String getSourceSettlementFeeRate() {
        return sourceSettlementFeeRate;
    }

    public void setSourceSettlementFeeRate(String sourceSettlementFeeRate) {
        this.sourceSettlementFeeRate = sourceSettlementFeeRate;
    }

    public Long getSourceSettlementAmount() {
        return sourceSettlementAmount;
    }

    public void setSourceSettlementAmount(Long sourceSettlementAmount) {
        this.sourceSettlementAmount = sourceSettlementAmount;
    }

    public Integer getSqbTradeNum() {
        return sqbTradeNum;
    }

    public void setSqbTradeNum(Integer sqbTradeNum) {
        this.sqbTradeNum = sqbTradeNum;
    }

    public Long getSqbTradeAmount() {
        return sqbTradeAmount;
    }

    public void setSqbTradeAmount(Long sqbTradeAmount) {
        this.sqbTradeAmount = sqbTradeAmount;
    }

    public Integer getSqbRefundNum() {
        return sqbRefundNum;
    }

    public void setSqbRefundNum(Integer sqbRefundNum) {
        this.sqbRefundNum = sqbRefundNum;
    }

    public Long getSqbRefundAmount() {
        return sqbRefundAmount;
    }

    public void setSqbRefundAmount(Long sqbRefundAmount) {
        this.sqbRefundAmount = sqbRefundAmount;
    }

    public Integer getSqbClearingNum() {
        return sqbClearingNum;
    }

    public void setSqbClearingNum(Integer sqbClearingNum) {
        this.sqbClearingNum = sqbClearingNum;
    }

    public Long getSqbClearingAmount() {
        return sqbClearingAmount;
    }

    public void setSqbClearingAmount(Long sqbClearingAmount) {
        this.sqbClearingAmount = sqbClearingAmount;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Boolean getIsImport() {
        return isImport;
    }

    public void setIsImport(Boolean isImport) {
        this.isImport = isImport;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public String getCappingRate() {
        return cappingRate;
    }

    public void setCappingRate(String cappingRate) {
        this.cappingRate = cappingRate;
    }

    public String getServiceProviderId() {
        return serviceProviderId;
    }

    public void setServiceProviderId(String serviceProviderId) {
        this.serviceProviderId = serviceProviderId;
    }

}