package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.Date;

public class TbSourceBillType {
    private Integer id;

    private String name;

    private Byte sourceBillCompany;

    private String sourceBillCompanyName;

    private Byte sourceBillClassify;

    private String sourceBillClassifyName;

    private Byte sourceBillCycle;

    private String sourceBillCycleName;

    private Byte sourceBillInputMethod;

    private String sourceBillInputMethodName;

    private Byte splitStrategy;

    private String splitStrategyName;

    private String policyId;

    private String importTemplateRuleCode;

    private Boolean deleted;

    private Date createAt;

    private Date updateAt;

    private byte[] config;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Byte getSourceBillCompany() {
        return sourceBillCompany;
    }

    public void setSourceBillCompany(Byte sourceBillCompany) {
        this.sourceBillCompany = sourceBillCompany;
    }

    public String getSourceBillCompanyName() {
        return sourceBillCompanyName;
    }

    public void setSourceBillCompanyName(String sourceBillCompanyName) {
        this.sourceBillCompanyName = sourceBillCompanyName;
    }

    public Byte getSourceBillClassify() {
        return sourceBillClassify;
    }

    public void setSourceBillClassify(Byte sourceBillClassify) {
        this.sourceBillClassify = sourceBillClassify;
    }

    public String getSourceBillClassifyName() {
        return sourceBillClassifyName;
    }

    public void setSourceBillClassifyName(String sourceBillClassifyName) {
        this.sourceBillClassifyName = sourceBillClassifyName;
    }

    public Byte getSourceBillCycle() {
        return sourceBillCycle;
    }

    public void setSourceBillCycle(Byte sourceBillCycle) {
        this.sourceBillCycle = sourceBillCycle;
    }

    public String getSourceBillCycleName() {
        return sourceBillCycleName;
    }

    public void setSourceBillCycleName(String sourceBillCycleName) {
        this.sourceBillCycleName = sourceBillCycleName;
    }

    public Byte getSourceBillInputMethod() {
        return sourceBillInputMethod;
    }

    public void setSourceBillInputMethod(Byte sourceBillInputMethod) {
        this.sourceBillInputMethod = sourceBillInputMethod;
    }

    public String getSourceBillInputMethodName() {
        return sourceBillInputMethodName;
    }

    public void setSourceBillInputMethodName(String sourceBillInputMethodName) {
        this.sourceBillInputMethodName = sourceBillInputMethodName;
    }

    public Byte getSplitStrategy() {
        return splitStrategy;
    }

    public void setSplitStrategy(Byte splitStrategy) {
        this.splitStrategy = splitStrategy;
    }

    public String getSplitStrategyName() {
        return splitStrategyName;
    }

    public void setSplitStrategyName(String splitStrategyName) {
        this.splitStrategyName = splitStrategyName;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        this.policyId = policyId;
    }

    public String getImportTemplateRuleCode() {
        return importTemplateRuleCode;
    }

    public void setImportTemplateRuleCode(String importTemplateRuleCode) {
        this.importTemplateRuleCode = importTemplateRuleCode;
    }

    public Boolean getDeleted() {
        return deleted;
    }

    public void setDeleted(Boolean deleted) {
        this.deleted = deleted;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }

    public byte[] getConfig() {
        return config;
    }

    public void setConfig(byte[] config) {
        this.config = config;
    }
}