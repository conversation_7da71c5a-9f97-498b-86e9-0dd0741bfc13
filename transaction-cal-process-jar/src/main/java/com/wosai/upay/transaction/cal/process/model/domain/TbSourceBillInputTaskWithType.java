package com.wosai.upay.transaction.cal.process.model.domain;

import com.wosai.upay.transaction.cal.process.model.dto.TradeInfoDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * TbSourceBillInputTaskWithType
 *
 * <AUTHOR>
 * @date 2019-09-17 02:23
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class TbSourceBillInputTaskWithType extends TbSourceBillInputTaskWithBLOBs {

    private TbSourceBillType tbSourceBillType;

    private TradeInfoDto tradeInfo;

}
