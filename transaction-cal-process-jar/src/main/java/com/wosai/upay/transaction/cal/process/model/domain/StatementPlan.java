package com.wosai.upay.transaction.cal.process.model.domain;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;
@Data
@Accessors(chain = true)
public class StatementPlan {
    private Long id;
    private String name;
    private Integer agreementType;
    private Integer status;
    private String createBy;
    private Date createAt;
    private Date updateAt;
    private Date deleteAt;
    private String email;
    private String agreementConfig;
    private String statementConfig;
    private String comment;

    @Override
    public String toString() {
        return "StatementPlan{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", agreementType=" + agreementType +
                ", status=" + status +
                ", createBy='" + createBy + '\'' +
                ", createAt=" + createAt +
                ", updateAt=" + updateAt +
                ", deleteAt=" + deleteAt +
                ", email='" + email + '\'' +
                ", statementConfig='" + statementConfig + '\'' +
                ", comment='" + comment + '\'' +
                '}';
    }
}