package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.Date;

public class TbSqbBillMonth {
    private Integer id;

    private String tradeMonth;

    private Integer billType;

    private String sourceMerchantId;

    private String sqbMerchantId;

    private Integer sqbTradeNum;

    private Long sqbTradeMoney;

    private Integer sqbRefundNum;

    private Long sqbRefundMoney;

    private String dimension;

    private Date createAt;

    private String sqbMerchantSn;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getTradeMonth() {
        return tradeMonth;
    }

    public void setTradeMonth(String tradeMonth) {
        this.tradeMonth = tradeMonth;
    }

    public Integer getBillType() {
        return billType;
    }

    public void setBillType(Integer billType) {
        this.billType = billType;
    }

    public String getSourceMerchantId() {
        return sourceMerchantId;
    }

    public void setSourceMerchantId(String sourceMerchantId) {
        this.sourceMerchantId = sourceMerchantId;
    }

    public String getSqbMerchantId() {
        return sqbMerchantId;
    }

    public void setSqbMerchantId(String sqbMerchantId) {
        this.sqbMerchantId = sqbMerchantId;
    }

    public Integer getSqbTradeNum() {
        return sqbTradeNum;
    }

    public void setSqbTradeNum(Integer sqbTradeNum) {
        this.sqbTradeNum = sqbTradeNum;
    }

    public Long getSqbTradeMoney() {
        return sqbTradeMoney;
    }

    public void setSqbTradeMoney(Long sqbTradeMoney) {
        this.sqbTradeMoney = sqbTradeMoney;
    }

    public Integer getSqbRefundNum() {
        return sqbRefundNum;
    }

    public void setSqbRefundNum(Integer sqbRefundNum) {
        this.sqbRefundNum = sqbRefundNum;
    }

    public Long getSqbRefundMoney() {
        return sqbRefundMoney;
    }

    public void setSqbRefundMoney(Long sqbRefundMoney) {
        this.sqbRefundMoney = sqbRefundMoney;
    }

    public String getDimension() {
        return dimension;
    }

    public void setDimension(String dimension) {
        this.dimension = dimension;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public String getSqbMerchantSn() {
        return sqbMerchantSn;
    }

    public void setSqbMerchantSn(String sqbMerchantSn) {
        this.sqbMerchantSn = sqbMerchantSn;
    }

}