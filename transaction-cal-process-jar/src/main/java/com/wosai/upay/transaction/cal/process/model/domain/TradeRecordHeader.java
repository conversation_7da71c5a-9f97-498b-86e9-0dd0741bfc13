package com.wosai.upay.transaction.cal.process.model.domain;

import lombok.Data;

/**
 * @version 1.0
 * @author: yuhai
 * @program: transaction-cal-process
 * @className TradeRecordHeader
 * @description:
 * @create: 2025-05-29 09:28
 **/
@Data
public class TradeRecordHeader {


    /**
     * 交易日期
     */
    private Integer tradeDate;
    /**
     * 时间
     */
    private Integer tradeTime;
    /**
     * 商户流水号
     */
    private Integer merchantTradeSn;
    /**
     * 收款通道/支付方式
     */
    private Integer paymentChannel;
    /**
     * 商品名
     */
    private Integer productName;
    /**
     * 商户内部订单号
     */
    private Integer merchantInternalOrderNo;
    /**
     * 商户订单号
     */
    private Integer merchantOrderNo;
    /**
     * 收款通道订单号
     */
    private Integer paymentChannelOrderNo;
    /**
     * 交易类型/交易模式
     */
    private Integer tradeType;
    /**
     * 交易状态
     */
    private Integer tradeStatus;
    /**
     * 付款账户
     */
    private Integer payerAccount;
    /**
     * 币种/货币类型
     */
    private Integer currencyType;
    /**
     * 收款金额
     */
    private Integer collectionAmount;
    /**
     * 收钱吧商户优惠
     */
    private Integer shouqianbaMerchantDiscount;
    /**
     * 收钱吧补贴优惠
     */
    private Integer shouqianbaSubsidyDiscount;
    /**
     * 收款通道补贴优惠
     */
    private Integer paymentChannelSubsidyDiscount;
    /**
     * 收款通道商户预充值优惠
     */
    private Integer paymentChannelPrepaidDiscount;
    /**
     * 收款通道商户免充值优惠
     */
    private Integer paymentChannelFreeDiscount;
    /**
     * 消费者实付金额
     */
    private Integer consumerActualPayment;
    /**
     * 费率%
     */
    private Integer feeRate;
    /**
     * 支付手续费
     */
    private Integer paymentHandlingFee;
    /**
     * 实收金额
     */
    private Integer actualCollectionAmount;
    /**
     * 技术服务费
     */
    private Integer technicalServiceFee;
    /**
     * 结算金额
     */
    private Integer settlementAmount;
    /**
     * 商户号
     */
    private Integer merchantSn;
    /**
     * 商户名称
     */
    private Integer merchantName;
    /**
     * 门店号
     */
    private Integer storeSn;
    /**
     * 商户门店号
     */
    private Integer merchantStoreSn;
    /**
     * 门店名称
     */
    private Integer storeName;
    /**
     * 终端号
     */
    private Integer terminalSn;
    /**
     * 商户终端号
     */
    private Integer merchantTerminalSn;
    /**
     * 终端名称
     */
    private Integer terminalName;
    /**
     * 终端类型
     */
    private Integer terminalType;
    /**
     * 设备号
     */
    private Integer deviceId;
    /**
     * 操作员
     */
    private Integer operator;
    /**
     * 收银员
     */
    private Integer cashier;
    /**
     * 备注
     */
    private Integer remark;
    /**
     * 分账标识
     */
    private Integer profitSharingFlag;
    /**
     * 分账金额
     */
    private Integer profitSharingAmount;
    /**
     * 收钱吧商户优惠类型
     */
    private Integer shouqianbaMerchantDiscountType;
    /**
     * 交易模式
     */
    private Integer tradingMode;
    /**
     * 智慧点单服务费
     */
    private Integer intelligentOrderServiceFee;
    /**
     * 流量服务费
     */
    private Integer trafficServiceFee;
    /**
     * 校园配送服务费
     */
    private Integer campusDeliveryServiceFee;
    /**
     * 第三方配送服务费
     */
    private Integer thirdPartyDeliveryServiceFee;
    /**
     * 券包出售服务费
     */
    private Integer couponPackageSalesServiceFee;
    /**
     * 权益卡出售服务费
     */
    private Integer rightsCardSalesServiceFee;
    /**
     * 储值充值服务费
     */
    private Integer storedValueRechargeServiceFee;
    /**
     * 花呗分期免息营销服务费
     */
    private Integer alipayInstallmentFreeInterestFee;
    /**
     * 先享后付服务费
     */
    private Integer payLaterServiceFee;
    /**
     * 流量服务费固定佣金
     */
    private Integer trafficServiceFixedCommission;
    /**
     * 流量服务费起步价
     */
    private Integer trafficServiceStartingPrice;
    /**
     * 零售外卖服务费
     */
    private Integer retailTakeoutServiceFee;
    /**
     * 线上门店技术服务费
     */
    private Integer onlineStoreTechnicalServiceFee;
    /**
     * 打包服务费
     */
    private Integer packagingServiceFee;
    /**
     * 配送管理助手费
     */
    private Integer deliveryManagementAssistantFee;
    /**
     * 应用方
     */
    private Integer applicationParty;
    /**
     * 交易流水号
     */
    private Integer tradeSn;
    /**
     * 银联终端号
     */
    private Integer termId;
    /**
     * 收单机构商户号
     */
    private Integer providerMchId;
    /**
     * 交易场景
     */
    private Integer tradeScene;
}
