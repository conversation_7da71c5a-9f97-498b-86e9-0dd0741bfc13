package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBill;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillExample;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillV2;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillV2Example;

import java.util.Date;
import java.util.List;

import com.wosai.upay.transaction.cal.process.processor.SourceBill;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Update;

public interface TbSourceBillV2Mapper {
    long countByExample(TbSourceBillV2Example example);

    int deleteByExample(TbSourceBillV2Example example);

    int deleteByPrimaryKey(Long id);

    int insert(TbSourceBillV2 record);

    int insertSelective(TbSourceBillV2 record);

    int batchInsert(@Param("sourceBillInputTaskId") Integer sourceBillInputTaskId,
                    @Param("tradeDateMonth") Date tradeDateMonth,
                    @Param("sourceBillType") Integer sourceBillType,
                    @Param("list") List<SourceBill.Detail> detailList);

    List<TbSourceBillV2> selectByExample(TbSourceBillV2Example example);

    TbSourceBillV2 selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TbSourceBillV2 record, @Param("example") TbSourceBillV2Example example);

    int updateByExample(@Param("record") TbSourceBillV2 record, @Param("example") TbSourceBillV2Example example);

    int updateByPrimaryKeySelective(TbSourceBillV2 record);

    int updateByPrimaryKey(TbSourceBillV2 record);

    @Update("update tb_source_bill_type_v2 set config = #{config} where id = #{type}")
    int updateConfig(@Param("type") int type, @Param("config") byte[] config);
}