package com.wosai.upay.transaction.cal.process.mapper;


import com.wosai.upay.transaction.cal.process.model.domain.StatementPlan;
import com.wosai.upay.transaction.cal.process.model.dto.PlanListParam;

import java.util.List;

public interface StatementPlanMapper {
    int insertSelective(StatementPlan statementPlan);

    int updateByPrimaryKeySelective(StatementPlan statementPlan);

    StatementPlan selectByPrimaryKey(Long id);

    List<StatementPlan> listPlan(PlanListParam listParam);
}