package com.wosai.upay.transaction.cal.process.config;

import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImplExporter;
import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.itsys.cornucopia.admin.service.*;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.upay.core.service.*;
import com.wosai.upay.job.service.MerchantProviderParamsService;
import com.wosai.upay.signature.service.SignService;
import com.wosai.upay.transaction.cal.process.exception.CustomErrorResolver;
import com.wosai.upay.transaction.cal.process.util.RpcConfigUtil;
import com.wosai.upay.task.center.service.ExportService;
import com.wosai.upay.task.center.service.TaskLogService;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.user.api.service.GroupService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;


@Configuration
public class JsonRpcConfig {



    @Value("${jsonrpc.core-business-server}")
    private String coreBusinessServer;

    @Value("${jsonrpc.upay-task-center}")
    private String upayTaskCenterUrl;

    @Value("${jsonrpc.user-service}")
    private String userServiceUrl;
    @Value("${jsonrpc.merchant-user-service}")
    private String merchantUserServiceUrl;
    @Value("${jsonrpc.crm}")
    private String crmUrl;
    @Value("${jsonrpc.signature-proxy-server}")
    private String signatureProxyServer;
    @Value("${jsonrpc.merchant-contract-job}")
    private String merchantContractJob;
    @Value("${jsonrpc.cornucopia-admin}")
    private String cornucopiaAdminUrl;


    @Value("${jsonrpc.aop-gateway-service}")
    private String aopGatewayServiceUrl;


    @Value("${jsonrpc.business-logstash}")
    private String businessLogstashServiceUrl;

    @Value("${jsonrpc.upay-transaction}")
    private String upayTransactionServiceUrl;



    @Bean
    public static AutoJsonRpcServiceImplExporter rpcServiceImplExporter() {
        AutoJsonRpcServiceImplExporter exporter = new AutoJsonRpcServiceImplExporter();
        exporter.setErrorResolver(new CustomErrorResolver());
        return exporter;
    }

    @Bean
    public JsonProxyFactoryBean merchantService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessServer + "/rpc/merchant", MerchantService.class);
    }

    @Bean
    public JsonProxyFactoryBean logService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessServer + "/rpc/log", LogService.class);
    }

    @Bean
    public JsonProxyFactoryBean storeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessServer + "/rpc/store", StoreService.class);
    }

    @Bean
    public JsonProxyFactoryBean supportService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessServer + "/rpc/support", SupportService.class);
    }

    @Bean
    public JsonProxyFactoryBean tradeConfigService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(coreBusinessServer + "/rpc/tradeConfig", TradeConfigService.class);
    }

    @Bean
    public JsonProxyFactoryBean exportService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayTaskCenterUrl + "/rpc/export", ExportService.class);
    }

    @Bean
    public JsonProxyFactoryBean taskLogService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayTaskCenterUrl + "/rpc/task", TaskLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean groupService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(userServiceUrl + "/rpc/group", GroupService.class);
    }

    /**
     * merchant-user-service
     * @return
     */
    @Bean
    public JsonProxyFactoryBean merchantUserGroupService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/group", com.wosai.app.service.GroupService.class);
    }

    @Bean
    public JsonProxyFactoryBean organizationService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(crmUrl + "/rpc/organization", OrganizationService.class);
    }

    @Bean
    public JsonProxyFactoryBean signService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(signatureProxyServer + "/rpc/sign", SignService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantProviderParamsService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantContractJob + "/rpc/merchantProviderParams", MerchantProviderParamsService.class);
    }

    @Bean
    public JsonProxyFactoryBean clientSideNoticeService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(aopGatewayServiceUrl + "/rpc/clientSide/notice", ClientSideNoticeService.class);
    }

    @Bean
    public JsonProxyFactoryBean businessOpLogService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(businessLogstashServiceUrl + "/rpc/businessOpLog", BusinessOpLogService.class);
    }

    @Bean
    public JsonProxyFactoryBean merchantUserServiceV2() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(merchantUserServiceUrl + "/rpc/merchantuserV2", MerchantUserServiceV2.class);
    }

    @Bean
    public JsonProxyFactoryBean iPolicyService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/policy", IPolicyService.class);
    }

    @Bean
    public JsonProxyFactoryBean PropertyRpcService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/property", PropertyRpcService.class);
    }

    @Bean
    public JsonProxyFactoryBean iImportTemplateRuleService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/importTemplateRule", IImportTemplateRuleRpcService.class);
    }

    @Bean
    public JsonProxyFactoryBean importTaskService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/importTask", IImportTaskService.class);
    }

    @Bean
    public JsonProxyFactoryBean iCashAndOrderSyncService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/sync", ICashAndOrderSyncService.class);
    }

    @Bean
    public JsonProxyFactoryBean iFulfillmentSettlementOrderService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/fulfillmentSettlementOrder", IFulfillmentSettlementOrderService.class);
    }

    @Bean
    public JsonProxyFactoryBean iArOrderService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(cornucopiaAdminUrl + "/rpc/arOrder", IArOrderService.class);
    }

    @Bean
    public JsonProxyFactoryBean orderService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayTransactionServiceUrl + "/rpc/order", OrderService.class);
    }

    @Bean
    public JsonProxyFactoryBean transactionService() {
        return RpcConfigUtil.getJsonProxyFactoryWareTracingBean(upayTransactionServiceUrl + "/rpc/transaction", com.wosai.upay.transaction.service.TransactionService.class);
    }
}

