package com.wosai.upay.transaction.cal.process.service;

import com.aliyun.oss.model.GetBucketPolicyStatusResult;
import cn.hutool.core.util.ObjectUtil;
import com.github.pagehelper.Page;
import com.wosai.itsys.cornucopia.admin.enums.CommonStatusEnum;
import com.wosai.upay.transaction.cal.process.util.RedisUtils;
import com.github.pagehelper.PageHelper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.itsys.cornucopia.admin.domain.request.StartSettlementRequest;
import com.wosai.itsys.cornucopia.admin.domain.request.SyncOrderRequest;
import com.wosai.itsys.cornucopia.admin.domain.response.PolicyResponse;
import com.wosai.itsys.cornucopia.admin.domain.response.PropertyResponse;
import com.wosai.itsys.cornucopia.admin.service.*;
import com.wosai.upay.transaction.cal.process.converter.SummaryBillConverter;
import com.wosai.upay.transaction.cal.process.mapper.TbBillOutputV2Mapper;
import com.wosai.upay.transaction.cal.process.mapper.TbSourceBillInputTaskMapper;
import com.wosai.upay.transaction.cal.process.mapper.TbSourceBillTypeMapper;
import com.wosai.upay.transaction.cal.process.mapper.TbSummaryBillMapper;
import com.wosai.upay.transaction.cal.process.model.domain.*;
import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import com.wosai.upay.transaction.cal.process.model.request.SummaryBillPageRequest;
import com.wosai.upay.transaction.cal.process.model.response.BasePageResponse;
import com.wosai.upay.transaction.cal.process.model.response.SummaryBillResponse;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import com.wosai.upay.transaction.cal.process.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class SummaryBillServiceImpl implements SummaryBillService {
    @Resource
    private TbSummaryBillMapper tbSummaryBillMapper;

    @Resource
    private IPolicyService iPolicyService;

    @Resource
    private IImportTaskService iImportTaskService;

    @Resource
    private ICashAndOrderSyncService iCashAndOrderSyncService;

    @Resource
    private TbBillOutputV2Mapper tbBillOutputV2Mapper;

    @Resource
    private IArOrderService iArOrderService;

    @Resource
    private IFulfillmentSettlementOrderService iFulfillmentSettlementOrderService;

    @Resource
    private TbSourceBillInputTaskMapper tbSourceBillInputTaskMapper;

    @Resource
    private TbSourceBillTypeMapper billTypeMapper;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private PropertyRpcService propertyRpcService;

    private static final AtomicInteger SUMMARY_BILL_TASK_EXECUTOR_THREAD_NUMBER = new AtomicInteger(1);
    private static final ExecutorService SUMMARY_BILL_TASK_EXECUTOR = new ThreadPoolExecutor(2, 10, 60, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100), r -> new Thread(r, "source-bill-task-" + SUMMARY_BILL_TASK_EXECUTOR_THREAD_NUMBER.getAndIncrement()));

    @Override
    public BasePageResponse<SummaryBillResponse> pageSummaryBill(SummaryBillPageRequest request) {
        SummaryBillPageRequest.SummaryBillPageQuery query = request.getQuery();
        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
        TbSummaryBillExample.Criteria criteria = tbSummaryBillExample.createCriteria();
        if (StringUtils.isNotEmpty(query.getPolicyId())) {
            criteria.andPolicyIdEqualTo(query.getPolicyId());
        }
        if (StringUtils.isNotEmpty(query.getTradeStartMonth()) && StringUtils.isNotEmpty(query.getTradeEndMonth())) {
            criteria.andTradeMonthBetween(DateTimeUtil.convertToDate(query.getTradeStartMonth()), DateTimeUtil.convertToDate(query.getTradeEndMonth()));
        }
        if (StringUtils.isNotEmpty(query.getEntryStartMonth()) && StringUtils.isNotEmpty(query.getEntryEndMonth())) {
            criteria.andEntryMonthBetween(DateTimeUtil.convertToDate(query.getEntryStartMonth()), DateTimeUtil.convertToDate(query.getEntryEndMonth()));
        }
        if (query.getStatus() != null) {
            criteria.andStatusEqualTo(query.getStatus().name());
        }
        if (query.getPushFulfillmentStatus() != null) {
            criteria.andPushStatusEqualTo(query.getPushFulfillmentStatus().name());
        }
        if (StringUtils.isNotEmpty(query.getServerChannelCode())) {
            criteria.andServerChannelCodeEqualTo(query.getServerChannelCode());
        }
        if (StringUtils.isNotEmpty(query.getSummaryBillSn())) {
            criteria.andSnEqualTo(query.getSummaryBillSn());
        }
        if (StringUtils.isNotEmpty(query.getName())) {
            criteria.andNameEqualTo(query.getName());
        }
        if (StringUtils.isNotEmpty(query.getPropertyId())) {
            criteria.andPropertyIdEqualTo(query.getPropertyId());
        }
        if (StringUtils.isNotEmpty(query.getNcName())) {
            criteria.andNcNameEqualTo(query.getNcName());
        }
        if (StringUtils.isNotEmpty(query.getCompanyName())) {
            criteria.andCompanyNameEqualTo(query.getCompanyName());
        }
        PageHelper.startPage(request.getPageNo(), request.getPageSize(), "id DESC");
        Page<TbSummaryBill> tbSummaryBills = (Page<TbSummaryBill>) tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
        if (CollectionUtils.isEmpty(tbSummaryBills)) {
            return new BasePageResponse();
        }

        List<PolicyResponse> policyResponseList = iPolicyService.queryPolicyByIdList(tbSummaryBills.stream()
                .map(TbSummaryBill::getPolicyId)
                .filter(Objects::nonNull)
                .map(Long::valueOf)
                .collect(Collectors.toList()));
        List<PropertyResponse> propertyResponseList = propertyRpcService.queryByIdList(tbSummaryBills.stream()
                .map(TbSummaryBill::getPropertyId)
                .filter(Objects::nonNull)
                .map(Long::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isEmpty(propertyResponseList) && CollectionUtils.isNotEmpty(policyResponseList)) {
            propertyResponseList = propertyRpcService.queryByIdList(policyResponseList.stream()
                    .map(PolicyResponse::getPropertyId)
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList()));
        }

        List<SummaryBillResponse> summaryBillResponses = SummaryBillConverter.INSTANCE.doListToVoList(tbSummaryBills.getResult());
        for (SummaryBillResponse summaryBillResponse : summaryBillResponses) {
            summaryBillResponse.setLineNumber(summaryBillResponse.getRemark().getTaskList().stream().mapToInt(task -> Integer.parseInt(task.getNumber())).sum());
            PolicyResponse policyResponse = policyResponseList.stream().filter(policy -> policy.getId().toString().equals(summaryBillResponse.getPolicyId())).findFirst().orElse(null);
            if (policyResponse != null) {
                summaryBillResponse.setPolicyName(Optional.ofNullable(policyResponse).map(PolicyResponse::getName).orElse(null));
                summaryBillResponse.setNcName(Optional.ofNullable(policyResponse).map(PolicyResponse::getNcName).orElse(null));
                summaryBillResponse.setCompanyName(Optional.ofNullable(policyResponse).map(PolicyResponse::getCompanyName).orElse(null));
                summaryBillResponse.setPropertyId(policyResponse.getPropertyId().toString());
            }
            PropertyResponse propertyResponse = propertyResponseList.stream().filter(property -> property.getId().toString().equals(summaryBillResponse.getPropertyId())).findFirst().orElse(null);
            if (propertyResponse != null) {
                summaryBillResponse.setPropertyId(propertyResponse.getId().toString());
                summaryBillResponse.setPropertyName(propertyResponse.getName());
            }
        }

        return new BasePageResponse(summaryBillResponses, tbSummaryBills.getTotal(), request.getPageNo(), request.getPageSize());
    }

    @Override
    public SummaryBillResponse querySummaryBillDetail(String sn) {
        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
        tbSummaryBillExample.createCriteria().andSnEqualTo(sn);
        List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
        if (tbSummaryBills.size() == 0) {
            return null;
        }
        SummaryBillResponse summaryBillResponse = SummaryBillConverter.INSTANCE.doToVo(tbSummaryBills.get(0));
        TbSummaryBill summaryBill = tbSummaryBills.get(0);
        String policyId = summaryBill.getPolicyId();
        PolicyResponse policyResponse = null;
        if (policyId != null) {
            policyResponse = iPolicyService.queryPolicyById(Long.valueOf(policyId));
            if (policyResponse != null) {
                summaryBillResponse.setNcName(policyResponse.getNcName());
                summaryBillResponse.setCompanyName(policyResponse.getCompanyName());
                summaryBillResponse.setPolicyName(policyResponse.getName());
                summaryBillResponse.setPropertyId(policyResponse.getPropertyId().toString());
            }
        }
        PropertyResponse propertyResponse = null;
        if (StringUtils.isNotBlank(summaryBill.getPropertyId())) {
            propertyResponse = propertyRpcService.getById(Long.valueOf(summaryBill.getPropertyId()));
        }
        if (propertyResponse == null && policyResponse != null) {
            propertyResponse = propertyRpcService.getById(policyResponse.getPropertyId());
        }

        // Get property details
        if (tbSummaryBills.get(0).getPropertyId() != null) {
            if (propertyResponse != null) {
                summaryBillResponse.setPropertyId(propertyResponse.getId().toString());
                summaryBillResponse.setPropertyName(propertyResponse.getName());
            }
        }
        summaryBillResponse.setLineNumber(summaryBillResponse.getRemark().getTaskList().stream().mapToInt(task -> Optional.ofNullable(task.getNumber()).map(Integer::valueOf).orElse(0)).sum());
        summaryBillResponse.setRelatedImportTaskList(SummaryBillConverter.INSTANCE.convertRelatedImportTaskList(summaryBillResponse.getRemark().getTaskList()));
        return summaryBillResponse;
    }

    @Override
    public void confirmSummaryBill(String sn) {
        if (StringUtils.isEmpty(sn)) {
            throw new IllegalArgumentException("sn不能为空");
        }
        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
        tbSummaryBillExample.createCriteria().andSnEqualTo(sn);
        List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
        if (CollectionUtils.isEmpty(tbSummaryBills)) {
            throw new IllegalArgumentException("账单不存在");
        }
        TbSummaryBill tbSummaryBill = tbSummaryBills.get(0);
        if (StringUtils.isNotEmpty(tbSummaryBill.getPolicyId())) {
            PolicyResponse policyResponse = iPolicyService.queryPolicyById(Long.valueOf(tbSummaryBill.getPolicyId()));
            if (policyResponse == null || !Objects.equals(policyResponse.getStatus(), CommonStatusEnum.ENABLE.getCode())) {
                throw new IllegalArgumentException("请确保账单政策已启用");
            }
        }

        tbSummaryBill.setStatus(SummaryBillStatus.CONFIRMED);
        tbSummaryBillMapper.updateByExampleSelective(tbSummaryBill, tbSummaryBillExample);
    }

    @Override
    public void pushSummaryBIll(List<String> summaryBillSnList) {
        if (CollectionUtils.isEmpty(summaryBillSnList)) {
            return;
        }
        if (summaryBillSnList.size() >= 10) {
            throw new IllegalArgumentException("一次推送最多10个账单");
        }
        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
        tbSummaryBillExample.createCriteria().andSnIn(summaryBillSnList);
        List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
        List<Long> policyIdList = tbSummaryBills.stream()
                .map(TbSummaryBill::getPolicyId)
                .filter(StringUtils::isNotBlank)
                .map(Long::valueOf)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(policyIdList)) {
            List<PolicyResponse> policyResponseList = iPolicyService.queryPolicyByIdList(policyIdList);
            if (policyResponseList.stream().anyMatch(policyResponse -> !Objects.equals(policyResponse.getStatus(), CommonStatusEnum.ENABLE.getCode()))) {
                throw new IllegalArgumentException("请确保账单政策已启用");
            }
        }
        tbSummaryBills.forEach(tbSummaryBill -> {
            TbSummaryBillExample singleQuery = new TbSummaryBillExample();
            singleQuery.createCriteria().andIdEqualTo(tbSummaryBill.getId());
            if (!tbSummaryBill.isConfirmed()) {
                throw new IllegalArgumentException("账单未确认");
            }
            if (tbSummaryBill.getPushStatus() != FulfillmentPushStatus.INIT) {
                throw new IllegalArgumentException("账单当前推送状态为：" + tbSummaryBill.getPushStatus().name());
            }
            tbSummaryBill.setPushStatus(FulfillmentPushStatus.ON_GOING);
            tbSummaryBillMapper.updateByExampleSelective(tbSummaryBill, singleQuery);
        });
        SUMMARY_BILL_TASK_EXECUTOR.execute(() -> {
            for (TbSummaryBill tbSummaryBill : tbSummaryBills) {
                try {
                    TbBillOutputV2Example tbBillOutputExample = new TbBillOutputV2Example();
                    TbBillOutputV2Example.Criteria criteria = tbBillOutputExample.createCriteria().andSummaryBillSnEqualTo(tbSummaryBill.getSn());
                    criteria.andPushStatusEqualTo(FulfillmentPushStatus.INIT.getCode());

                    int pageSize = 500;
                    int maxRunTime = 5000;
                    int runTime = 0;

                    List<TbBillOutputV2> tbBillOutputs = new ArrayList<>();
                    do {
                        PageHelper.startPage(1, pageSize);
                        tbBillOutputs = tbBillOutputV2Mapper.selectByExample(tbBillOutputExample);

                        if (CollectionUtils.isNotEmpty(tbBillOutputs)) {
                            iCashAndOrderSyncService.syncCreateOrderList(convertSyncOrderRequest(tbBillOutputs, tbSummaryBill));
                            tbBillOutputs.forEach(output -> {
                                output.setPushStatus(FulfillmentPushStatus.SUCCESS);
                            });
                            tbBillOutputV2Mapper.insertOrUpdateBatch(tbBillOutputs);
                        }

                        if (runTime++ > maxRunTime) {
                            log.error("超过最大运行次数，终止推送账单");
                            break;
                        }
                    } while (CollectionUtils.isNotEmpty(tbBillOutputs));
                    tbSummaryBill.setPushStatus(FulfillmentPushStatus.SUCCESS);
                    if (ObjectUtil.isNull(tbSummaryBill.getEntryMonth())) {
                        tbSummaryBill.setEntryMonth(new Date());
                    }
                } catch (Exception e) {
                    tbSummaryBill.setPushStatus(FulfillmentPushStatus.FAILED);
                    log.error("账单推送失败", e);
                } finally {
                    TbSummaryBillExample singleQuery = new TbSummaryBillExample();
                    singleQuery.createCriteria().andIdEqualTo(tbSummaryBill.getId());
                    tbSummaryBillMapper.updateByExampleSelective(tbSummaryBill, singleQuery);
                }
                if (tbSummaryBill.getPushStatus() == FulfillmentPushStatus.SUCCESS
                        && (StringUtils.isNotEmpty(tbSummaryBill.getPropertyId()) || StringUtils.isNotEmpty(tbSummaryBill.getPolicyId()))
                ) {
                    StartSettlementRequest startSettlementRequest = new StartSettlementRequest();
                    if (tbSummaryBill.getPolicyId() != null) {
                        startSettlementRequest.setPolicyId(Long.valueOf(tbSummaryBill.getPolicyId()));
                    } else if (tbSummaryBill.getPropertyId() != null){
                        startSettlementRequest.setPropertyId(Long.valueOf(tbSummaryBill.getPropertyId()));
                    }
                    try {
                        iFulfillmentSettlementOrderService.settlement(startSettlementRequest);
                        iArOrderService.settlement(startSettlementRequest);
                    } catch (Exception e) {
                        log.error("推单结算履约单异常", e);
                    }
                }
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void removeSourceBillByImportTaskSn(String importTaskSn) {
        String lockKey = "summary_bill:remove:" + importTaskSn;
        String requestId = UUID.randomUUID().toString();
        try {
            boolean locked = redisUtils.setNX(lockKey, requestId, 30);
            if (!locked) {
                log.warn("Failed to acquire distributed lock for key: {}", lockKey);
                throw new RuntimeException("操作太频繁，请稍后重试");
            }
            TbBillOutputV2Example tbBillOutputExample = new TbBillOutputV2Example();
            tbBillOutputExample.createCriteria().andImportTaskSnEqualTo(importTaskSn);

            int pageSize = 1000;
            int maxRunTime = 1000;
            int runTime = 0;
            List<TbBillOutputV2> tbBillOutputs = new ArrayList<>();
            TbSummaryBill summaryBill = null;
            do {
                if (maxRunTime <= ++runTime) {
                    log.error("超过最大运行次数，终止删除账单");
                    break;
                }

                PageHelper.startPage(1, pageSize);
                tbBillOutputs = tbBillOutputV2Mapper.selectByExample(tbBillOutputExample);
                if (CollectionUtils.isNotEmpty(tbBillOutputs)) {
                    TbBillOutputV2Example deleteExample = new TbBillOutputV2Example();
                    deleteExample.createCriteria().andIdIn(tbBillOutputs.stream().map(TbBillOutputV2::getId).collect(Collectors.toList()));
                    tbBillOutputV2Mapper.deleteByExample(deleteExample);
                    if (summaryBill == null) {
                        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
                        tbSummaryBillExample.createCriteria().andSnEqualTo(tbBillOutputs.get(0).getSummaryBillSn());
                        List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
                        if (CollectionUtils.isEmpty(tbSummaryBills)) {
                            throw new CommonInvalidParameterException("账单不存在");
                        }
                        summaryBill = tbSummaryBills.get(0);
                    }
                    // 更新汇总账单金额
                    tbBillOutputs.forEach(summaryBill::removeBillOutput);
                }
            } while (CollectionUtils.isNotEmpty(tbBillOutputs));
            if (summaryBill == null) {
                throw new CommonInvalidParameterException("账单不存在");
            }
            summaryBill.cancelImportTask(importTaskSn);
            tbSummaryBillMapper.insertSelectiveOrUpdate(summaryBill);
        } finally {
            redisUtils.deleteIfEqual(lockKey, requestId);
            log.info("Released distributed lock. key: {}, requestId: {}", lockKey, requestId);
        }
    }

    @Override
    public void initBillType(Map<String, String> billTypeMap) {
        billTypeMap.forEach((name, policyId) -> {
            TbSourceBillTypeExample tbSourceBillTypeExample = new TbSourceBillTypeExample();
            tbSourceBillTypeExample.createCriteria().andNameEqualTo(name);
            List<TbSourceBillType> tbSourceBillTypes = billTypeMapper.selectByExample(tbSourceBillTypeExample);
            if (CollectionUtils.isNotEmpty(tbSourceBillTypes)) {
                tbSourceBillTypes.get(0).setPolicyId(policyId);
                billTypeMapper.updateByExample(tbSourceBillTypes.get(0), tbSourceBillTypeExample);
            }
        });
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteTestBillData(String summaryBillSn) {
        if (StringUtils.isEmpty(summaryBillSn)) {
            throw new IllegalArgumentException("汇总账单编号不能为空");
        }

        // 1. 尝试查询汇总账单（如果存在则记录，不存在则继续删除明细）
        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
        tbSummaryBillExample.createCriteria().andSnEqualTo(summaryBillSn);
        List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
        TbSummaryBill summaryBill = null;
        if (CollectionUtils.isNotEmpty(tbSummaryBills)) {
            summaryBill = tbSummaryBills.get(0);
        }

        // 2. 删除TbBillOutputV2数据（分批删除，每批1000条）
        TbBillOutputV2Example tbBillOutputExample = new TbBillOutputV2Example();
        tbBillOutputExample.createCriteria().andSummaryBillSnEqualTo(summaryBillSn);
        int pageSize = 1000;
        int maxRunTime = 1000; // 最大运行次数，防止无限循环
        int runTime = 0;
        List<TbBillOutputV2> tbBillOutputs;
        do {
            PageHelper.startPage(1, pageSize);
            tbBillOutputs = tbBillOutputV2Mapper.selectByExample(tbBillOutputExample);
            if (CollectionUtils.isNotEmpty(tbBillOutputs)) {
                TbBillOutputV2Example deleteExample = new TbBillOutputV2Example();
                deleteExample.createCriteria().andIdIn(tbBillOutputs.stream().map(TbBillOutputV2::getId).collect(Collectors.toList()));
                tbBillOutputV2Mapper.deleteByExample(deleteExample);
            }
            runTime++;
        } while (CollectionUtils.isNotEmpty(tbBillOutputs) && runTime < maxRunTime);

        // 3. 如果汇总账单存在，则删除汇总账单
        if (summaryBill != null) {
            tbSummaryBillMapper.deleteByExample(tbSummaryBillExample);
        }
    }

    private List<SyncOrderRequest> convertSyncOrderRequest(List<TbBillOutputV2> tbBillOutputs, TbSummaryBill tbSummaryBill) {
        return tbBillOutputs.stream().map(output -> {
            SyncOrderRequest syncOrderRequest = new SyncOrderRequest();
            syncOrderRequest.setAppId("transaction-cal-process");

            syncOrderRequest.setAmount(output.getSourceSettlementAmount());
            syncOrderRequest.setOrderNo(output.getSummaryBillSn() + "_" + output.getId());
            if (tbSummaryBill.getPolicyId() != null) {
                syncOrderRequest.setPolicyId(Long.valueOf(tbSummaryBill.getPolicyId()));
            }
            if (tbSummaryBill.getPropertyId() != null) {
                syncOrderRequest.setBusinessType(Long.valueOf(tbSummaryBill.getPropertyId()));
            }
            syncOrderRequest.setCompanyName(tbSummaryBill.getCompanyName());
            syncOrderRequest.setCusName(tbSummaryBill.getNcName());
            syncOrderRequest.setLoanType("0");
            syncOrderRequest.setOrderTime(DateTimeUtil.convertToBasicString(output.getTradeDateMonth()));
            syncOrderRequest.setPaymentFlag("0");
            syncOrderRequest.setPerformanceFlag("1");
            syncOrderRequest.setPerformanceTime(DateTimeUtil.convertToBasicString(output.getTradeDateMonth()));
            //添加original_code/original_name
            syncOrderRequest.setOriginalCode(output.getSqbMerchantSn());
            //TODO 添加original_name 暂时没值
            return syncOrderRequest;
        }).collect(Collectors.toList());
    }
}
