package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class TbSourceBillTypeExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TbSourceBillTypeExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyIsNull() {
            addCriterion("source_bill_company is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyIsNotNull() {
            addCriterion("source_bill_company is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyEqualTo(Byte value) {
            addCriterion("source_bill_company =", value, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNotEqualTo(Byte value) {
            addCriterion("source_bill_company <>", value, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyGreaterThan(Byte value) {
            addCriterion("source_bill_company >", value, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyGreaterThanOrEqualTo(Byte value) {
            addCriterion("source_bill_company >=", value, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyLessThan(Byte value) {
            addCriterion("source_bill_company <", value, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyLessThanOrEqualTo(Byte value) {
            addCriterion("source_bill_company <=", value, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyIn(List<Byte> values) {
            addCriterion("source_bill_company in", values, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNotIn(List<Byte> values) {
            addCriterion("source_bill_company not in", values, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_company between", value1, value2, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNotBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_company not between", value1, value2, "sourceBillCompany");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameIsNull() {
            addCriterion("source_bill_company_name is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameIsNotNull() {
            addCriterion("source_bill_company_name is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameEqualTo(String value) {
            addCriterion("source_bill_company_name =", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameNotEqualTo(String value) {
            addCriterion("source_bill_company_name <>", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameGreaterThan(String value) {
            addCriterion("source_bill_company_name >", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameGreaterThanOrEqualTo(String value) {
            addCriterion("source_bill_company_name >=", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameLessThan(String value) {
            addCriterion("source_bill_company_name <", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameLessThanOrEqualTo(String value) {
            addCriterion("source_bill_company_name <=", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameLike(String value) {
            addCriterion("source_bill_company_name like", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameNotLike(String value) {
            addCriterion("source_bill_company_name not like", value, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameIn(List<String> values) {
            addCriterion("source_bill_company_name in", values, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameNotIn(List<String> values) {
            addCriterion("source_bill_company_name not in", values, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameBetween(String value1, String value2) {
            addCriterion("source_bill_company_name between", value1, value2, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCompanyNameNotBetween(String value1, String value2) {
            addCriterion("source_bill_company_name not between", value1, value2, "sourceBillCompanyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyIsNull() {
            addCriterion("source_bill_classify is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyIsNotNull() {
            addCriterion("source_bill_classify is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyEqualTo(Byte value) {
            addCriterion("source_bill_classify =", value, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNotEqualTo(Byte value) {
            addCriterion("source_bill_classify <>", value, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyGreaterThan(Byte value) {
            addCriterion("source_bill_classify >", value, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyGreaterThanOrEqualTo(Byte value) {
            addCriterion("source_bill_classify >=", value, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyLessThan(Byte value) {
            addCriterion("source_bill_classify <", value, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyLessThanOrEqualTo(Byte value) {
            addCriterion("source_bill_classify <=", value, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyIn(List<Byte> values) {
            addCriterion("source_bill_classify in", values, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNotIn(List<Byte> values) {
            addCriterion("source_bill_classify not in", values, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_classify between", value1, value2, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNotBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_classify not between", value1, value2, "sourceBillClassify");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameIsNull() {
            addCriterion("source_bill_classify_name is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameIsNotNull() {
            addCriterion("source_bill_classify_name is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameEqualTo(String value) {
            addCriterion("source_bill_classify_name =", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameNotEqualTo(String value) {
            addCriterion("source_bill_classify_name <>", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameGreaterThan(String value) {
            addCriterion("source_bill_classify_name >", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameGreaterThanOrEqualTo(String value) {
            addCriterion("source_bill_classify_name >=", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameLessThan(String value) {
            addCriterion("source_bill_classify_name <", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameLessThanOrEqualTo(String value) {
            addCriterion("source_bill_classify_name <=", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameLike(String value) {
            addCriterion("source_bill_classify_name like", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameNotLike(String value) {
            addCriterion("source_bill_classify_name not like", value, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameIn(List<String> values) {
            addCriterion("source_bill_classify_name in", values, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameNotIn(List<String> values) {
            addCriterion("source_bill_classify_name not in", values, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameBetween(String value1, String value2) {
            addCriterion("source_bill_classify_name between", value1, value2, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillClassifyNameNotBetween(String value1, String value2) {
            addCriterion("source_bill_classify_name not between", value1, value2, "sourceBillClassifyName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleIsNull() {
            addCriterion("source_bill_cycle is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleIsNotNull() {
            addCriterion("source_bill_cycle is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleEqualTo(Byte value) {
            addCriterion("source_bill_cycle =", value, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNotEqualTo(Byte value) {
            addCriterion("source_bill_cycle <>", value, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleGreaterThan(Byte value) {
            addCriterion("source_bill_cycle >", value, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleGreaterThanOrEqualTo(Byte value) {
            addCriterion("source_bill_cycle >=", value, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleLessThan(Byte value) {
            addCriterion("source_bill_cycle <", value, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleLessThanOrEqualTo(Byte value) {
            addCriterion("source_bill_cycle <=", value, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleIn(List<Byte> values) {
            addCriterion("source_bill_cycle in", values, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNotIn(List<Byte> values) {
            addCriterion("source_bill_cycle not in", values, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_cycle between", value1, value2, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNotBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_cycle not between", value1, value2, "sourceBillCycle");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameIsNull() {
            addCriterion("source_bill_cycle_name is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameIsNotNull() {
            addCriterion("source_bill_cycle_name is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameEqualTo(String value) {
            addCriterion("source_bill_cycle_name =", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameNotEqualTo(String value) {
            addCriterion("source_bill_cycle_name <>", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameGreaterThan(String value) {
            addCriterion("source_bill_cycle_name >", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameGreaterThanOrEqualTo(String value) {
            addCriterion("source_bill_cycle_name >=", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameLessThan(String value) {
            addCriterion("source_bill_cycle_name <", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameLessThanOrEqualTo(String value) {
            addCriterion("source_bill_cycle_name <=", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameLike(String value) {
            addCriterion("source_bill_cycle_name like", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameNotLike(String value) {
            addCriterion("source_bill_cycle_name not like", value, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameIn(List<String> values) {
            addCriterion("source_bill_cycle_name in", values, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameNotIn(List<String> values) {
            addCriterion("source_bill_cycle_name not in", values, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameBetween(String value1, String value2) {
            addCriterion("source_bill_cycle_name between", value1, value2, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillCycleNameNotBetween(String value1, String value2) {
            addCriterion("source_bill_cycle_name not between", value1, value2, "sourceBillCycleName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodIsNull() {
            addCriterion("source_bill_input_method is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodIsNotNull() {
            addCriterion("source_bill_input_method is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodEqualTo(Byte value) {
            addCriterion("source_bill_input_method =", value, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNotEqualTo(Byte value) {
            addCriterion("source_bill_input_method <>", value, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodGreaterThan(Byte value) {
            addCriterion("source_bill_input_method >", value, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodGreaterThanOrEqualTo(Byte value) {
            addCriterion("source_bill_input_method >=", value, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodLessThan(Byte value) {
            addCriterion("source_bill_input_method <", value, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodLessThanOrEqualTo(Byte value) {
            addCriterion("source_bill_input_method <=", value, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodIn(List<Byte> values) {
            addCriterion("source_bill_input_method in", values, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNotIn(List<Byte> values) {
            addCriterion("source_bill_input_method not in", values, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_input_method between", value1, value2, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNotBetween(Byte value1, Byte value2) {
            addCriterion("source_bill_input_method not between", value1, value2, "sourceBillInputMethod");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameIsNull() {
            addCriterion("source_bill_input_method_name is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameIsNotNull() {
            addCriterion("source_bill_input_method_name is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameEqualTo(String value) {
            addCriterion("source_bill_input_method_name =", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameNotEqualTo(String value) {
            addCriterion("source_bill_input_method_name <>", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameGreaterThan(String value) {
            addCriterion("source_bill_input_method_name >", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameGreaterThanOrEqualTo(String value) {
            addCriterion("source_bill_input_method_name >=", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameLessThan(String value) {
            addCriterion("source_bill_input_method_name <", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameLessThanOrEqualTo(String value) {
            addCriterion("source_bill_input_method_name <=", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameLike(String value) {
            addCriterion("source_bill_input_method_name like", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameNotLike(String value) {
            addCriterion("source_bill_input_method_name not like", value, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameIn(List<String> values) {
            addCriterion("source_bill_input_method_name in", values, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameNotIn(List<String> values) {
            addCriterion("source_bill_input_method_name not in", values, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameBetween(String value1, String value2) {
            addCriterion("source_bill_input_method_name between", value1, value2, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputMethodNameNotBetween(String value1, String value2) {
            addCriterion("source_bill_input_method_name not between", value1, value2, "sourceBillInputMethodName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyIsNull() {
            addCriterion("split_strategy is null");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyIsNotNull() {
            addCriterion("split_strategy is not null");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyEqualTo(Byte value) {
            addCriterion("split_strategy =", value, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNotEqualTo(Byte value) {
            addCriterion("split_strategy <>", value, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyGreaterThan(Byte value) {
            addCriterion("split_strategy >", value, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyGreaterThanOrEqualTo(Byte value) {
            addCriterion("split_strategy >=", value, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyLessThan(Byte value) {
            addCriterion("split_strategy <", value, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyLessThanOrEqualTo(Byte value) {
            addCriterion("split_strategy <=", value, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyIn(List<Byte> values) {
            addCriterion("split_strategy in", values, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNotIn(List<Byte> values) {
            addCriterion("split_strategy not in", values, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyBetween(Byte value1, Byte value2) {
            addCriterion("split_strategy between", value1, value2, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNotBetween(Byte value1, Byte value2) {
            addCriterion("split_strategy not between", value1, value2, "splitStrategy");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameIsNull() {
            addCriterion("split_strategy_name is null");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameIsNotNull() {
            addCriterion("split_strategy_name is not null");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameEqualTo(String value) {
            addCriterion("split_strategy_name =", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameNotEqualTo(String value) {
            addCriterion("split_strategy_name <>", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameGreaterThan(String value) {
            addCriterion("split_strategy_name >", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameGreaterThanOrEqualTo(String value) {
            addCriterion("split_strategy_name >=", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameLessThan(String value) {
            addCriterion("split_strategy_name <", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameLessThanOrEqualTo(String value) {
            addCriterion("split_strategy_name <=", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameLike(String value) {
            addCriterion("split_strategy_name like", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameNotLike(String value) {
            addCriterion("split_strategy_name not like", value, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameIn(List<String> values) {
            addCriterion("split_strategy_name in", values, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameNotIn(List<String> values) {
            addCriterion("split_strategy_name not in", values, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameBetween(String value1, String value2) {
            addCriterion("split_strategy_name between", value1, value2, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andSplitStrategyNameNotBetween(String value1, String value2) {
            addCriterion("split_strategy_name not between", value1, value2, "splitStrategyName");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIsNull() {
            addCriterion("policy_id is null");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIsNotNull() {
            addCriterion("policy_id is not null");
            return (Criteria) this;
        }

        public Criteria andPolicyIdEqualTo(String value) {
            addCriterion("policy_id =", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotEqualTo(String value) {
            addCriterion("policy_id <>", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdGreaterThan(String value) {
            addCriterion("policy_id >", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdGreaterThanOrEqualTo(String value) {
            addCriterion("policy_id >=", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLessThan(String value) {
            addCriterion("policy_id <", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLessThanOrEqualTo(String value) {
            addCriterion("policy_id <=", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLike(String value) {
            addCriterion("policy_id like", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotLike(String value) {
            addCriterion("policy_id not like", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIn(List<String> values) {
            addCriterion("policy_id in", values, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotIn(List<String> values) {
            addCriterion("policy_id not in", values, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdBetween(String value1, String value2) {
            addCriterion("policy_id between", value1, value2, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotBetween(String value1, String value2) {
            addCriterion("policy_id not between", value1, value2, "policyId");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeIsNull() {
            addCriterion("import_template_rule_code is null");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeIsNotNull() {
            addCriterion("import_template_rule_code is not null");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeEqualTo(String value) {
            addCriterion("import_template_rule_code =", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeNotEqualTo(String value) {
            addCriterion("import_template_rule_code <>", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeGreaterThan(String value) {
            addCriterion("import_template_rule_code >", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeGreaterThanOrEqualTo(String value) {
            addCriterion("import_template_rule_code >=", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeLessThan(String value) {
            addCriterion("import_template_rule_code <", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeLessThanOrEqualTo(String value) {
            addCriterion("import_template_rule_code <=", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeLike(String value) {
            addCriterion("import_template_rule_code like", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeNotLike(String value) {
            addCriterion("import_template_rule_code not like", value, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeIn(List<String> values) {
            addCriterion("import_template_rule_code in", values, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeNotIn(List<String> values) {
            addCriterion("import_template_rule_code not in", values, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeBetween(String value1, String value2) {
            addCriterion("import_template_rule_code between", value1, value2, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andImportTemplateRuleCodeNotBetween(String value1, String value2) {
            addCriterion("import_template_rule_code not between", value1, value2, "importTemplateRuleCode");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNull() {
            addCriterion("deleted is null");
            return (Criteria) this;
        }

        public Criteria andDeletedIsNotNull() {
            addCriterion("deleted is not null");
            return (Criteria) this;
        }

        public Criteria andDeletedEqualTo(Boolean value) {
            addCriterion("deleted =", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotEqualTo(Boolean value) {
            addCriterion("deleted <>", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThan(Boolean value) {
            addCriterion("deleted >", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedGreaterThanOrEqualTo(Boolean value) {
            addCriterion("deleted >=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThan(Boolean value) {
            addCriterion("deleted <", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedLessThanOrEqualTo(Boolean value) {
            addCriterion("deleted <=", value, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedIn(List<Boolean> values) {
            addCriterion("deleted in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotIn(List<Boolean> values) {
            addCriterion("deleted not in", values, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andDeletedNotBetween(Boolean value1, Boolean value2) {
            addCriterion("deleted not between", value1, value2, "deleted");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}