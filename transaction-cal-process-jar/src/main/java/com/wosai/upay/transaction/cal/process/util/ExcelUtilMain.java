package com.wosai.upay.transaction.cal.process.util;

import lombok.SneakyThrows;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.net.URL;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2023/10/11.
 */
public class ExcelUtilMain {
    private final static Logger log = LoggerFactory.getLogger(ExcelUtilMain.class);

    /**
     *
     * @param url excel 文件的地址
     * @param sheetNo 从 0 开始
     * @param startLine 从 几行 开始 从0计数 包含此行
     * @param csvFilePath
     */
    @SneakyThrows
    public static void convertExcelToCsv(URL url, int sheetNo, int startLine, String csvFilePath) {
        try {
            Workbook workbook = new XSSFWorkbook(url.openStream());
            if(workbook.getNumberOfSheets() == 1){
                log.warn("{} sheet {} not exists", url, sheetNo);
                return;
            }
            Sheet sheet = workbook.getSheetAt(sheetNo);
            FileWriter csvWriter = new FileWriter(csvFilePath);
            for (int i = startLine; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                for (Cell cell : row) {
                    String cellValue = "";
                    if (cell.getCellType() == CellType.STRING.getCode()) {
                        cellValue = cell.getStringCellValue();
                    } else if (cell.getCellType() == CellType.NUMERIC.getCode()) {
                        cellValue = String.valueOf(cell.getNumericCellValue());
                    } else {
                        cellValue = String.valueOf(cell.getStringCellValue());
                    }
                    //替换 逗号
                    if(cellValue != null && cellValue.contains(",")){
                        cellValue = cellValue.replaceAll(",", "&");
                    }
                    csvWriter.append(cellValue);
                    csvWriter.append(",");
                }
                csvWriter.append("\n");
            }
            Thread.sleep(1000000000);
            csvWriter.flush();
            csvWriter.close();
            log.info("Excel file converted to CSV successfully!");

        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @SneakyThrows
    public static void main(String[] args) {

        convertExcelToCsv(new File("/Users/<USER>/Downloads/海底捞_对账单_2025-03-29_2025-03-30 (1).xlsx").toURI().toURL(), 1, 5, "/Users/<USER>/Downloads/海底捞_对账单_2025-03-29_2025-03-30 (1)Main.csv");
//        ExcelUtil.convertExcelToCsv(new File("/Users/<USER>/Downloads/海底捞_对账单_2025-03-29_2025-03-30 (1).xlsx").toURI().toURL(), 1, 5, "/Users/<USER>/Downloads/海底捞_对账单_2025-03-29_2025-03-30 (1).csv");
    }
}
