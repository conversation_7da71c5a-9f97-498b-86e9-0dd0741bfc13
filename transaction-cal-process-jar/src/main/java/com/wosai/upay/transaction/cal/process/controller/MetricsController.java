package com.wosai.upay.transaction.cal.process.controller;

import com.wosai.middleware.hera.toolkit.metrics.MetricsManager;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
public class MetricsController {
    @GetMapping(path = "/metrics", produces = {"text/plain;version=0.0.4;charset=utf-8"})
    public String endpoint() {
        String data = MetricsManager.scrape();
        return data;
    }

}
