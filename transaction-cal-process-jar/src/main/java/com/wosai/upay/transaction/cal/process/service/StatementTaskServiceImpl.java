package com.wosai.upay.transaction.cal.process.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.Lists;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.sales.core.model.Organization;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.core.model.Group;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.cal.process.constant.StatementType;
import com.wosai.upay.transaction.cal.process.helper.PageInfoHelper;
import com.wosai.upay.transaction.cal.process.mapper.StatementTaskMapper;
import com.wosai.upay.transaction.cal.process.model.domain.StatementTask;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskDto;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskFilterParam;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskParam;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskUpdateParam;
import com.wosai.upay.transaction.cal.process.util.CommonApolloUtil;
import com.wosai.upay.user.api.service.GroupService;
import com.wosai.web.api.ListResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
public class StatementTaskServiceImpl implements StatementTaskService {

    @Autowired
    private StatementTaskMapper statementTaskMapper;

    @Autowired
    private MerchantService merchantService;
    @Autowired
    private GroupService groupService;
    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private OrganizationService organizationService;

    @Override
    public void createTask(StatementTaskParam statementTaskParam) {
        StatementTask statementTask = new StatementTask();
        statementTask.setPlanId(statementTaskParam.getPlanId());
        statementTask.setType(statementTaskParam.getMerchantType());
        statementTask.setPSn(statementTaskParam.getGroupSn());
        statementTask.setMerchantIds(statementTaskParam.getMerchantIds());
        statementTask.setIsvCode(statementTaskParam.getIsvCode());
        statementTask.setStatus(1);
        statementTask.setCreateBy(statementTaskParam.getCreateBy());

        //处理按文件上传
        if (StringUtil.isNotBlank(statementTaskParam.getFileUrl())) {
            try {
                String url = URLDecoder.decode(statementTaskParam.getFileUrl(), "UTF-8");
                String[] merchant_sns = new String(this.restTemplate.getForEntity(url, byte[].class).getBody()).split(",");
                PageInfo pageInfo = new PageInfo();
                int page = 1;
                pageInfo.setPage(page);
                pageInfo.setPageSize(100);
                List<Map> list = merchantService.findMerchants(pageInfo, CollectionUtil.hashMap(
                        "merchant_sns", merchant_sns
                )).getRecords();
                List<String> merchantIds = new ArrayList();
                while (list != null && list.size() > 0) {
                    page = page + 1;
                    pageInfo.setPage(page);
                    merchantIds.addAll(list.stream().map(map -> BeanUtil.getPropString(map, "id").trim()).collect(Collectors.toList()));
                    list = merchantService.findMerchants(pageInfo, CollectionUtil.hashMap(
                            "merchant_sns", merchant_sns
                    )).getRecords();
                }
                if (merchantIds.size() == 0) {
                    throw new RuntimeException("商户号文件不正确.");
                }
                statementTask.setMerchantIds(merchantIds.stream().collect(Collectors.joining(",", "", ",")));
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
                throw new RuntimeException("商户号文件不正确.");
            }
        }
        // 分账业务接收方需要使用商户号作为查询条件
        if(statementTaskParam.getMerchantType() != null
                && statementTaskParam.getMerchantType() == StatementType.TYPE_SHARING_BILL_RECEIVER){
            String merchantIds = statementTask.getMerchantIds();
            if(StringUtil.isNotBlank(merchantIds)){
                String merchantSns = Arrays.asList(merchantIds.split(","))
                        .stream()
                        .map(mid ->{
                            return MapUtil.getString(merchantService.getMerchantByMerchantId(mid), Merchant.SN);
                        })
                        .filter(StringUtil::isNotEmpty)
                        .collect(Collectors.joining(","));
                statementTask.setMerchantIds(merchantSns);
            }
        }

        if (StatementType.TYPE_ISV == statementTaskParam.getMerchantType()) {
            List<StatementTaskDto> tasks = statementTaskMapper.selectTaskList(null
                    , statementTaskParam.getMerchantType()
                    , null, null
                    , null, null
                    , statementTaskParam.getIsvCode());
            if (!CollectionUtils.isEmpty(tasks)) {
                throw new RuntimeException("ISV编号任务已存在.");
            }
        }
        statementTaskMapper.insertSelective(statementTask);
    }

    @Override
    public void enableTask(Long taskId) {
        StatementTask statementTask = new StatementTask();
        statementTask.setId(taskId);
        statementTask.setStatus(1);

        statementTaskMapper.updateByPrimaryKeySelective(statementTask);
    }

    @Override
    public void disableTask(Long taskId) {
        StatementTask statementTask = new StatementTask();
        statementTask.setId(taskId);
        statementTask.setStatus(0);

        statementTaskMapper.updateByPrimaryKeySelective(statementTask);
    }

    @Override
    public void deleteTask(Long taskId) {
        StatementTask statementTask = new StatementTask();
        statementTask.setId(taskId);
        statementTask.setDeleteAt(new Date());

        statementTaskMapper.updateByPrimaryKeySelective(statementTask);
    }

    @Override
    public void updateTask(StatementTaskUpdateParam statementTaskParam) {
        StatementTask statementTask = new StatementTask();

        statementTask.setId(statementTaskParam.getTaskId());
        statementTask.setPlanId(statementTaskParam.getPlanId());
        statementTask.setType(statementTaskParam.getMerchantType());
        statementTask.setPSn(statementTaskParam.getGroupSn());
        statementTask.setMerchantIds(statementTaskParam.getMerchantIds());
        statementTask.setIsvCode(statementTaskParam.getIsvCode());
        statementTaskMapper.updateByPrimaryKeySelective(statementTask);

    }

    @Override
    public ListResult<StatementTaskDto> selectTaskList(PageInfo pageInfo, StatementTaskFilterParam statementTaskFilterParam) {
        pageInfo = PageInfoHelper.checkAndProcess(pageInfo);

        Map merchant = merchantService.getMerchantBySn(statementTaskFilterParam.getMerchantSn());
        String merchant_id = MapUtil.getString(merchant, "id");
        if (StringUtil.isNotBlank(statementTaskFilterParam.getMerchantSn()) && merchant_id == null) {
            return new ListResult<>();
        }

        int pn = pageInfo.getPage();
        int ps = pageInfo.getPageSize();
        Page<StatementTaskDto> page = PageHelper.startPage(pn, ps).doSelectPage(() -> {
            statementTaskMapper.selectTaskList(statementTaskFilterParam.getTaskId()
                    , statementTaskFilterParam.getMerchantType()
                    , statementTaskFilterParam.getGroupSn(), merchant_id
                    , statementTaskFilterParam.getPlanId(), statementTaskFilterParam.getPlanName()
                    , statementTaskFilterParam.getIsvCode()
            );
        });

        ListResult<StatementTaskDto> list = new ListResult<>();
        list.setTotal(page.getTotal());
        List<StatementTaskDto> result = page.getResult().stream().map(statementTaskDto -> {
                    if (StringUtil.isNotEmpty(statementTaskDto.getGroupSn())) {
                        Map map = groupService.getGroupBySn(statementTaskDto.getGroupSn());
                        String groupName = MapUtil.getString(map, Group.NAME);
                        statementTaskDto.setGroupMerchantName(groupName);
                    }
                    if (StringUtil.isNotEmpty(statementTaskDto.getMerchantIds())) {
                        List<Map> merchantList = Lists.newArrayList(statementTaskDto.getMerchantIds().split(","))
                                .stream()
                                .map(merchantService::getMerchant)
                                .map(map -> BeanUtil.getPart(map, Arrays.asList(
                                        "id", Merchant.NAME, Merchant.SN
                                )))
                                .collect(Collectors.toList());
                        statementTaskDto.setMerchantInfoList(merchantList);
                    }
                    if (StringUtil.isNotBlank(statementTaskDto.getIsvCode())) {
                        String name = MapUtil.getString(organizationService.getSimpleOrganization(CollectionUtil.hashMap("code", statementTaskDto.getIsvCode())),
                                Organization.NAME);
                        statementTaskDto.setIsvName(name);
                    }

                    return statementTaskDto;
                }
        ).collect(Collectors.toList());

        list.setRecords(result);

        return list;

    }

    @Override
    public List<String> getStatementVersion() {
        return CommonApolloUtil.getStatementVersion();
    }

    @Override
    public List<StatementTaskDto> findTask(StatementTaskFilterParam statementTaskFilterParam) {
        Map merchant = merchantService.getMerchantBySn(statementTaskFilterParam.getMerchantSn());
        String merchant_id = MapUtil.getString(merchant, "id");
        if (StringUtil.isNotBlank(statementTaskFilterParam.getMerchantSn()) && merchant_id == null) {
            return new ArrayList<>();
        }
        List<StatementTaskDto> statementTaskDtos = statementTaskMapper.selectTaskList(statementTaskFilterParam.getTaskId()
                , statementTaskFilterParam.getMerchantType()
                , statementTaskFilterParam.getGroupSn(), merchant_id
                , statementTaskFilterParam.getPlanId(), statementTaskFilterParam.getPlanName()
                , statementTaskFilterParam.getIsvCode());
        return statementTaskDtos;
    }

    public StatementTask findById(Long id) {
        return statementTaskMapper.selectByPrimaryKey(id);
    }

}
