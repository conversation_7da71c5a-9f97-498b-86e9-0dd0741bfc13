package com.wosai.upay.transaction.cal.process.util;

import com.alibaba.fastjson.JSON;
import com.aliyun.odps.Column;
import com.aliyun.odps.Instance;
import com.aliyun.odps.Odps;
import com.aliyun.odps.OdpsException;
import com.aliyun.odps.account.Account;
import com.aliyun.odps.account.AliyunAccount;
import com.aliyun.odps.data.Record;
import com.aliyun.odps.data.RecordReader;
import com.aliyun.odps.task.SQLTask;
import com.aliyun.odps.tunnel.TableTunnel;
import com.ctrip.framework.apollo.ConfigService;
import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.common.utils.transaction.TransactionUtils;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.sales.core.model.Organization;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.ConstantUtil;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.task.center.model.Transaction;
import com.wosai.upay.transaction.cal.process.constant.StatementType;
import com.wosai.upay.transaction.cal.process.model.domain.StatementPlan;
import com.wosai.upay.transaction.cal.process.model.domain.StatementTaskEx;
import com.wosai.upay.transaction.cal.process.model.dto.StatementConfig;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicReference;

import static com.wosai.common.utils.transaction.Transaction.MCH_DISCOUNT_ORIGIN_TYPE;

@Component
public class OdpsUtils {

    private static final Logger logger = LoggerFactory.getLogger(OdpsUtils.class);
    private static final DateTimeFormatter PT_FORMATTER = DateTimeFormatter.ofPattern("yyyyMMdd");
    private static final DateTimeFormatter DTF = DateTimeFormatter.ofPattern("yyyy/MM/dd HH:mm:ss");

    private static final String BASE_SQL = "SELECT t.* ,mu.name as operator_name,\n" +
            "cm.sn as merchant_sn, cm.name as merchant_name, cs.sn as store_sn, cs.client_sn as store_client_sn, cs.name as store_name, \n" +
            "tt.sn as terminal_sn, tt.client_sn as terminal_client_sn, tt.name as terminal_name, " +
            "CASE WHEN tt.vendor_app_appid IN ('2018033100000614','2018050400000669','2018070400000806','2018052200000725','2018050700000676','2019030600001368','2016050300000001','2018051000000691','2017110200000406','2019090300001975','2020061300002863','2020080300002985') THEN '扫码终端'\n" +
            "                    WHEN tt.vendor_app_appid IN ('2016081800000003','2017020600000069','2019010700001242','2019010700001243','2019010700001244','2019010700001245') THEN '智能POS'\n" +
            "                    WHEN tt.vendor_app_appid='2016090200000007' THEN '传统POS'\n" +
            "                    WHEN tt.vendor_app_appid IN ('2017030600000083','2018041000000638','2019032900021744','2019091800002007') THEN '收银插件'\n" +
            "                    WHEN tt.type=40 AND tt.vendor_id='859d9f5f-af99-11e5-9ec3-00163e00625b' AND (tt.vendor_app_appid IN ('2019053000001597','2016120600014245','2019053100001606','2019071800001840','2019053100001602','2020041000002681') OR vendor_app_appid IS NULL) THEN '门店码'\n" +
            "                    WHEN tt.vendor_app_appid='2019012900001333' THEN '久久折小程序'\n" +
            "                    WHEN tt.vendor_app_appid='2016111100000033' THEN '收钱吧APP'\n" +
            "                    WHEN tt.vendor_app_appid IN (\n" +
            "                                        '2020113000003337','2020113000003338','2020113000003339','2020113000003340',\n" +
            "                                        '2020113000003341','2020113000003342','2020113000003343','2020113000003344','2020113000003345',\n" +
            "                                        '2020113000003346','2020113000003347','2020113000003348','2020113000003349','2020113000003350',\n" +
            "                                        '2020113000003351','2020113000003352','2020113000003353','2020113000003354','2020113000003355',\n" +
            "                                        '2020113000003356','2020113000003357','2020113000003358','2020113000003359','2020113000003360',\n" +
            "                                        '2020113000003361','2020113000003362','2020113000003363','2020113000003364','2020113000003365',\n" +
            "                                        '2020113000003366','2020113000003367','2020113000003368','2020113000003369','2020113000003370',\n" +
            "                                        '2020113000003371','2020113000003372','2020113000003373','2020113000003374','2020113000003375',\n" +
            "                                        '2020113000003376','2020113000003377','2020113000003378','2020113000003379'\n" +
            "                    ) THEN '超盟终端'\n" +
            "                    ELSE '其他' END \n" +
            "               AS terminal_type ,tt.device_fingerprint as terminal_device_fingerprint,\n" +
            "co.code as isv_code\n"+
            "FROM wosai_hz_main.ods_trans_v2_transaction as t\n" +
            "LEFT JOIN wosai_hz_main.ods_crm_merchant as cm ON cm.merchant_id = t.merchant_id\n" +
            "LEFT JOIN wosai_main.dwb_d_crm_organization as co ON  co.id = cm.organization_id\n" +
            "LEFT JOIN wosai_hz_main.ods_crm_v2_store AS cs ON cs.id = t.store_id\n" +
            "LEFT JOIN wosai_hz_main.ods_trans_v2_terminal AS tt ON tt.id = t.terminal_id\n" +
            "LEFT JOIN wosai_hz_main.ods_trans_merchant_user AS mu ON mu.operator_id = t.operator AND mu.pt = '${date}' AND mu.merchant_id = t.merchant_id\n" +
            "WHERE t.pt = '${date}'\n" +
            "AND  cm.pt = '${date}'\n" +
            "AND co.pt = '${date}'\n" +
            "AND cs.pt = '${date}'\n" +
            "AND tt.pt = '${date}'\n" +
            "AND t.status = 2000\n";

    private static final String SQL = BASE_SQL + "AND co.code ='${isv}';";
    private static final String SQL_NEW = BASE_SQL + "AND co.code in (${isv});";


    @Value("${odps.accessId}")
    private String accessId;
    @Value("${odps.accessKey}")
    private String accessKey;
    @Value("${odps.endPoint}")
    private String endPoint;
    @Value("${odps.projectName}")
    private String projectName;

    public static Odps odps;
    private static Map paywayDesc;
    public static LinkedHashMap<String, String> transactionTypeMap;
    public static LinkedHashMap<String, String> transactionStatusMap;
    public static LinkedHashMap<String, String> terminalTypeMap;

    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(3);
        map.put("30", "付款");
        map.put("31", "退款撤销");
        map.put("11", "退款");
        map.put("10", "撤单");
        map.put("32", "预授权");
        map.put("12", "预授权撤销");
        map.put("13", "预授权完成");
        map.put("14", "预授权完成撤销");
        transactionTypeMap = map;
    }

    /**
     * 新支付网关交易状态表
     */
    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(14);
        map.put("0", "处理中");
        map.put("2000", "成功");
        map.put("2001", "失败");
        map.put("2110", "失败");
        map.put("2101", "失败");
        map.put("2102", "失败");
        map.put("2103", "失败");
        map.put("2104", "失败");
        map.put("2105", "失败");
        map.put("2106", "失败");
        map.put("2107", "失败");
        map.put("2108", "失败");
        map.put("2109", "失败");
        map.put("1001", "处理中");
        map.put("1002", "失败");
        transactionStatusMap = map;
    }

    static {
        LinkedHashMap<String, String> map = new LinkedHashMap<String, String>(6);
        map.put("10", "Android应用");
        map.put("11", "iOS应用");
        map.put("20", "Windows桌面应用");
        map.put("30", "专用设备");
        map.put("40", "门店码");
        map.put("50", "服务");
        terminalTypeMap = map;
    }

    private String[] header = "交易日期,时间,商户流水号,收款通道/支付方式,商品名,商户内部订单号,商户订单号,收款通道订单号,交易类型/交易模式,交易状态,付款账户,币种/货币类型,收款金额,收钱吧商户优惠,收钱吧补贴优惠,收款通道补贴优惠,收款通道商户预充值优惠,收款通道商户免充值优惠,消费者实付金额,扣率%,手续费,实收金额,结算金额,商户号,商户名称,门店号,商户门店号,门店名称,终端号,商户终端号,终端名称,终端类型,设备号,操作员,收银员,备注,分账标识,分账金额,收钱吧商户优惠类型,应用方".split(",");

    private String[] headerV2 = "交易日期,时间,商户流水号,收款通道/支付方式,商品名,商户内部订单号,商户订单号,收款通道订单号,交易类型/交易模式,交易状态,付款账户,币种/货币类型,收款金额,收钱吧商户优惠,收钱吧补贴优惠,收款通道补贴优惠,收款通道商户预充值优惠,收款通道商户免充值优惠,消费者实付金额,扣率%,手续费,实收金额,结算金额,商户号,商户名称,门店号,商户门店号,门店名称,终端号,商户终端号,终端名称,终端类型,设备号,操作员,收银员,备注,分账标识,分账金额,收钱吧商户优惠类型,应用方,收钱吧流水号".split(",");

    private String[] headerV1_3 = "交易日期,时间,商户流水号,收款通道/支付方式,商品名,商户内部订单号,商户订单号,收款通道订单号,交易类型/交易模式,交易状态,付款账户,币种/货币类型,收款金额,收钱吧商户优惠,收钱吧补贴优惠,收款通道补贴优惠,收款通道商户预充值优惠,收款通道商户免充值优惠,消费者实付金额,扣率%,手续费,实收金额,结算金额,商户号,商户名称,门店号,商户门店号,门店名称,终端号,商户终端号,终端名称,终端类型,设备号,操作员,收银员,备注,分账标识,分账金额,收钱吧商户优惠类型,应用方,收钱吧流水号,通道交易结束时间".split(",");

    @Autowired
    private OrganizationService organizationService;

    @PostConstruct
    private void init() {
        Account account = new AliyunAccount(accessId, accessKey);
        odps = new Odps(account);
        odps.setEndpoint(endPoint);
        odps.setDefaultProject(projectName);

        logger.info("【OdpsUtils初始化】accessId: {}, accessKey: {}, endPoint: {}, projectName: {}"
                , accessId, "*", endPoint, projectName);

        paywayDesc = getPaywayDesc();
    }


    public String getIsv(String isvCode, String date, String version) {
        String table = "Tmp_" + UUID.randomUUID().toString().replace("-", "_");
        logger.info("getIsv , isvCode: {}, date: {}, table: {}", isvCode, date, table);
        runSqlWithCreateTable(odps, table, buildSql(isvCode, date));
        return tunnel(odps, projectName, table, isvCode, date,version);
    }

    public String buildIsvTable(String isvCodes, String date) {
        String table = "Tmp_" + UUID.randomUUID().toString().replace("-", "_");
        logger.info("getIsv , isvCodes: {}, date: {}, table: {}", isvCodes, date, table);
        runSqlWithCreateTable(odps, table, buildSqlNew(isvCodes, date));
        return table;
    }

    private String buildSql(String isvCode, String date) {
        String sql = SQL.replace("${date}", date).replace("${isv}",   isvCode);
        logger.info("odps sql: {}", sql);
        return sql;
    }

    private String buildSqlNew(String isvCode, String date) {
        String sql = SQL_NEW.replace("${date}", date).replace("${isv}",   isvCode);
        logger.info("odps sql: {}", sql);
        return sql;
    }
//    private void runSql(Odps odps, String sql) {
//        try {
//            Instance i = SQLTask.run(odps, sql);
//            i.waitForSuccess();
//        } catch (OdpsException e) {
//            logger.error("【ODPS--runSql】异常, 异常栈: ", e);
//        }
//    }

    private void runSqlWithCreateTable(Odps odps, String table, String sql) {
        Instance i;
        StringBuilder sb = new StringBuilder("Create Table ").append(table)
                .append(" lifecycle 1 as ").append(sql);
        try {
            i = SQLTask.run(odps, sb.toString());
            i.waitForSuccess();
        } catch (OdpsException e) {
            logger.error("【ODPS--runSqlWithCreateTable】异常, 异常栈: ", e);
            throw new RuntimeException(e);
        }
    }

    private String tunnel(Odps odps, String projectName, String table, String isvCode, String date, String version) {
        TableTunnel tunnel = new TableTunnel(odps);
        try {
            TableTunnel.DownloadSession downloadSession = tunnel.createDownloadSession(projectName, table);
            long count = downloadSession.getRecordCount();
            if (count == 0) {
                logger.info("没有数据");
                return "";
            }
            RecordReader recordReader = downloadSession.openRecordReader(0, count);
            Record record;
            // 写入 CSV
            CSVFormat csvFormat = CSVFormat.DEFAULT;
            // 这是写入CSV的代码

            String fileFullPath = "../" + UUID.randomUUID().toString() + ".csv";
            try (Writer writer = new FileWriter(fileFullPath); CSVPrinter printer = new CSVPrinter(writer, csvFormat)) {
                printer.printRecord("收钱吧交易明细");
                printer.printRecord("isv编号:" + isvCode);
                //获取isv名称
                String name = org.apache.commons.collections4.MapUtils.getString(organizationService.getSimpleOrganization(CollectionUtil.hashMap("code", isvCode)),
                        Organization.NAME);
                printer.printRecord("isv名称:" + name);
                printer.printRecord(String.format(
                        "起始日期" + ":[%s 00:00:00]—:[%s 23:59:59]",
                        date,
                        date
                ));
                printer.printRecord("#-----------------------------------------交易明细列表----------------------------------------#");

                if (version == null || StatementType.VERSION_1_0.equals(version)) {
                    printer.printRecord(header);
                } else if (StatementType.VERSION_1_1.equals(version)) {
                    printer.printRecord(headerV2);
                } else if (StatementType.VERSION_1_3.equals(version)) {
                    printer.printRecord(headerV1_3);
                }

                while ((record = recordReader.read()) != null) {
                    List data = consumeRecord(record, version);
                    if (!CollectionUtils.isEmpty(data)) {
                        try {
                            printer.printRecord(data);
                        } catch (IOException e) {
                            logger.error("printRecord error", e);
                        }
                    }
                }
                writer.flush();
            } catch (Exception e) {
                logger.error("写入isv文件异常:", e);
                throw e;
            }

            String absolutePath = new File(fileFullPath).getAbsolutePath();
            logger.info("absolutePath: {}", absolutePath);
            recordReader.close();
            return absolutePath;
        } catch (Exception e) {
            logger.error("【ODPS--tunnel】异常, 异常栈: ", e);
            throw new RuntimeException(e);
        }
    }


    /**
     *
     * @return key 为isv_code value为文件相对路径
     */
    public Map<String, String> tunnel(String table, Set<String> isvCodes, String date, Map<String, StatementTaskEx> taskMapping) {
        TableTunnel tunnel = new TableTunnel(odps);
        //key:isv_code value:filePath
        Map<String, String> result = new HashMap<>();
        //key:filePath
        Map<String, CSVPrinter> filePrinterMapping = new HashMap<>();

        try {
            TableTunnel.DownloadSession downloadSession = tunnel.createDownloadSession(projectName, table);
            long count = downloadSession.getRecordCount();
            if (count == 0) {
                logger.error("isv对账单导出,codes:{}没有数据", isvCodes);
                return result;
            }
            RecordReader recordReader = downloadSession.openRecordReader(0, count);
            Record record;
            // 写入 CSV
            while ((record = recordReader.read()) != null) {
                String isvCode = record.getString("isv_code");
                String version = "1.0"; // 默认值
                if (isvCode != null) {
                    StatementTaskEx task = taskMapping.get(isvCode);
                    if (task != null && task.getStatementPlan() != null && task.getStatementPlan().getStatementConfig() != null) {
                        StatementConfig config = JSON.parseObject(task.getStatementPlan().getStatementConfig(), StatementConfig.class);
                        if (config != null && config.getVersion() != null) {
                            version = config.getVersion();
                        }
                    }
                }
                String fileFullPath;
                CSVPrinter tuple2 = null;
                if(result.containsKey(isvCode)){
                    fileFullPath = result.get(isvCode);
                    tuple2 = filePrinterMapping.get(fileFullPath);
                }else {
                    fileFullPath = "../" + UUID.randomUUID().toString() + ".csv";
                    logger.info("isv_code:{},文件初始化:{}", isvCode, fileFullPath);
                    result.put(isvCode, fileFullPath);
                    tuple2 = initCSVPrinter(fileFullPath, isvCode, date, version);
                    filePrinterMapping.put(fileFullPath, tuple2);
                }
                List data = consumeRecord(record, version);
                if (!CollectionUtils.isEmpty(data)) {
                    try {
                        tuple2.printRecord(data);
                    } catch (IOException e) {
                        logger.error("printRecord error", e);
                    }
                }
            }
            for (CSVPrinter value : filePrinterMapping.values()) {
                value.close(true);
            }
            recordReader.close();
            return result;
        } catch (Exception e) {
            logger.error("【ODPS--tunnel】异常, 异常栈: ", e);
            throw new RuntimeException(e);
        }
    }


    private CSVPrinter initCSVPrinter(String path, String isvCode, String date, String version) {
        CSVPrinter printer = null;
        try {
            printer = new CSVPrinter(new FileWriter(path), CSVFormat.DEFAULT);
            printer.printRecord("收钱吧交易明细");
            printer.printRecord("isv编号:" + isvCode);
            //获取isv名称
            String name = org.apache.commons.collections4.MapUtils.getString(organizationService.getSimpleOrganization(CollectionUtil.hashMap("code", isvCode)),
                    Organization.NAME);
            printer.printRecord("isv名称:" + name);
            printer.printRecord(String.format(
                    "起始日期" + ":[%s 00:00:00]—:[%s 23:59:59]",
                    date,
                    date
            ));
            printer.printRecord("#-----------------------------------------交易明细列表----------------------------------------#");
            if (version == null || StatementType.VERSION_1_0.equals(version)) {
                printer.printRecord(header);
            } else if (StatementType.VERSION_1_1.equals(version)) {
                printer.printRecord(headerV2);
            } else if (StatementType.VERSION_1_3.equals(version)) {
                printer.printRecord(headerV1_3);
            }
        } catch (IOException e) {
            logger.error("初始化isv文件code:{},失败:{}", isvCode, e.getMessage());
            throw new RuntimeException(e);
        }
        return printer;
    }

    private static List consumeRecord(Record record, String version) {
        List rowValue = new ArrayList();
        //record 转map
        Map<String, Object> data = new HashMap<>();
        Column[] columns = record.getColumns();
        int columnCount = record.getColumnCount();

        for (int idx = 0; idx < columnCount; idx++) {
            String columnName = columns[idx].getName();
            if(columnName.equals("isv_code")){
                //该字段不导出
                continue;
            }
            data.put(columnName, record.get(columnName));
        }
        TransationUtil.transformReflect(data);
        TransationUtil.jsonFormatOrder(data);
        TransationUtil.expandTransactionItemsPayments(data, false);
        TransationUtil.expandTransactionItemTradeInfo(data);
        TransationUtil.calculateExtendFields(data);

        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
        SimpleDateFormat simpleDateFormat2 = new SimpleDateFormat("HH:mm:ss");
        rowValue.add(simpleDateFormat.format(record.get(DaoConstants.CTIME)));//交易日期
        rowValue.add(simpleDateFormat2.format(record.get(DaoConstants.CTIME)));//时间

        String clientTsn = null;
        String refundRequestNo = null;
        Integer transactionType = record.getBigint(Transaction.TYPE).intValue();
        //商户流水号
        if (Transaction.TYPE_REFUND == transactionType) {
            String client_tsn = record.getString(Transaction.CLIENT_TSN);
//            logger.info("商户流水号={}", client_tsn);
            if (StringUtils.hasText(client_tsn) && client_tsn.split("-").length == 2) {
                int index = client_tsn.lastIndexOf("-");
                clientTsn = client_tsn.substring(0, index);
                refundRequestNo = client_tsn.substring(index + 1);
//                logger.info("clientTsn={},refundRequestNo={}", clientTsn, refundRequestNo);
            }
        }
        if (Transaction.TYPE_REFUND == transactionType && refundRequestNo != null) {
            rowValue.add(refundRequestNo);
        } else {
            rowValue.add(record.getString(Transaction.CLIENT_TSN));
        }
        rowValue.add(MapUtils.getString(paywayDesc, record.get("payway") + ""));//收款通道
        rowValue.add(record.getString(Transaction.SUBJECT));//商品名

        if (Transaction.TYPE_REFUND == transactionType && clientTsn != null) {
            rowValue.add(clientTsn);
        } else {
            rowValue.add(record.getString(Transaction.CLIENT_TSN));
        }
        rowValue.add(record.getString(Transaction.ORDER_SN));//商户订单号
        rowValue.add(record.getString(Transaction.TRADE_NO));//收款通道订单号
        rowValue.add(MapUtils.getString(transactionTypeMap, record.getBigint(Transaction.TYPE).toString()));//交易类型
        rowValue.add(MapUtils.getString(transactionStatusMap, record.getBigint(Transaction.STATUS).toString()));//交易状态
        //付款账户
        String buyerUid = record.getString("buyer_uid");
        String buyerLogin = record.getString("buyer_login");
        if ("3".equals(record.get("payway"))) {//付款账户
            rowValue.add(StringUtils.hasText(buyerUid) ? buyerUid : buyerLogin);
        } else {
            rowValue.add(buyerLogin);
        }
        rowValue.add(BeanUtil.getPropString(data, Transaction.CURRENCY));//货币类型


        long effectiveAmount = Optional.ofNullable(record.getBigint(Transaction.EFFECTIVE_AMOUNT)).orElse(0L);
        long paidAmount = Optional.ofNullable(record.getBigint(Transaction.PAID_AMOUNT)).orElse(0L);
        long channelDiscount = BeanUtil.getPropLong(data, Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT, 0L);
        long channelMchDiscount = BeanUtil.getPropLong(data, Transaction.DISCOUNT_CHANNEL_MCH_AMOUNT, 0L);
        long channelMchTopUpDiscount = BeanUtil.getPropLong(data, Transaction.DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT, 0L);
        ;
        long originalAmount = Optional.ofNullable(record.getBigint(Transaction.ORIGINAL_AMOUNT)).orElse(0L);
        long discountAmount = BeanUtil.getPropLong(data, Transaction.DISCOUNT_WOSAI_AMOUNT);
        long actualReceiveAmount = BeanUtil.getPropLong(data, Transaction.ACTUAL_RECEIVE_AMOUNT, 0);
        long clearingAmount = BeanUtil.getPropLong(data, Transaction.CLEARING_AMOUNT, 0);
        long merchantDiscount = BeanUtil.getPropLong(data, Transaction.DISCOUNT_WOSAI_MCH_AMOUNT, 0L);
        long tradeFee = BeanUtil.getPropLong(data, Transaction.FEE, 0L);
        long sharingAmount = BeanUtil.getPropLong(data, Transaction.SHARING_AMOUNT, 0);
        if (paidAmount == 0L) {
            paidAmount = effectiveAmount - (channelDiscount + channelMchDiscount + channelMchTopUpDiscount);
        }
        if (Transaction.TYPE_PAYMENT != transactionType && Transaction.TYPE_REFUND_REVOKE != transactionType && Transaction.TYPE_DEPOSIT_CONSUME != transactionType) {
            originalAmount = originalAmount * -1;
            paidAmount = paidAmount * -1;
            discountAmount *= -1;
            actualReceiveAmount *= -1;
            clearingAmount *= -1;
            merchantDiscount *= -1;
            channelDiscount *= -1;
            channelMchDiscount *= -1;
            channelMchTopUpDiscount *= -1;
            tradeFee *= -1;
            sharingAmount *= -1;
        }

        rowValue.add(originalAmount / 100.0);//收款金额
        rowValue.add(merchantDiscount / 100.0);//收钱吧商户优惠
        rowValue.add(discountAmount / 100.0);//收钱吧补贴优惠
        rowValue.add(channelDiscount / 100.0);//收款通道补贴优惠
        rowValue.add(channelMchTopUpDiscount / 100.0);//收款通道商户预充值优惠
        rowValue.add(channelMchDiscount / 100.0);//收款通道商户免充值优惠

        rowValue.add(paidAmount / 100.0);//消费者实付金额
        rowValue.add(BeanUtil.getPropString(data, TransactionParam.FEE_RATE));//扣率%
        rowValue.add(tradeFee / 100.0);//手续费
        rowValue.add(actualReceiveAmount / 100.0);//实收金额
        rowValue.add(clearingAmount / 100.0);//结算金额

        rowValue.add(record.getString("merchant_sn"));//商户号
        rowValue.add(record.getString(Transaction.MERCHANT_NAME));//商户名称
        rowValue.add(record.getString(ConstantUtil.KEY_STORE_SN));//门店号
        rowValue.add(record.getString("store_client_sn"));//商户门店号
        rowValue.add(record.getString(ConstantUtil.KEY_STORE_NAME));//门店名称
        rowValue.add(record.getString(ConstantUtil.KEY_TERMINAL_SN));//终端号
        rowValue.add(record.getString("terminal_client_sn"));//商户终端号
        rowValue.add(record.getString(ConstantUtil.KEY_TERMINAL_NAME));//终端名称
        rowValue.add(record.getString("terminal_type"));//终端类型
        rowValue.add(record.getString("terminal_device_fingerprint"));//设备号
        rowValue.add(record.getString(Transaction.OPERATOR));//操作员
        rowValue.add(record.getString("operator_name"));//收银员
        Object reflect = data.get(Transaction.REFLECT);//备注
        if (reflect instanceof Map) {
            rowValue.add(CollectionUtils.isEmpty((Map) reflect) ? "" : WosaiJsonUtils.toJSONString(reflect));
        } else {
            rowValue.add(Objects.isNull(reflect) ? "" : "" + reflect);
        }

        int sharingFlag = BeanUtil.getPropInt(data, TransactionEnhanceFields.SHARING_FLAG.getField());
//        String productFlag = BeanUtil.getPropString(transaction, Transaction.PRODUCT_FLAG);
        rowValue.add(sharingFlag == 1 ? "是" : "否"); //分账标识
        rowValue.add(sharingAmount / 100.0);//分账金额
        TransactionUtils.expandMchDiscountOriginType(data);
        rowValue.add(BeanUtil.getPropString(data, MCH_DISCOUNT_ORIGIN_TYPE, ""));//收钱吧商户优惠类型
        String productFlag = record.getString(Transaction.PRODUCT_FLAG);
        rowValue.add(ProductFlagUtils.getProductFlagDesc(productFlag));//应用方
        if (StatementType.VERSION_1_1.equals(version) || StatementType.VERSION_1_3.equals(version)) {
            rowValue.add(record.getString(Transaction.TSN));
        }
        if (StatementType.VERSION_1_3.equals(version)) {
            Long channelFinishTime = record.getBigint(Transaction.CHANNEL_FINISH_TIME);
            channelFinishTime = channelFinishTime != null ? channelFinishTime : record.getBigint(Transaction.FINISH_TIME);
            channelFinishTime = channelFinishTime != null ? channelFinishTime : record.getBigint(Transaction.MTIME);
            rowValue.add(formatTimestamp(channelFinishTime));
        }
        return rowValue;
    }



    public Map getPaywayDesc() {
        String property = ConfigService.getConfig("core.global-config").getProperty("payway-desc", "{}");
        Map map = new Gson().fromJson(property, new TypeToken<Map<String, String>>() {
        }.getType());
        return map;
    }

    public static String formatTimestamp(long timestamp) {
        LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(timestamp),
                ZoneId.systemDefault()
        );
        return dateTime.format(DTF);
    }

}
