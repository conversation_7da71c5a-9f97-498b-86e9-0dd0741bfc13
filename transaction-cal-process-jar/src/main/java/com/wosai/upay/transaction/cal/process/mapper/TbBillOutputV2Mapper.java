package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2;
import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputV2Example;

import java.util.Date;
import java.util.List;

import com.wosai.upay.transaction.cal.process.model.dto.BillOutput;
import com.wosai.upay.transaction.cal.process.model.dto.TradeInfoDto;
import org.apache.ibatis.annotations.Param;

public interface TbBillOutputV2Mapper {
    long countByExample(TbBillOutputV2Example example);

    TradeInfoDto statisticalTradeTotal(@Param("taskId") Integer taskId);

    int deleteByExample(TbBillOutputV2Example example);

    int deleteByPrimaryKey(Long id);

    int insert(TbBillOutputV2 record);

    int insertSelective(TbBillOutputV2 record);

    int batchInsert(@Param("list") List<TbBillOutputV2> TbBillOutputV2List);

    List<TbBillOutputV2> selectByExample(TbBillOutputV2Example example);

    TbBillOutputV2 selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TbBillOutputV2 record, @Param("example") TbBillOutputV2Example example);

    int updateByExample(@Param("record") TbBillOutputV2 record, @Param("example") TbBillOutputV2Example example);

    int updateByPrimaryKeySelective(TbBillOutputV2 record);

    int updateByPrimaryKey(TbBillOutputV2 record);

    List<BillOutput> findBillOutputList(@Param("billType") Integer billType,
                                        @Param("billSourceCompany") Integer billSourceCompany,
                                        @Param("billSourceClassify") Integer billSourceClassify,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate,
                                        @Param("sourceMerchantId") String sourceMerchantId,
                                        @Param("sourceMerchantName") String sourceMerchantName,
                                        @Param("sqbMerchantSn") String sqbMerchantSn,
                                        @Param("serviceProviderId") String serviceProviderId,
                                        @Param("lastId") Long lastId);

    int insertOrUpdateBatch(List<TbBillOutputV2> recordList);
}