package com.wosai.upay.transaction.cal.process.util;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.wosai.common.utils.transaction.TransactionEnhanceFields;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.dao.DaoConstants;
import com.wosai.data.util.CollectionUtil;
import com.wosai.data.util.StringUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.core.model.TransactionParam;
import com.wosai.upay.task.center.model.Payment;
import com.wosai.upay.task.center.model.StatementObjectConfig;
import com.wosai.upay.task.center.model.Transaction;
import org.apache.commons.collections.MapUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import static com.wosai.upay.transaction.cal.process.util.StringUtil.getMapFromJsonObject;

public class TransationUtil {
    private final static Logger logger = LoggerFactory.getLogger(TransationUtil.class);
    public static ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 由于折扣立减信息存放在transaction items 里面的payments里面，把这些字段拆开为
     * discount_wosai_amount
     * discount_wosai_mch_amount
     * hongbao_wosai_amount
     * hongbao_wosai_mch_amount
     * discount_channel_amount
     * discount_channel_mch_amount
     * discount_channel_mch_top_up_amount
     * hongbao_channel_amount
     * hongbao_channel_mch_amount
     * hongbao_channel_mch_top_up_amount
     * 如果这些字段有值，才添加相应字段， 早期收钱吧活动立减也需要把对应的金额转换为对应的字段
     */
    public static void expandTransactionItemsPayments(Map tran, boolean removeExtraOutFields) {
        Long hongbaoWosaiAmount = null;
        Long hongbaoWosaiMchAmount = null;
        Long discountWosaiAmount = null;
        Long discountWosaiMchAmount = null;

        Long discountChannelAmount = null;
        Long discountChannelMchAmount = null;
        Long discountChannelMchTopUpAmount = null;
        Long hongbaoChannelAmount = null;
        Long hongbaoChannelMchAmount = null;
        Long hongbaoChannelMchTopUpAmount = null;
        String originType = null;

        List<Map<String, Object>> payments = getListFromObject(BeanUtil.getNestedProperty(tran, Transaction.ITEMS + "." + Transaction.PAYMENTS));

        if (payments != null && payments.size() > 0) {
            for (Map payment : payments) {
                String type = BeanUtil.getPropString(payment, Payment.TYPE, "");
                long amount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_HONGBAO_WOSAI:
                        hongbaoWosaiAmount = hongbaoWosaiAmount == null ? 0 : hongbaoWosaiAmount;
                        hongbaoWosaiAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_WOSAI_MCH:
                        hongbaoWosaiMchAmount = hongbaoWosaiMchAmount == null ? 0 : hongbaoWosaiMchAmount;
                        hongbaoWosaiMchAmount += amount;
                        originType = BeanUtil.getPropString(payment, Payment.ORIGIN_TYPE);//woSai商户红包类型
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI:
                        discountWosaiAmount = discountWosaiAmount == null ? 0 : discountWosaiAmount;
                        discountWosaiAmount += amount;
                        break;
                    case Payment.TYPE_DISCOUNT_WOSAI_MCH:
                        discountWosaiMchAmount = discountWosaiMchAmount == null ? 0 : discountWosaiMchAmount;
                        discountWosaiMchAmount += amount;
                        originType = BeanUtil.getPropString(payment, Payment.ORIGIN_TYPE);//woSai商户折扣类型
                        break;
                    default:
                        break;
                }
            }
        } else {
            //处理早期收钱吧立减红包订单 在这里区分不了是红包还是立减了，全部当做是立减
            long originalAmount = BeanUtil.getPropLong(tran, Transaction.ORIGINAL_AMOUNT);
            long effectiveAmount = BeanUtil.getPropLong(tran, Transaction.EFFECTIVE_AMOUNT);

            if (originalAmount - effectiveAmount > 0) {
                discountWosaiAmount = 0 + (originalAmount - effectiveAmount);
            }
        }

        List<Map> extraPayments = getListFromObject(BeanUtil.getNestedProperty(tran, Transaction.EXTRA_OUT_FIELDS + "." + Transaction.PAYMENTS));
        if (removeExtraOutFields) {
            tran.remove(Transaction.EXTRA_OUT_FIELDS);
        }
        if (extraPayments != null && extraPayments.size() > 0) {
            for (Map payment : extraPayments) {
                String type = BeanUtil.getPropString(payment, Payment.TYPE, "");
                long amount = BeanUtil.getPropLong(payment, Payment.AMOUNT);
                switch (type) {
                    case Payment.TYPE_DISCOUNT_CHANNEL:
                        discountChannelAmount = discountChannelAmount == null ? 0 : discountChannelAmount;
                        discountChannelAmount += amount;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH:
                        discountChannelMchAmount = discountChannelMchAmount == null ? 0 : discountChannelMchAmount;
                        discountChannelMchAmount += amount;
                        break;
                    case Payment.TYPE_DISCOUNT_CHANNEL_MCH_TOP_UP:
                        discountChannelMchTopUpAmount = discountChannelMchTopUpAmount == null ? 0 : discountChannelMchTopUpAmount;
                        discountChannelMchTopUpAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL:
                        hongbaoChannelAmount = hongbaoChannelAmount == null ? 0 : hongbaoChannelAmount;
                        hongbaoChannelAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH:
                        hongbaoChannelMchAmount = hongbaoChannelMchAmount == null ? 0 : hongbaoChannelMchAmount;
                        hongbaoChannelMchAmount += amount;
                        break;
                    case Payment.TYPE_HONGBAO_CHANNEL_MCH_TOP_UP:
                        hongbaoChannelMchTopUpAmount = hongbaoChannelMchTopUpAmount == null ? 0 : hongbaoChannelMchTopUpAmount;
                        hongbaoChannelMchTopUpAmount += amount;
                        break;
                    default:
                        break;
                }
            }
        }

        if (hongbaoWosaiAmount != null) {
            tran.put(Transaction.HONGBAO_WOSAI_AMOUNT, hongbaoWosaiAmount);
        }
        if (hongbaoWosaiMchAmount != null) {
            tran.put(Transaction.HONGBAO_WOSAI_MCH_AMOUNT, hongbaoWosaiMchAmount);
            if (!StringUtils.isEmpty(originType)) {
                tran.put(Transaction.DISCOUNT_WOSAI_MCH_TYPE, originType);
            }
        }
        if (discountWosaiAmount != null) {
            tran.put(Transaction.DISCOUNT_WOSAI_AMOUNT, discountWosaiAmount);
        }
        if (discountWosaiMchAmount != null) {
            tran.put(Transaction.DISCOUNT_WOSAI_MCH_AMOUNT, discountWosaiMchAmount);
            if (!StringUtils.isEmpty(originType)) {
                tran.put(Transaction.DISCOUNT_WOSAI_MCH_TYPE, originType);
            }
        }
        if (discountChannelAmount != null) {
            tran.put(Transaction.DISCOUNT_CHANNEL_AMOUNT, discountChannelAmount);
        }
        if (discountChannelMchAmount != null) {
            tran.put(Transaction.DISCOUNT_CHANNEL_MCH_AMOUNT, discountChannelMchAmount);
        }
        if (discountChannelMchTopUpAmount != null) {
            tran.put(Transaction.DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT, discountChannelMchTopUpAmount);
        }
        if (hongbaoChannelAmount != null) {
            tran.put(Transaction.HONGBAO_CHANNEL_AMOUNT, hongbaoChannelAmount);
        }
        if (hongbaoChannelMchAmount != null) {
            tran.put(Transaction.HONGBAO_CHANNEL_MCH_AMOUNT, hongbaoChannelMchAmount);
        }
        if (hongbaoChannelMchTopUpAmount != null) {
            tran.put(Transaction.HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNT, hongbaoChannelMchTopUpAmount);
        }

    }

    public static void calculateExtendFields(Map orderEx) {
        int status = BeanUtil.getPropInt(orderEx, Transaction.STATUS);
        int type = BeanUtil.getPropInt(orderEx, Transaction.TYPE);
        Long actualReceiveAmount = 0L;
        Long clearingAmount = 0L;
        Long mchFavorableAmount = 0L;
        Long wosaiFavorableAmount = 0L;
        Long channelFavorableAmount = 0L;
        Long channelAgentFavorableAmount = 0L;
        Long channelMchFavorableAmount = 0L;
        Long channelMchTopUpFavorableAmount = 0L;
        Long sharingAmount = 0L;
        //预授权 预授权撤销 不计算
        if (status == Transaction.STATUS_SUCCESS && (Transaction.TYPE_DEPOSIT_FREEZE != type && Transaction.TYPE_DEPOSIT_CANCEL != type)) {
            Long originalAmount = BeanUtil.getPropLong(orderEx, Transaction.ORIGINAL_AMOUNT);
            Long effectiveAmount = BeanUtil.getPropLong(orderEx, Transaction.EFFECTIVE_AMOUNT);
            Long paidAmount = BeanUtil.getPropLong(orderEx, Transaction.PAID_AMOUNT);

            Long hongbaoWosaiMchAmount = BeanUtil.getPropLong(orderEx, Transaction.HONGBAO_WOSAI_MCH_AMOUNT);
            Long discountWosaiMchAmount = BeanUtil.getPropLong(orderEx, Transaction.DISCOUNT_WOSAI_MCH_AMOUNT);

            Long discountChannelAmount = BeanUtil.getPropLong(orderEx, Transaction.DISCOUNT_CHANNEL_AMOUNT);
            Long discountChannelMchAmount = BeanUtil.getPropLong(orderEx, Transaction.DISCOUNT_CHANNEL_MCH_AMOUNT);
            Long discountChannelMchTopUpAmount = BeanUtil.getPropLong(orderEx, Transaction.DISCOUNT_CHANNEL_MCH_TOP_UP_AMOUNT);
            Long hongbaoChannelAmount = BeanUtil.getPropLong(orderEx, Transaction.HONGBAO_CHANNEL_AMOUNT);
            Long hongbaoChannelMchAmount = BeanUtil.getPropLong(orderEx, Transaction.HONGBAO_CHANNEL_MCH_AMOUNT);
            Long hongbaoChannelMchTopUpAmount = BeanUtil.getPropLong(orderEx, Transaction.HONGBAO_CHANNEL_MCH_TOP_UP_AMOUNT);

            //商户优惠
            mchFavorableAmount = OrderOrTransAmountUtil.getMerchantFavorableAmount(hongbaoWosaiMchAmount, discountWosaiMchAmount);
            //收钱吧优惠
            wosaiFavorableAmount = OrderOrTransAmountUtil.getWosaiFavorableAmount(originalAmount, effectiveAmount, mchFavorableAmount);
            //收款通道机构优惠
            channelAgentFavorableAmount = OrderOrTransAmountUtil.getChannelAgentFavorableAmount(hongbaoChannelAmount, discountChannelAmount);
            //收款通道商户免充值优惠
            channelMchFavorableAmount = OrderOrTransAmountUtil.getChannelMchFavorableAmount(discountChannelMchAmount, hongbaoChannelMchAmount);
            //收款通道商户充值优惠
            channelMchTopUpFavorableAmount = OrderOrTransAmountUtil.getChannelMchTopUpFavorableAmount(discountChannelMchTopUpAmount, hongbaoChannelMchTopUpAmount);
            //收款通道优惠
            channelFavorableAmount = OrderOrTransAmountUtil.getChannelFavorableAmount(channelAgentFavorableAmount, channelMchFavorableAmount, channelMchTopUpFavorableAmount);
            //实收金额
//            logger.info("mchFavorableAmount={} ,channelMchFavorableAmount={}", mchFavorableAmount, channelMchFavorableAmount);
            actualReceiveAmount = OrderOrTransAmountUtil.getActualReceiveAmount(originalAmount, mchFavorableAmount, channelMchFavorableAmount);

            Long fee = BeanUtil.getPropLong(orderEx, Transaction.FEE);

            //兼容支付网关费率计算的问题，original_amount 和 effective_amount 为0时，手续费为0
            if (originalAmount == 0 && effectiveAmount == 0) {
                fee = 0l;
                orderEx.put(Transaction.FEE, fee);
            }

            //如果支付网关trade_config 没有记录手续费，由(实收金额 * 费率)计算得出
            if (com.wosai.data.util.StringUtil.empty(BeanUtil.getPropString(orderEx, Transaction.FEE))) {
                double feeRate = 0.0;
                String feeRateString = BeanUtil.getPropString(orderEx, TransactionParam.FEE_RATE, "0.0");
                if (feeRateString.trim() != "") {
                    feeRate = formatMoney(feeRateString, 3);
                }
                fee = Math.round(actualReceiveAmount * feeRate * 0.01);
                orderEx.put(Transaction.FEE, fee);
            }
            if (!StringUtil.empty(BeanUtil.getPropString(orderEx, TransactionParam.FEE_ORIGINAL))) {
                String originalFeeRate = BeanUtil.getPropString(orderEx, TransactionParam.FEE_RATE_ORIGINAL, "0.0");
                orderEx.put(TransactionParam.FEE_ORIGINAL, Math.round(actualReceiveAmount * formatMoney(originalFeeRate, 3) * 0.01));
            }
            //结算金额
            clearingAmount = OrderOrTransAmountUtil.getClearingAmount(actualReceiveAmount, fee);
            //分账标识
            Integer sharingFlag = BeanUtil.getPropInt(orderEx, TransactionEnhanceFields.SHARING_FLAG.getField(), 0);
            if (Objects.equals(sharingFlag, 1)) {
                Double ratioTotal = MapUtils.getDouble(orderEx, TransactionEnhanceFields.RATIO_TOTAL.getField(), 0.0);
                //分账支出金额
                sharingAmount = Math.round(clearingAmount * ratioTotal * 0.01);
            }

        }

        orderEx.put(Transaction.MCH_FAVORABLE_AMOUNT, mchFavorableAmount);
        orderEx.put(Transaction.WOSAI_FAVORABLE_AMOUNT, wosaiFavorableAmount);
        orderEx.put(Transaction.CHANNEL_FAVORABLE_AMOUNT, channelFavorableAmount);
        orderEx.put(Transaction.CHANNEL_AGENT_FAVORABLE_AMOUNT, channelAgentFavorableAmount);
        orderEx.put(Transaction.CHANNEL_MCH_FAVORABLE_AMOUNT, channelMchFavorableAmount);
        orderEx.put(Transaction.CHANNEL_MCH_TOP_UP_FAVORABLE_AMOUNT, channelMchTopUpFavorableAmount);
        orderEx.put(Transaction.ACTUAL_RECEIVE_AMOUNT, actualReceiveAmount);
        orderEx.put(Transaction.SHARING_AMOUNT, sharingAmount);
        orderEx.put(Transaction.CLEARING_AMOUNT, clearingAmount - sharingAmount);

    }

    public static void expandTransactionItemTradeInfo(Map<String, Object> transaction) {
        Object extraOutField = BeanUtil.getProperty(transaction, Transaction.EXTRA_OUT_FIELDS);
        Map extraOut = getMapFromJsonObject(extraOutField);
        List payments = (List) BeanUtil.getProperty(extraOut, Transaction.PAYMENTS);
        Map overseas = (Map) BeanUtil.getProperty(extraOut, Transaction.OVERSEAS);
        String bankTradeNo = BeanUtil.getPropString(extraOut, Transaction.TRADE_NO, "");
        transaction.put(Transaction.BANK_TRADE_NO, bankTradeNo);
        transaction.put("payments", payments);
        Map configSnapshot = null;
        try {
            configSnapshot = (Map) BeanUtil.getProperty(transaction, Transaction.CONFIG_SNAPSHOT);
        } catch (Exception e) {
            logger.error("transfer transaction config_snapshot to map exception: [{}]", e);
        }
        Map<String, Object> tradeInfo = getTradeConfigInfoByConfigSnapshot(configSnapshot);
        transaction.put(TransactionParam.APP_AUTH_SHOP_ID, BeanUtil.getPropString(tradeInfo, TransactionParam.APP_AUTH_SHOP_ID));
        transaction.put(TransactionParam.FEE_RATE, BeanUtil.getPropString(tradeInfo, TransactionParam.FEE_RATE));
        transaction.put(TransactionParam.LIQUIDATION_NEXT_DAY, BeanUtil.getPropBoolean(tradeInfo, TransactionParam.LIQUIDATION_NEXT_DAY));
        String merchantDefaultCurrency = BeanUtil.getPropString(configSnapshot, TransactionParam.CURRENCY, "CNY");
        transaction.put(Transaction.MERCHANT_DEFAULT_CURRENCY, merchantDefaultCurrency); //商户基础信息中设置的交易币种
        transaction.put(Transaction.CURRENCY, BeanUtil.getPropString(overseas, Transaction.CURRENCY, merchantDefaultCurrency)); //实际交易中消费者用的币种 transaction.extra_out_fields.overseas.currency
        transaction.put(Transaction.PAYMENT_INST, BeanUtil.getPropString(overseas, Transaction.PAYMENT_INST, "CN"));
        transaction.put(Transaction.PAYWAY, filterOverseasPayway(transaction));
        if (!StringUtil.empty(BeanUtil.getPropString(tradeInfo, TransactionParam.FEE))) {
            transaction.put(TransactionParam.FEE, BeanUtil.getPropLong(tradeInfo, TransactionParam.FEE));
        }
        if (!StringUtil.empty(BeanUtil.getPropString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL))) {
            transaction.put(TransactionParam.FEE_RATE_ORIGINAL, BeanUtil.getPropString(tradeInfo, TransactionParam.FEE_RATE_ORIGINAL));
        }
        transaction.remove(Transaction.CONFIG_SNAPSHOT);

    }

    /**
     * 获取费率以及是否待结算信息
     *
     * @param configSnapshot
     * @return
     */
    public static Map<String, Object> getTradeConfigInfoByConfigSnapshot(Object configSnapshot) {
        if (configSnapshot == null) {
            return null;
        } else {
            Map snapshot = getMapFromJsonObject(configSnapshot);
            Map tradeParams = getTradeConfigFromConfigSnapshot(snapshot);
            boolean liquidationNextDay = BeanUtil.getPropBoolean(tradeParams, TransactionParam.LIQUIDATION_NEXT_DAY, false);
            String currency = BeanUtil.getPropString(snapshot, TransactionParam.CURRENCY, "CNY");

            String feeRateString = BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE, "0.0");
            if (feeRateString.trim().equals("")) {
                feeRateString = "0.0";
            }
            Map tradeConfig = CollectionUtil.hashMap(
                    TransactionParam.FEE_RATE, feeRateString,
                    TransactionParam.LIQUIDATION_NEXT_DAY, liquidationNextDay,
                    TransactionParam.CURRENCY, currency
            );
            if (!StringUtil.empty(BeanUtil.getPropString(tradeParams, TransactionParam.FEE))) {
                tradeConfig.put(TransactionParam.FEE, BeanUtil.getPropLong(tradeParams, TransactionParam.FEE));
            }
            if (!StringUtil.empty(BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE_ORIGINAL))) {
                tradeConfig.put(TransactionParam.FEE_RATE_ORIGINAL, BeanUtil.getPropString(tradeParams, TransactionParam.FEE_RATE_ORIGINAL));
            }
            return tradeConfig;
        }
    }


    public static void jsonFormatOrder(Map<String, Object> order) {
        List<String> jsonBlobColumnList = Arrays.asList(
                Transaction.ITEMS,
                Transaction.EXTRA_PARAMS,
                Transaction.EXTRA_OUT_FIELDS,
                Transaction.EXTENDED_PARAMS,
                Transaction.CONFIG_SNAPSHOT,
                Transaction.REFLECT,
                Transaction.BIZ_ERROR_CODE,
                Transaction.PROVIDER_ERROR_INFO
        );

        jsonBlobColumnList.parallelStream().forEach(column -> {
            Object value = order.get(column);
            if (!(value instanceof Map) && value != null) {
                Object data = null;
                try {
                    if (value instanceof byte[]) {
                        data = objectMapper.readValue((byte[]) value, Object.class);
                    } else if (value instanceof String) {
                        try {
                            data = objectMapper.readValue((String) value, Object.class);
                        } catch (Exception ex) {
                            data = value;
                        }
                    } else {
                        data = objectMapper.readValue(value.toString(), Object.class);
                    }
                } catch (JsonProcessingException e) {
                } catch (IOException ioException) {
                } catch (ClassCastException classCastException) {
                    logger.error("catch exception json format order: id = " + BeanUtil.getPropString(order, DaoConstants.ID) + " column = " + column, classCastException);
                }

                if (data != null && !(data instanceof Map)) {
                    logger.info("data is not map, id = {} column = {}", BeanUtil.getPropString(order, DaoConstants.ID), column);
                }
                order.put(column, data);
            }
        });
    }

    public static void transformReflect(Map<String, Object> transaction) {
        Object reflect = BeanUtil.getProperty(transaction, Transaction.REFLECT);
        if (reflect instanceof byte[]) {
            String reflectString = new String((byte[]) reflect);
            if ("null".equals(reflectString) || "\"\"".equals(reflectString)) {
                reflectString = null;
            }
            transaction.put(Transaction.REFLECT, reflectString);
        }
    }

    private static List getListFromObject(Object object) {
        try {
            if (object == null) {
                return null;
            } else if (object instanceof List) {
                return (List) object;
            } else if (object instanceof Map) {
                return Arrays.asList(object);
            } else {
                return null;
            }
        } catch (Exception e) {
            logger.error("can not transfer to List: " + object);
            return null;
        }
    }

    /**
     * 根据流水相关信息过滤出跨境支付的字段
     *
     * @param transaction
     * @return 99    支付宝香港-跨境	overseas.payment_inst== "ALIPAYCN" && overseas.currency == "CNY"
     * 98	支付宝香港--本地	overseas.payment_inst== "ALIPAYHK" && overseas.currency == "HKD"
     */
    public static int filterOverseasPayway(Map transaction) {
        String paymentInst = BeanUtil.getPropString(transaction, Transaction.PAYMENT_INST);
        String currency = BeanUtil.getPropString(transaction, Transaction.CURRENCY);
        int payway = BeanUtil.getPropInt(transaction, Transaction.PAYWAY);
        if (payway == StatementObjectConfig.ALIPAY_OLD) {
            if (paymentInst.equals(StatementObjectConfig.ALIPAYCN) && currency.equals(StatementObjectConfig.CURRENCY_CNY)) {
                return StatementObjectConfig.ALIPAY_OLD;
            } else if (paymentInst.equals(StatementObjectConfig.ALIPAYHK) && currency.equals(StatementObjectConfig.CURRENCY_HKD)) {
                return StatementObjectConfig.ALIPAY_HK_LOCAL;
            }
        }
        return payway;
    }

    /**
     * 四舍五入保留多少位小数点
     *
     * @param money
     * @param scaleSize
     * @return
     */
    public static double formatMoney(Object money, int scaleSize) {
        if (money == null) {
            return 0.00;
        }
        return new BigDecimal(money.toString()).setScale(scaleSize, BigDecimal.ROUND_HALF_UP).doubleValue();
    }

    /**
     * 取得交易参数
     *
     * @param configSnapshot
     * @return
     */
    private static Map getTradeConfigFromConfigSnapshot(Map<String, Object> configSnapshot) {
        if (configSnapshot == null) {
            return null;
        }
        for (String key : configSnapshot.keySet()) {
            Object value = configSnapshot.get(key);
            if (value instanceof Map) {
                if (BeanUtil.getPropBoolean(value, TransactionParam.ACTIVE, false)) {
                    return (Map) value;
                }
            }
        }
        return null;
    }
}
