package com.wosai.upay.transaction.cal.process.util;

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import lombok.SneakyThrows;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by wujianwei on 2023/10/11.
 */
public class ExcelUtil {
    private final static Logger log = LoggerFactory.getLogger(ExcelUtil.class);

    /**
     * @param url         excel 文件的地址
     * @param sheetNo     从 0 开始
     * @param startLine   从 几行 开始 从0计数 包含此行
     * @param csvFilePath
     */
    public static void convertExcelToCsv(URL url, int sheetNo, int startLine, String csvFilePath) {
        try (InputStream inputStream = url.openStream();
             FileWriter csvWriter = new FileWriter(csvFilePath)) {
            ExcelTypeEnum excelType = url.getFile().contains(".xls") ? ExcelTypeEnum.XLS : ExcelTypeEnum.XLSX;
            ExcelReader excelReader = new ExcelReader(inputStream, excelType, new AnalysisEventListener<List<String>>() {
                private int currentLine = 0;

                @Override
                public void invoke(List<String> rowData, AnalysisContext context) {
                    currentLine++;
                    if (currentLine < startLine) {
                        return; // 跳过起始行之前的行
                    }
                    // 处理单元格数据（替换逗号）
                    List<String> processedRow = new ArrayList<>();
                    for (String cellValue : rowData) {
                        String processedValue = (cellValue != null) ? cellValue.replaceAll(",", "&") : "";
                        processedRow.add(processedValue);
                    }
                    try {
                        csvWriter.write(String.join(",", processedRow) + "," + "\n");
                    } catch (IOException e) {
                        throw new RuntimeException("CSV写入失败", e);
                    }
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext context) {
                    log.info("Excel文件已成功转换为CSV: {}", csvFilePath);
                }
            });
            ReadSheet readSheet = new ReadSheet(sheetNo);
            readSheet.setAutoTrim(false);
            excelReader.read(readSheet);
        } catch (IOException e) {
            throw new RuntimeException("文件处理失败", e);
        }
    }


    @SneakyThrows
    public static void main(String[] args) {
//        Thread.sleep(20000);
        convertExcelToCsv(new URL("https://images.wosaimg.com//portal/transaction/be732f9f-0b02-49b8-ae78-1b1f4646ffd6/%E6%B5%B7%E5%BA%95%E6%8D%9E_%E5%AF%B9%E8%B4%A6%E5%8D%95_2025-04-01_2025-04-02.xlsx?Expires=1743591640&OSSAccessKeyId=STS.NWcnqTztYhL3NxiLWArELEFiz&Signature=GmKfkCpbw7Hn%2F2pldAAsvN%2B62c0%3D&security-token=CAIS4QJ1q6Ft5B2yfSjIr5TWJcvgl6t4347YTF7YqFcUfspgqoPCmDz2IHFNfHJgCOkbv%2Fo%2FlWtT6fodloloU55fSAnfdME18o5e9xioaNOe55fovexV18zgQDHPUEYOcnoL1L%2BrIunGc9KBNnrm9EYqs5aYGBymW1u6S%2B7r7bdsctUQWCShcDNCH604DwB%2BqcgcRxCzXLTXRXyMuGfLC1dysQdRkH527b%2FFoveR8R3Dllb3uIR3zsbTWsH4P5QzY80nDojphbEtKvX7vXQOu0QQxsBfl7dZ%2FDrLhNaZDmRK7g%2BOW%2BiuqYw2dFQpN%2FFnSvAU%2FaOnyqEnoJbIjo6y0x9ceP9cTiDDAZyt29fUwxYNw2q4zNwRUlG6MbDnXvGd22tM6oKI7hrhbpFo%2FPMkvN7I5uVG7gsLfMTwI2bFBeHMm9odytFu9G7TtrnBXFPsaJ7SvEhVRNlSJi1HOXbkPVFZI8duGxqAAaQTumaMRUqx0wiGxPsSzzUBBVVJSE3f67oUML0RW7IvVOD%2FrIkvmDnbRvEceLxrJGvV8m8kDaHzaWtmjRVHDL3MFloiXVzRt4PUh1V%2BlQZXHdonV%2BkGmMledOrhsz1jkzrpC9Qeyz3AbjgNNnq%2BMmteJvKfg7y82gPQwMx78uUTIAA%3D"), 1, 5, "/Users/<USER>/Downloads/海底捞_对账单_40025-04-01_2025-04-02.csv");
//        Thread.sleep(20000);
    }
}
