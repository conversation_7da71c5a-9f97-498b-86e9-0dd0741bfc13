package com.wosai.upay.transaction.cal.process.model.domain;

import lombok.Data;

/**
 * 对账明细
 */
@Data
public class ReconciliationEntity {

    private String sourceMerchantId;

    private String sourceMerchantName;

    private String sqbMerchantId;

    private String sqbMerchantSn;
    /**
     * 上游有效交易笔数
     */
    private Integer sourceValidTradeNum;
    /**
     * 上游交易金额
     */
    private Long sourceValidTradeAmount;
    /**
     * 收钱吧有效交易笔数
     */
    private Integer sqbTradeNum;
    /**
     * 收钱吧有效交易金额
     */
    private Long sqbTradeMoney;
    /**
     * 上游账单应结算金额
     */
    private Long sourceSettlementAmount;
    /**
     * 收钱吧应结算金额
     */
    private Long sqbSettlementAmount;
    /**
     * 银联品牌服务费
     */
    private Long unionServiceAmount;
}
