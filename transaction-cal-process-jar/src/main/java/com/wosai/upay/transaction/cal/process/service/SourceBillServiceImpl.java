package com.wosai.upay.transaction.cal.process.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.gson.Gson;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonDataObjectNotExistsException;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.itsys.cornucopia.admin.domain.response.PolicyResponse;
import com.wosai.itsys.cornucopia.admin.domain.response.PropertyResponse;
import com.wosai.itsys.cornucopia.admin.service.IPolicyService;
import com.wosai.itsys.cornucopia.admin.service.PropertyRpcService;
import com.wosai.mpay.api.alipay.*;
import com.wosai.mpay.util.RsaSignature;
import com.wosai.mpay.util.WebUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.SafeSimpleDateFormat;
import com.wosai.upay.core.model.*;
import com.wosai.upay.core.model.log.TaskApplyLog;
import com.wosai.upay.core.service.*;
import com.wosai.upay.signature.service.SignService;
import com.wosai.upay.transaction.cal.process.constant.SourceBillInputTask;
import com.wosai.upay.transaction.cal.process.converter.SummaryBillConverter;
import com.wosai.upay.transaction.cal.process.helper.PageInfoHelper;
import com.wosai.upay.transaction.cal.process.mapper.*;
import com.wosai.upay.transaction.cal.process.model.ParserConfig;
import com.wosai.upay.transaction.cal.process.model.domain.*;
import com.wosai.upay.transaction.cal.process.model.dto.*;
import com.wosai.upay.transaction.cal.process.model.request.BillListExportRequest;
import com.wosai.upay.transaction.cal.process.model.request.SourceBillPageRequest;
import com.wosai.upay.transaction.cal.process.model.response.SummaryBillResponse;
import com.wosai.upay.transaction.cal.process.processor.SourceBillTaskProcessor;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import com.wosai.upay.transaction.cal.process.util.OssUtils;
import com.wosai.upay.task.center.model.Order;
import com.wosai.web.api.ListResult;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import okhttp3.HttpUrl;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVPrinter;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateFormatUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.Collections;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * SourceBillServiceImpl
 *
 * <AUTHOR>
 * @date 2019-09-11 14:38
 */
@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class SourceBillServiceImpl implements SourceBillService {

    @Value("${file.path.bill}")
    private String filePathBill;

    @Autowired
    private TradeConfigService tradeConfigService;

    @Autowired
    private SignService signService;

    @Autowired
    private StoreService storeService;

    @Autowired
    private SupportService supportService;

    @Autowired
    private TbSummaryBillMapper tbSummaryBillMapper;

    @Autowired
    private IPolicyService iPolicyService;

    @Autowired
    private PropertyRpcService propertyRpcService;

    @Autowired
    private SummaryBillService summaryBillService;

    /**
     * 上游账单处理任务线程池
     */
    private static final AtomicInteger SOURCE_BILL_TASK_EXECUTOR_THREAD_NUMBER = new AtomicInteger(1);
    private static final ExecutorService SOURCE_BILL_TASK_EXECUTOR = new ThreadPoolExecutor(2, 10, 60, TimeUnit.SECONDS,
            new LinkedBlockingDeque<>(100), r -> new Thread(r, "source-bill-task-" + SOURCE_BILL_TASK_EXECUTOR_THREAD_NUMBER.getAndIncrement()));

    private Gson gson;

    private final MerchantService merchantService;
    private final LogService logService;

    private final TbSourceBillInputTaskMapper tbSourceBillInputTaskMapper;
    private final TbSourceBillTypeMapper tbSourceBillTypeMapper;
    private final TbBillOutputMapper tbBillOutputMapper;
    private final TbBillOutputV2Mapper tbBillOutputV2Mapper;
    private final TbSourceBillMapper tbSourceBillMapper;

    private final SourceBillTaskProcessor sourceBillTaskProcessor;
    private final OssUtils ossUtils;

    public SourceBillServiceImpl(Gson gson,
                                 MerchantService merchantService,
                                 LogService logService,
                                 TbSourceBillInputTaskMapper tbSourceBillInputTaskMapper,
                                 TbSourceBillTypeMapper tbSourceBillTypeMapper,
                                 TbBillOutputMapper tbBillOutputMapper,
                                 TbSourceBillMapper tbSourceBillMapper,
                                 SourceBillTaskProcessor sourceBillTaskProcessor, OssUtils ossUtils, TbBillOutputV2Mapper tbBillOutputV2Mapper) {
        this.gson = gson;
        this.merchantService = merchantService;
        this.logService = logService;
        this.tbSourceBillInputTaskMapper = tbSourceBillInputTaskMapper;
        this.tbSourceBillTypeMapper = tbSourceBillTypeMapper;
        this.tbBillOutputMapper = tbBillOutputMapper;
        this.tbSourceBillMapper = tbSourceBillMapper;
        this.sourceBillTaskProcessor = sourceBillTaskProcessor;
        this.ossUtils = ossUtils;
        this.tbBillOutputV2Mapper = tbBillOutputV2Mapper;
    }

    @Override
    public List<TbSourceBillTypeDto> findSourceBillTypeList(TbSourceBillTypeDto param) {
        if (param == null) {
            param = new TbSourceBillTypeDto();
        }
        param.setDeleted(false); // 仅查询未删除的数据

        TbSourceBillType selectCondition = new TbSourceBillType();
        BeanUtils.copyProperties(param, selectCondition);
        List<TbSourceBillType> tbSourceBillTypeList = this.tbSourceBillTypeMapper.selectBySelective(selectCondition);

        List<TbSourceBillTypeDto> tbSourceBillTypeDtoList = new ArrayList<>(tbSourceBillTypeList.size());
        tbSourceBillTypeList.forEach(tbSourceBillType -> {
            TbSourceBillTypeDto entity = new TbSourceBillTypeDto();
            BeanUtils.copyProperties(tbSourceBillType, entity);
            if (tbSourceBillType.getConfig() != null) {
                entity.setConfig(new String(tbSourceBillType.getConfig()));
            }
            tbSourceBillTypeDtoList.add(entity);
        });

        return tbSourceBillTypeDtoList;
    }

    //    @Transactional(rollbackFor = Exception.class)
    @Override
    public TbSourceBillInputTaskDto importSourceBill(TbSourceBillInputTaskDto param) {

        // 判断上游账单是否有效
        TbSourceBillType tbSourceBillType = this.checkSourceBillType(param.getSourceBillType());

        TbSourceBillInputTaskWithBLOBs insertParam = new TbSourceBillInputTaskWithBLOBs();
        insertParam.setSourceBillType(param.getSourceBillType());
        insertParam.setSourceBillStartDate(param.getSourceBillStartDate());
        insertParam.setSourceBillEndDate(param.getSourceBillEndDate());
        try {
            String fileName = ossUtils.trimUrl(param.getFileUrl());
            insertParam.setFileUrl(URLDecoder.decode(fileName, "UTF-8"));
        } catch (UnsupportedEncodingException e) {
            throw new CommonInvalidParameterException("文件URL编码格式有误");
        }
        insertParam.setCreateBy(param.getCreateBy());
        if (param.getRemark() != null) {
            insertParam.setRemark(param.getRemark().getBytes());
        }
        insertParam.setTaskStatus(SourceBillInputTask.STATUS_NEW_TASK); // 默认新建状态
        insertParam.setConfig(tbSourceBillType.getConfig()); //任务创建时的config
        this.tbSourceBillInputTaskMapper.insertSelective(insertParam);

        param.setId(insertParam.getId());
        param.setTaskStatus(insertParam.getTaskStatus());

        SOURCE_BILL_TASK_EXECUTOR.execute(() -> sourceBillTaskProcessor.process(insertParam));

        return param;
    }

    @Override
    public boolean importSourceBillV2(TbSourceBillInfoDTO param) {
        TbSourceBillType tbSourceBillType = null;
        
        // 当政策不为空时，获取原始账单类型
        if (StringUtils.isNotEmpty(param.getPolicyId())) {
            TbSourceBillTypeExample tbSourceBillTypeExample = new TbSourceBillTypeExample();
            tbSourceBillTypeExample.createCriteria().andPolicyIdEqualTo(param.getPolicyId());

            List<TbSourceBillType> tbSourceBillTypeList = this.tbSourceBillTypeMapper.selectByExample(tbSourceBillTypeExample);
            if (tbSourceBillTypeList.size() == 0 || tbSourceBillTypeList.get(0).getDeleted()) {
                tbSourceBillType = this.tbSourceBillTypeMapper.selectByPrimaryKey(99);
            } else {
                tbSourceBillType = tbSourceBillTypeList.get(0);
            }
        }

        TbSourceBillInputTaskWithBLOBs insertParam = new TbSourceBillInputTaskWithBLOBs();
        insertParam.setSourceBillStartDate(new Date());
        insertParam.setSourceBillEndDate(new Date());
        insertParam.setTaskStatus(SourceBillInputTask.STATUS_NEW_TASK); // 默认新建状态

        if (tbSourceBillType != null) {
            insertParam.setSourceBillType(tbSourceBillType.getId());
            insertParam.setConfig(tbSourceBillType.getConfig()); //任务创建时的config
        }
        this.tbSourceBillInputTaskMapper.insertSelective(insertParam);
        param.setInnerTaskId(insertParam.getId());

        TbSourceBillType finalTbSourceBillType = tbSourceBillType;
        sourceBillTaskProcessor.process(param, finalTbSourceBillType);
        return true;
    }

    private TbSourceBillType checkSourceBillType(int sourceBillTypeId) {
        TbSourceBillType tbSourceBillType = this.tbSourceBillTypeMapper.selectByPrimaryKey(sourceBillTypeId);
        if (tbSourceBillType == null) {
            throw new CommonInvalidParameterException("该账单类型不存在");
        }
        if (tbSourceBillType.getDeleted()) {
            throw new CommonInvalidParameterException("该账单类型已被删除");
        }
        return tbSourceBillType;
    }


    /**
     * 将上游账单任务添加到任务线程池
     *
     * @param task 上游账单任务
     */
    public void addSourceBillTaskToPool(TbSourceBillInputTask task) {
        if (task == null) {
            return;
        }

        SOURCE_BILL_TASK_EXECUTOR.execute(() -> sourceBillTaskProcessor.process(task));
    }

    @Transactional(rollbackFor = Exception.class)
    @Override
    public TbSourceBillInputTaskDto importSourceBillConfirm(@NotNull(message = "确认导入上游账单参数不能为空") @Valid TbSourceBillInputTaskDto param) {
        // 判断任务是否存在
        Integer taskId = param.getId();
        TbSourceBillInputTaskWithBLOBs task = this.tbSourceBillInputTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new CommonDataObjectNotExistsException("不存在该数据");
        }
        if (task.getTaskStatus() != SourceBillInputTask.TO_BE_CONFIRMED) {
            throw new CommonInvalidParameterException("非待导入确认状态的任务禁止确认导入");
        }

//        // 更新上游对账单列表为导入状态
//        TbSourceBill tbSourceBillRecord = new TbSourceBill();
//        tbSourceBillRecord.setIsImport(true);
//        TbSourceBillExample tbSourceBillExample = new TbSourceBillExample();
//        tbSourceBillExample.or().andSourceBillInputTaskIdEqualTo(taskId);
//        this.tbSourceBillMapper.updateByExampleSelective(tbSourceBillRecord, tbSourceBillExample);
//
//        // 更新上游对账单输出列表为导入状态
//        TbBillOutput tbBillOutputRecord = new TbBillOutput();
//        tbBillOutputRecord.setIsImport(true);
//        TbBillOutputExample tbBillOutputExample = new TbBillOutputExample();
//        tbBillOutputExample.or().andSourceBillInputTaskIdEqualTo(taskId);
//        this.tbBillOutputMapper.updateByExampleSelective(tbBillOutputRecord, tbBillOutputExample);

        // 更新任务状态
        TbSourceBillInputTaskWithBLOBs updateParam = new TbSourceBillInputTaskWithBLOBs();
        updateParam.setId(param.getId());
        updateParam.setTaskStatus(SourceBillInputTask.SUCCESS);
        updateParam.setConfirmBy(param.getConfirmBy());
        this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);

        param.setTaskStatus(SourceBillInputTask.SUCCESS);
        return param;
    }

    //    @Transactional(rollbackFor = Exception.class,timeout = 360000)
    @Override
    public TbSourceBillInputTaskDto importSourceBillCancel(@NotNull(message = "取消导入上游账单参数不能为空") @Valid TbSourceBillInputTaskDto param) {
        Integer taskId = param.getId();
        TbSourceBillInputTaskWithBLOBs task = this.tbSourceBillInputTaskMapper.selectByPrimaryKey(taskId);
        if (task == null) {
            throw new CommonDataObjectNotExistsException("不存在该数据");
        }
        if (task.getTaskStatus() != SourceBillInputTask.TO_BE_CONFIRMED && task.getTaskStatus() != SourceBillInputTask.FAILURE) {
            throw new CommonInvalidParameterException("非待导入确认和处理失败状态的任务禁止取消导入");
        }

        // 删除上游账单明细
        TbSourceBillExample tbSourceBillExample = new TbSourceBillExample();
        tbSourceBillExample.or().andSourceBillInputTaskIdEqualTo(taskId);
        while (this.tbSourceBillMapper.deleteByExample(tbSourceBillExample) > 0) {
            this.tbSourceBillMapper.deleteByExample(tbSourceBillExample);
        }


        // 删除匹配的账单
        TbBillOutputExample tbBillOutputExample = new TbBillOutputExample();
        tbBillOutputExample.or().andSourceBillInputTaskIdEqualTo(taskId);
        while (tbBillOutputMapper.deleteByExample(tbBillOutputExample) > 0) {
            tbBillOutputMapper.deleteByExample(tbBillOutputExample);
        }

        // 更新任务状态
        TbSourceBillInputTaskWithBLOBs updateParam = new TbSourceBillInputTaskWithBLOBs();
        updateParam.setId(param.getId());
        updateParam.setTaskStatus(SourceBillInputTask.CANCELLED);
        updateParam.setConfirmBy(param.getConfirmBy());
        this.tbSourceBillInputTaskMapper.updateByPrimaryKeySelective(updateParam);

        param.setTaskStatus(SourceBillInputTask.CANCELLED);
        return param;
    }

    @Override
    public TbSourceBillInputTaskCustomDto findSourceBillInputTaskDetail(Integer id) {
        TbSourceBillInputTaskCustomDto resultDto = new TbSourceBillInputTaskCustomDto();

        // 账单任务的基本信息
        TbSourceBillInputTaskWithBLOBs taskWithBLOBs = this.tbSourceBillInputTaskMapper.selectByPrimaryKey(id);
        if (taskWithBLOBs == null) {
            throw new CommonDataObjectNotExistsException("查询的数据不存在");
        }
        BeanUtils.copyProperties(taskWithBLOBs, resultDto);
        if (taskWithBLOBs.getRemark() != null) {
            resultDto.setRemark(new String(taskWithBLOBs.getRemark()));
        }
        if (taskWithBLOBs.getErrorInfo() != null) {
            resultDto.setErrorInfo(new String(taskWithBLOBs.getErrorInfo()));
        }

        // 账单交易信息
        if (taskWithBLOBs.getExtra() != null) {
            TradeInfoDto tradeInfoDto = gson.fromJson(new String(taskWithBLOBs.getExtra()), TradeInfoDto.class);
            resultDto.setTradeInfo(tradeInfoDto);
        } else {
            resultDto.setTradeInfo(new TradeInfoDto()); // 方便前端取值
        }

        // 账单类型信息
        TbSourceBillType tbSourceBillType = this.tbSourceBillTypeMapper.selectByPrimaryKey(taskWithBLOBs.getSourceBillType());
        TbSourceBillTypeDto tbSourceBillTypeDto = new TbSourceBillTypeDto();
        BeanUtils.copyProperties(tbSourceBillType, tbSourceBillTypeDto);
        resultDto.setTbSourceBillType(tbSourceBillTypeDto);

        if (StringUtils.isNotEmpty(resultDto.getFileUrl()) && !resultDto.getFileUrl().startsWith("http")) {
            resultDto.setFileUrl(ossUtils.restoreUrl(resultDto.getFileUrl()));
        }
        String fileName = Optional.ofNullable(resultDto.getTradeInfo()).map(TradeInfoDto::getReconciliationFile).orElse("");
        if (StringUtils.isNotEmpty(fileName) && !fileName.startsWith("http")){
            resultDto.getTradeInfo().setReconciliationFile(ossUtils.restoreUrl(fileName));
        }
        return resultDto;
    }

    @Override
    public ListResult<TbSourceBillInputTaskCustomDto> findSourceBillInputTaskList(PageInfo pageInfo,
                                                                                  Integer sourceBillType,
                                                                                  Integer sourceBillCompany,
                                                                                  Integer sourceBillClassify,
                                                                                  Integer taskStatus,
                                                                                  Date createAtStart,
                                                                                  Date createAtEnd,
                                                                                  String tradeDateMonth) {
        pageInfo = PageInfoHelper.checkAndProcess(pageInfo);

        Page page = PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize(), "t0.create_at DESC");
        List<TbSourceBillInputTaskWithType> taskWithTypeList = this.tbSourceBillInputTaskMapper.selectJoinBillTypeList(sourceBillType, sourceBillCompany, sourceBillClassify, taskStatus, createAtStart, createAtEnd, tradeDateMonth);

        List<TbSourceBillInputTaskCustomDto> dtoList = new ArrayList<>(taskWithTypeList.size());
        taskWithTypeList.forEach(taskWithType -> {
            TbSourceBillInputTaskCustomDto taskEntity = new TbSourceBillInputTaskCustomDto();
            BeanUtils.copyProperties(taskWithType, taskEntity);

            // 账单类型
            if (taskWithType.getTbSourceBillType() != null) {
                TbSourceBillTypeDto typeEntity = new TbSourceBillTypeDto();
                BeanUtils.copyProperties(taskWithType.getTbSourceBillType(), typeEntity);
                taskEntity.setTbSourceBillType(typeEntity);
            }

            // 账单交易信息
            if (taskWithType.getExtra() != null && taskWithType.getExtra().length > 0) {
                taskEntity.setTradeInfo(gson.fromJson(new String(taskWithType.getExtra()), TradeInfoDto.class));
            } else {
                taskEntity.setTradeInfo(new TradeInfoDto()); // 方便前端取值
            }

            if (StringUtils.isNotEmpty(taskEntity.getFileUrl()) && !taskEntity.getFileUrl().startsWith("http")){
                taskEntity.setFileUrl(ossUtils.restoreUrl(taskEntity.getFileUrl()));
            }
            String fileName = Optional.ofNullable(taskEntity.getTradeInfo()).map(TradeInfoDto::getReconciliationFile).orElse("");
            if (StringUtils.isNotEmpty(fileName) && !fileName.startsWith("http")){
                taskEntity.getTradeInfo().setReconciliationFile(ossUtils.restoreUrl(fileName));
            }
            dtoList.add(taskEntity);
        });

        ListResult<TbSourceBillInputTaskCustomDto> listResult = new ListResult<>(dtoList);
        listResult.setTotal(page.getTotal());
        return listResult;
    }

    @Override
    public TbBillOutputCustomDto findBillOutputDetail(Long id) {
        // 账单主体
        TbBillOutput tbBillOutput = this.tbBillOutputMapper.selectByPrimaryKey(id);
        if (tbBillOutput == null) {
            throw new CommonDataObjectNotExistsException("不存在该上游账单");
        }
        TbBillOutputCustomDto resultDto = new TbBillOutputCustomDto();
        BeanUtils.copyProperties(tbBillOutput, resultDto);

        // 账单类型
        TbSourceBillType tbSourceBillType = this.tbSourceBillTypeMapper.selectByPrimaryKey(tbBillOutput.getSourceBillType());
        if (tbSourceBillType != null) {
            TbSourceBillTypeDto tbSourceBillTypeDto = new TbSourceBillTypeDto();
            BeanUtils.copyProperties(tbSourceBillType, tbSourceBillTypeDto);
            resultDto.setTbSourceBillType(tbSourceBillTypeDto);
        }

        // 收钱吧商户名称
        String sqbMerchantId = resultDto.getSqbMerchantId();
        if (StringUtils.isNotEmpty(sqbMerchantId)) {
            Map sqbMerchantInfo = this.merchantService.getMerchant(sqbMerchantId);
            if (MapUtils.isNotEmpty(sqbMerchantInfo)) {
                resultDto.setSqbMerchantName(MapUtils.getString(sqbMerchantInfo, Merchant.NAME));
            }
        }

        if (StringUtils.isNotEmpty(resultDto.getSummaryBillSn())) {
            TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
            tbSummaryBillExample.createCriteria().andSnEqualTo(resultDto.getSummaryBillSn());
            SummaryBillResponse summaryBillResponse = summaryBillService.querySummaryBillDetail(resultDto.getSummaryBillSn());
            if (summaryBillResponse != null) {
                resultDto.setPolicyName(summaryBillResponse.getPolicyName());
                resultDto.setCompanyName(summaryBillResponse.getCompanyName());
                resultDto.setNcName(summaryBillResponse.getNcName());
                resultDto.setTradeMonth(summaryBillResponse.getTradeMonth());
            }

        }

        return resultDto;
    }

    @Override
    public TbBillOutputCustomDto findBillOutputDetailV2(Long id) {
        // 账单主体
        TbBillOutputV2 tbBillOutput = this.tbBillOutputV2Mapper.selectByPrimaryKey(id);
        if (tbBillOutput == null) {
            throw new CommonDataObjectNotExistsException("不存在该上游账单");
        }
        TbBillOutputCustomDto resultDto = new TbBillOutputCustomDto();
        BeanUtils.copyProperties(tbBillOutput, resultDto);
        byte[] extraParamsBytes = tbBillOutput.getExtraParams();
        if (extraParamsBytes != null) {
            String extraParamsStr = new String(extraParamsBytes, StandardCharsets.UTF_8);
            resultDto.setExtraParams(extraParamsStr);
        }
        resultDto.setIsImport(tbBillOutput.getSourceSettlementAmount() > 0);

        // 账单类型
        TbSourceBillType tbSourceBillType = this.tbSourceBillTypeMapper.selectByPrimaryKey(tbBillOutput.getSourceBillType());
        if (tbSourceBillType != null) {
            TbSourceBillTypeDto tbSourceBillTypeDto = new TbSourceBillTypeDto();
            BeanUtils.copyProperties(tbSourceBillType, tbSourceBillTypeDto);
            resultDto.setTbSourceBillType(tbSourceBillTypeDto);
        }

        // 收钱吧商户名称
        String sqbMerchantId = resultDto.getSqbMerchantId();
        Map sqbMerchantInfo = null;
        if (StringUtils.isNotEmpty(sqbMerchantId)) {
            sqbMerchantInfo = this.merchantService.getMerchant(sqbMerchantId);
        } else if(StringUtils.isNotEmpty(resultDto.getSqbMerchantSn())) {
            sqbMerchantInfo = this.merchantService.getMerchantByMerchantSn(sqbMerchantId);
        }
        if (MapUtils.isNotEmpty(sqbMerchantInfo)) {
            resultDto.setSqbMerchantName(MapUtils.getString(sqbMerchantInfo, Merchant.NAME));
        }

        if (StringUtils.isNotEmpty(resultDto.getSummaryBillSn())) {
            TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
            tbSummaryBillExample.createCriteria().andSnEqualTo(resultDto.getSummaryBillSn());
            SummaryBillResponse summaryBillResponse = summaryBillService.querySummaryBillDetail(resultDto.getSummaryBillSn());
            if (summaryBillResponse != null) {
                resultDto.setPolicyName(summaryBillResponse.getPolicyName());
                resultDto.setCompanyName(summaryBillResponse.getCompanyName());
                resultDto.setNcName(summaryBillResponse.getNcName());
                resultDto.setTradeMonth(summaryBillResponse.getTradeMonth());
            }

        }

        return resultDto;
    }

    @Override
    public ListResult<BillOutput> findBillOutput(Integer billType, Integer billSourceCompany, Integer billSourceClassify,
                                                 String startDate, String endDate,
                                                 String sourceMerchantId, String merchantName, String sqbMerchantSn,
                                                 String serviceProviderId, Integer pn, Integer ps) {

        Date start = null;
        Date end = null;
        try {
            if (!StringUtils.isBlank(startDate)) {
                start = DateUtils.parseDate(startDate, "yyyy-MM");
            }

            if (!StringUtils.isBlank(endDate)) {
                end = DateUtils.parseDate(endDate, "yyyy-MM");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(end);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                end = calendar.getTime();
            }
        } catch (Exception e) {
            log.error("date format failed", e);
            throw new CommonInvalidParameterException("时间日期格式不正确");
        }

        Date finalStart = start;
        Date finalEnd = end;
        Page<BillOutput> page = PageHelper.startPage(pn, ps, "o.id DESC").setCount(false).doSelectPage(() ->
                tbBillOutputMapper.findBillOutputList(billType, billSourceCompany, billSourceClassify, finalStart, finalEnd,
                        sourceMerchantId, merchantName, sqbMerchantSn, serviceProviderId,null));

        ListResult<BillOutput> list = new ListResult<>();
        page.setTotal(99999999L);
        list.setTotal(page.getTotal());

        List<BillOutput> result = page.getResult().stream().peek(billOutput -> {
            Date tradeDateMonth = billOutput.getTradeDateMonth();
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(tradeDateMonth);
            calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
            billOutput.setStartDate(DateFormatUtils.format(tradeDateMonth, "yyyy-MM-dd"));
            billOutput.setEndDate(DateFormatUtils.format(calendar.getTime(), "yyyy-MM-dd"));

        }).collect(Collectors.toList());

        list.setRecords(result);

        return list;
    }

    @Override
    public ListResult<BillOutputV2> findBillOutputV2(SourceBillPageRequest sourceBillPageRequest) {
        Date start = null;
        Date end = null;
        SourceBillPageRequest.SourceBillPageQuery query = sourceBillPageRequest.getQuery();
        try {
            if (!StringUtils.isBlank(query.getTradeStartMonth())) {
                start = DateUtils.parseDate(query.getTradeStartMonth(), "yyyyMM");
            }

            if (!StringUtils.isBlank(query.getTradeEndMonth())) {
                end = DateUtils.parseDate(query.getTradeEndMonth(), "yyyyMM");
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(end);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));
                end = calendar.getTime();
            }
        } catch (Exception e) {
            log.error("date format failed", e);
            throw new CommonInvalidParameterException("时间日期格式不正确");
        }
        
        // 构建查询条件
        TbSummaryBillExample tbSummaryBillExample = new TbSummaryBillExample();
        TbSummaryBillExample.Criteria summaryBillCriteria = tbSummaryBillExample.createCriteria();
        
        // 如果有summaryBillSn，优先使用summaryBillSn查询
        if (!StringUtils.isEmpty(query.getSummaryBillSn())) {
            summaryBillCriteria.andSnEqualTo(query.getSummaryBillSn());
        } else {
            // 如果summaryBillSn为空，至少需要propertyId或policyId之一
            if (StringUtils.isEmpty(query.getPropertyId()) && StringUtils.isEmpty(query.getPolicyId())) {
                throw new CommonInvalidParameterException("summaryBillSn、propertyId、policyId不能同时为空");
            }
        }
        
        if (query.getStatus() != null) {
            summaryBillCriteria.andStatusEqualTo(query.getStatus());
        }
        if (query.getPolicyId() != null) {
            summaryBillCriteria.andPolicyIdEqualTo(query.getPolicyId());
        }
        if (query.getPropertyId() != null) {
            summaryBillCriteria.andPropertyIdEqualTo(query.getPropertyId());
        }
        
        List<TbSummaryBill> tbSummaryBills = tbSummaryBillMapper.selectByExample(tbSummaryBillExample);
        if (CollectionUtils.isEmpty(tbSummaryBills)) {
            return new ListResult<>();
        }

        Date finalStart = start;
        Date finalEnd = end;
        
        // 收集所有summaryBill的sn
        List<String> summaryBillSnList = tbSummaryBills.stream().map(TbSummaryBill::getSn).collect(Collectors.toList());
        
        TbBillOutputV2Example tbBillOutputExample = new TbBillOutputV2Example();
        TbBillOutputV2Example.Criteria criteria = tbBillOutputExample.createCriteria().andSummaryBillSnIn(summaryBillSnList);
        if (finalStart != null) {
            criteria.andTradeDateMonthGreaterThanOrEqualTo(finalStart);
        }
        if (finalEnd != null) {
            criteria.andTradeDateMonthLessThanOrEqualTo(finalEnd);
        }
        if (query.getSourceMerchantId() != null) {
            criteria.andSourceMerchantIdEqualTo(query.getSourceMerchantId());
        }
        if (query.getSqbMerchantSn() != null) {
            criteria.andSqbMerchantSnEqualTo(query.getSqbMerchantSn());
        }
        if (query.getServiceProviderId() != null) {
            criteria.andServiceProviderIdEqualTo(query.getServiceProviderId());
        }
        if (query.getDeviceNo() != null) {
            criteria.andDeviceNoEqualTo(query.getDeviceNo());
        }
        if (query.getSourceLevel2MerchantId() != null) {
            criteria.andSourceLevel2MerchantIdEqualTo(query.getSourceLevel2MerchantId());
        }
        if (query.getSourceLevel2MerchantName() != null) {
            criteria.andSourceLevel2MerchantNameEqualTo(query.getSourceLevel2MerchantName());
        }
        if (query.getTemplateCode() != null) {
            criteria.andImportTaskSnLike("%" + query.getTemplateCode() + "%");
        }

        Page<TbBillOutputV2> page = PageHelper.startPage(sourceBillPageRequest.getPageNo(), sourceBillPageRequest.getPageSize(), "id DESC").doSelectPage(() ->
                tbBillOutputV2Mapper.selectByExample(tbBillOutputExample)
        );

        ListResult<BillOutputV2> list = new ListResult<>();
        list.setTotal(page.getTotal());
        
        // 创建summaryBill的映射，便于根据sn查找对应的名称和状态
        Map<String, TbSummaryBill> summaryBillMap = tbSummaryBills.stream()
                .collect(Collectors.toMap(TbSummaryBill::getSn, Function.identity()));
        
        List<BillOutputV2> billOutputV2List = page.getResult().stream().map(billOutput -> {
            TbSummaryBill correspondingSummaryBill = summaryBillMap.get(billOutput.getSummaryBillSn());
            return SummaryBillConverter.INSTANCE.doToBillOutputV2(billOutput, 
                    correspondingSummaryBill != null ? correspondingSummaryBill.getName() : "", 
                    correspondingSummaryBill != null ? correspondingSummaryBill.getStatus() : null);
        }).collect(Collectors.toList());
        List<PolicyResponse> policyResponseList = iPolicyService.queryPolicyByIdList(tbSummaryBills.stream()
                .map(TbSummaryBill::getPolicyId)
                .filter(Objects::nonNull)
                .map(Long::valueOf).collect(Collectors.toList()));
        if (CollectionUtils.isNotEmpty(policyResponseList)) {
            billOutputV2List.forEach(billOutputV2 -> {
                billOutputV2.setPolicyName(policyResponseList.get(0).getName());
                billOutputV2.setPolicyId(policyResponseList.get(0).getId().toString());
            });
        }

        // Get property details for each summaryBill
        Set<String> propertyIds = tbSummaryBills.stream()
                .map(TbSummaryBill::getPropertyId)
                .filter(Objects::nonNull)
                .collect(Collectors.toSet());
        
        Map<String, PropertyResponse> propertyMap = new HashMap<>();
        if (!propertyIds.isEmpty()) {
            List<Long> propertyIdList = propertyIds.stream().map(Long::valueOf).collect(Collectors.toList());
            List<PropertyResponse> propertyResponses = propertyRpcService.queryByIdList(propertyIdList);
            if (propertyResponses != null) {
                propertyMap = propertyResponses.stream()
                        .collect(Collectors.toMap(p -> p.getId().toString(), Function.identity()));
            }
        }
        
        // 为每个billOutput设置对应的property信息
        Map<String, PropertyResponse> finalPropertyMap = propertyMap;
        billOutputV2List.forEach(billOutputV2 -> {
            TbSummaryBill correspondingSummaryBill = summaryBillMap.get(billOutputV2.getSummaryBillSn());
            if (correspondingSummaryBill != null && correspondingSummaryBill.getPropertyId() != null) {
                PropertyResponse propertyResponse = finalPropertyMap.get(correspondingSummaryBill.getPropertyId());
                if (propertyResponse != null) {
                    billOutputV2.setPropertyId(propertyResponse.getId().toString());
                    billOutputV2.setPropertyName(propertyResponse.getName());
                }
            }
        });

        list.setRecords(billOutputV2List);

        return list;
    }

    private static final AtomicInteger EXPORT_BILL_LIST_EXECUTOR_THREAD_NUMBER = new AtomicInteger(1);
    private static final ExecutorService EXPORT_BILL_LIST_EXECUTOR = new ThreadPoolExecutor(1, 1, 30, TimeUnit.MINUTES,
            new LinkedBlockingDeque<>(200), r -> new Thread(r, "export-bill-" + EXPORT_BILL_LIST_EXECUTOR_THREAD_NUMBER.getAndIncrement()));

    @Override
    public Map<String, Object> exportBillList(BillListExportRequest request) {
        // 创建任务
        Map logParam = new HashMap(6);
        logParam.put(TaskApplyLog.TYPE, 423);
        logParam.put(TaskApplyLog.APPLY_SYSTEM, 2);
        logParam.put(TaskApplyLog.APPLY_DATE, new java.sql.Date(System.currentTimeMillis()).toString());
        logParam.put(TaskApplyLog.USER_ID, request.getAccount_name());
        Map taskApplyLog = this.logService.createTaskApplyLog(logParam);

        String taskId;
        if (taskApplyLog == null || StringUtils.isEmpty(taskId = MapUtils.getString(taskApplyLog, DaoConstants.ID))) {
            throw new CommonDataObjectNotExistsException("任务提交失败");
        }

        // 异步处理任务
        EXPORT_BILL_LIST_EXECUTOR.execute(() -> asyncDoExportBillList(request, taskId));

        // 返回结果
        return MapUtil.hashMap("taskId", taskId,
                "message", "请求已成功提交，处理结果稍后在任务处理中心查看");
    }

    private static final String[] FILE_HEADER = {"账单开始时间", "账单结束时间",
            "上游商户号", "上游商户名称", "外部门店号", "外部门店名称", "收钱吧商户号",
            "维度（对于支付宝花呗是分期期数）", "账单类型",
            "上游账单有效支付笔数(拆分后)", "上游账单有效支付金额(拆分后)(单位:元)", "上游账单有效退款笔数(拆分后)", "上游账单有效退款金额(拆分后)(单位:元)",
            "结算依据类型", "上游账单结算依据(拆分后)", "结算费率", "上游账单应结算总金额(拆分后)(单位:元)", "收钱吧有效支付笔数",
            "收钱吧有效支付金额(拆分后)(单位:元)", "收钱吧有效退款笔数", "收钱吧有效退款金额(拆分后)(单位:元)", "收钱吧有效交易笔数",
            "收钱吧有效交易金额(拆分后)(单位:元)", "收钱吧有效交易金额占比", "上游渠道号", "备注"};

    private static final DateTimeFormatter DATE_TIME_FORMATTER_YYYY_MM_DD = DateTimeFormatter.ofPattern("yyyy-MM-dd");

    /**
     * 异步处理账单导出
     */
    private void asyncDoExportBillList(BillListExportRequest request, String taskId) {
        Map<String, Object> result = new HashMap<>(2);

        int applyStatus = 2;

        String fileFullPath = filePathBill + taskId + ".csv";

        // 账单日期格式化
        Date startDate = null;
        if (request.getSourceBillStartDate() != null) {
            startDate = new Date(LocalDate.parse(request.getSourceBillStartDate() + "-01", DATE_TIME_FORMATTER_YYYY_MM_DD).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }
        Date endDate = null;
        if (request.getSourceBillEndDate() != null) {
            endDate = new Date(LocalDate.parse(request.getSourceBillStartDate() + "-31", DATE_TIME_FORMATTER_YYYY_MM_DD).atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli());
        }

        log.info("asyncDoExportBillList: 开始查询数据");
        // 查询数据集
        List<BillOutput> billOutputList = new ArrayList<>();

        Long lastId = null;
        Date finalStartDate = startDate;
        Date finalEndDate = endDate;
        List<BillOutput> tempList = new ArrayList<>();
        do {
            Long finalLastId = lastId;
            tempList = PageHelper.startPage(1, 1000, "o.id desc").setCount(false).doSelectPage(() ->
                    tbBillOutputMapper.findBillOutputList(request.getSourceBillType(),
                            request.getSourceBillCompany(), request.getSourceBillClassify(),
                            finalStartDate, finalEndDate,
                            request.getSourceMerchantId(), request.getSourceMerchantName(),
                            request.getSqbMerchantNo(), request.getServiceProviderId(), finalLastId));
            if (tempList != null && tempList.size() > 0) {
                lastId = Long.valueOf(tempList.get(tempList.size() - 1).getId());
                log.info("fetch lastId={}, size={}", lastId, tempList.size());
                billOutputList.addAll(tempList);
            }
        } while (tempList != null && tempList.size() != 0);


        if (CollectionUtils.isEmpty(billOutputList)) {
            result.put("message", "导出的数据为空");
            this.logService.updateTaskApplyLog(MapUtil.hashMap(DaoConstants.ID, taskId, TaskApplyLog.APPLY_STATUS, applyStatus, TaskApplyLog.APPLY_RESULT, gson.toJson(result)));
            return;
        }

        log.info("asyncDoExportBillList: 开始写入csv");
        // 写入 CSV
        CSVFormat csvFormat = CSVFormat.DEFAULT.withHeader(FILE_HEADER);

        // 这是写入CSV的代码
        try (Writer writer = new FileWriter(fileFullPath); CSVPrinter printer = new CSVPrinter(writer, csvFormat)) {
            billOutputList.forEach(billOutput -> {
                log.info("writer csv: {}", billOutput);
                List<String> record = new ArrayList<>();
                Date tradeDateMonth = billOutput.getTradeDateMonth();
                Calendar calendar = Calendar.getInstance();
                calendar.setTime(tradeDateMonth);
                calendar.set(Calendar.DAY_OF_MONTH, calendar.getActualMaximum(Calendar.DAY_OF_MONTH));

                record.add(DateFormatUtils.format(tradeDateMonth, "yyyy-MM-dd")); // 账单开始时间
                record.add(DateFormatUtils.format(calendar.getTime(), "yyyy-MM-dd")); // 账单结束时间
                record.add(billOutput.getSourceMerchantId()); // 上游商户号
                record.add(billOutput.getSourceMerchantName()); // 上游商户名称
                record.add(billOutput.getSourceLevel2MerchantId()); // 外部门店号(上游商户二级商户/门店号)
                record.add(billOutput.getSourceLevel2MerchantName()); // 外部门店名称 (上游商户二级商户/门店名称)
                record.add(billOutput.getSqbMerchantSn()); // 收钱吧商户号
                record.add(billOutput.getDimension()); // 维度（对于支付宝花呗是分期期数）
                record.add(billOutput.getSourceBillTypeName()); // 账单类型
                // 上游账单有效支付
                record.add(String.valueOf(billOutput.getSourceTradeNum())); // 上游账单有效支付笔数(拆分后)
                record.add(BigDecimal.valueOf(billOutput.getSourceTradeAmount()).divide(BigDecimal.valueOf(100)).toString()); // 上游账单有效支付金额(拆分后)(单位:元)
                record.add(String.valueOf(billOutput.getSourceRefundNum())); // 上游账单有效退款笔数(拆分后)
                record.add(BigDecimal.valueOf(billOutput.getSourceRefundAmount()).divide(BigDecimal.valueOf(100)).toString()); // 上游账单有效退款金额(拆分后)(单位:元)
                record.add(billOutput.getSourceSettlementBasisType()); // 结算依据类型
                record.add(BigDecimal.valueOf(billOutput.getSourceSettlementBasis()).divide(BigDecimal.valueOf(100)).toString()); // 上游账单结算依据(拆分后)
                // 结算费率
                if (StringUtils.isNotEmpty(billOutput.getSourceSettlementFeeRate())) {
                    //兼容
                    record.add(new BigDecimal(billOutput.getSourceSettlementFeeRate().trim()).multiply(BigDecimal.valueOf(100)).toString() + "%");
                } else {
                    record.add("0.00%");
                }
                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSourceSettlementAmount()).orElse(0L)).divide(BigDecimal.valueOf(100)).toString()); // 上游账单应结算总金额(拆分后)(单位:元)
                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSqbTradeNum()).orElse(0)).toString()); //收钱吧有效支付笔数
                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSqbTradeAmount()).orElse(0L)).divide(BigDecimal.valueOf(100)).toString());//收钱吧有效支付金额

                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSqbRefundNum()).orElse(0)).toString());//收钱吧有效退款笔数
                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSqbRefundAmount()).orElse(0L)).divide(BigDecimal.valueOf(100)).toString());//收钱吧有效退款金额

                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSqbClearingNum()).orElse(0)).toString());//收钱吧有效交易笔数
                record.add(BigDecimal.valueOf(Optional.ofNullable(billOutput.getSqbClearingAmount()).orElse(0L)).divide(BigDecimal.valueOf(100)).toString());//收钱吧有效交易金额

                //收钱吧有效交易金额占比
                if (StringUtils.isNotEmpty(billOutput.getSourceSettlementFeeRate())) {
                    record.add(new BigDecimal(billOutput.getSourceValidTradeAmountRatio().trim()).multiply(BigDecimal.valueOf(100)).toString() + "%");
                } else {
                    record.add("0.00%");
                }

                record.add(billOutput.getServiceProviderId());//上游渠道号
                record.add(billOutput.getRemark());  // 备注
                try {
                    printer.printRecord(record);

                } catch (IOException e) {
                    log.error("", e);
                }
            });

            writer.flush();

        } catch (Exception e) {
            applyStatus = 3;
            log.error("写入文件异常:", e);
        }

        log.info("开始上传oss: {}", fileFullPath);
        // 上传到 OSS
        String key = ossUtils.sendToOss(fileFullPath, true);

        if (StringUtils.isEmpty(key)) {
            applyStatus = 3;
            result.put("message", "文件上传阿里云OSS失败");
        } else {
            String fileUrl = "<a href=" + ossUtils.endpointUrl + "/" + OssUtils.STATICS_BUCKET_NAME + "/" + key + ">下载文件</a>";
            result.put("操作成功", fileUrl);
        }

        // 更新任务中心
        this.logService.updateTaskApplyLog(MapUtil.hashMap(DaoConstants.ID, taskId, TaskApplyLog.APPLY_STATUS, applyStatus, TaskApplyLog.APPLY_RESULT, gson.toJson(result)));
    }

    @Override
    public void updateParserConfig(Integer billType, ParserConfig config) {
        byte[] bytes = new Gson().toJson(config).getBytes();

        tbSourceBillMapper.updateConfig(billType, bytes);
    }

    @SneakyThrows
    @Override
    public Map<String, String> getWeixinTradebillRequest(Map<String, Object> params) {
        // 获取商户的微信商户号
        String merchantSn = MapUtil.getString(params, TransactionParam.MERCHANT_SN);
        String subMchId = MapUtil.getString(params, TransactionParam.WEIXIN_SUB_MCH_ID);
        if(StringUtil.isBlank(subMchId) && !StringUtil.isEmpty(merchantSn)){
            Map<String, Object> merchant = merchantService.getMerchantByMerchantSn(merchantSn);
            if(MapUtil.isEmpty(merchant)){
                throw new CommonInvalidParameterException("商户不存在");
            }
            Map<String, Object> merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(MapUtil.getString(merchant, DaoConstants.ID),
                    SupportService.PAYWAY_WEIXIN);
            Map<String, Object> configParams = MapUtil.getMap(merchantConfig, MerchantConfig.PARAMS);
            if(MapUtil.isEmpty(merchantConfig) || MapUtil.isEmpty(configParams)){
                throw new CommonInvalidParameterException("商户不是直连商户");
            }
            // 按照sub_payway顺序依次获取直连的微信商户号
            Map<String, Object> weixinTradeParams = new LinkedHashMap<>();
            MapUtil.addKeysIfNotExist(configParams, weixinTradeParams, TransactionParam.WEIXIN_TRADE_PARAMS, TransactionParam.WEIXIN_WAP_TRADE_PARAMS,
                    TransactionParam.WEIXIN_MINI_TRADE_PARAMS, TransactionParam.WEIXIN_APP_TRADE_PARAMS, TransactionParam.WEIXIN_H5_TRADE_PARAMS);
            for (Map.Entry<String, Object> tradeParamsInfo : weixinTradeParams.entrySet()) {
                if(tradeParamsInfo.getValue() instanceof  Map){
                    subMchId = MapUtil.getString((Map)tradeParamsInfo.getValue(), TransactionParam.WEIXIN_SUB_MCH_ID);
                    if(StringUtil.isNotBlank(subMchId)){
                        break;
                    }
                }
            }
            if(StringUtil.isEmpty(subMchId)){
                throw new CommonInvalidParameterException("商户不是直连商户");
            }
        }

        String billDate = MapUtil.getString(params, "bill_date");
        if(StringUtil.isEmpty(billDate)){
            billDate = new SafeSimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.getYesterdayStart().getTime());
        }

        // 请求参数加密
        Map<String, String> request = MapUtil.hashMap("bill_date", billDate,
                        "bill_type", MapUtil.getString(params, "bill_type"),
                        "tar_type", MapUtil.getString(params, "tar_type"),
                        "sub_mchid", subMchId
                );

        return buildWeixinAuthorizationResponse("/v3/bill/tradebill", MapUtil.getString(params,"channel_id", "**********"), request);
    }

    @SneakyThrows
    @Override
    public Map<String, String> getMerchantWeixinFundflowBillRequest(Map<String, Object> params) {
        // 获取商户的微信商户号
        String merchantSn = MapUtil.getString(params, TransactionParam.MERCHANT_SN);
        String subMchId = MapUtil.getString(params, TransactionParam.WEIXIN_SUB_MCH_ID);
        if(StringUtil.isBlank(subMchId) && !StringUtil.isEmpty(merchantSn)){
            Map<String, Object> merchant = merchantService.getMerchantByMerchantSn(merchantSn);
            if(MapUtil.isEmpty(merchant)){
                throw new CommonInvalidParameterException("商户不存在");
            }
            Map<String, Object> merchantConfig = tradeConfigService.getMerchantConfigByMerchantIdAndPayway(MapUtil.getString(merchant, DaoConstants.ID),
                    SupportService.PAYWAY_WEIXIN);
            Map<String, Object> configParams = MapUtil.getMap(merchantConfig, MerchantConfig.PARAMS);
            if(MapUtil.isEmpty(merchantConfig) || MapUtil.isEmpty(configParams)){
                throw new CommonInvalidParameterException("商户不是直连商户");
            }
            // 按照sub_payway顺序依次获取直连的微信商户号
            Map<String, Object> weixinTradeParams = new LinkedHashMap<>();
            MapUtil.addKeysIfNotExist(configParams, weixinTradeParams, TransactionParam.WEIXIN_TRADE_PARAMS, TransactionParam.WEIXIN_WAP_TRADE_PARAMS,
                    TransactionParam.WEIXIN_MINI_TRADE_PARAMS, TransactionParam.WEIXIN_APP_TRADE_PARAMS, TransactionParam.WEIXIN_H5_TRADE_PARAMS);
            for (Map.Entry<String, Object> tradeParamsInfo : weixinTradeParams.entrySet()) {
                if(tradeParamsInfo.getValue() instanceof  Map){
                    subMchId = MapUtil.getString((Map)tradeParamsInfo.getValue(), TransactionParam.WEIXIN_SUB_MCH_ID);
                    if(StringUtil.isNotBlank(subMchId)){
                        break;
                    }
                }
            }
            if(StringUtil.isEmpty(subMchId)){
                throw new CommonInvalidParameterException("商户不是直连商户");
            }
        } else if (StringUtil.isBlank(subMchId) && StringUtil.isEmpty(merchantSn)){
            throw new CommonInvalidParameterException("商户不存在");
        }

        String billDate = MapUtil.getString(params, "bill_date");
        if(StringUtil.isEmpty(billDate)){
            billDate = new SafeSimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.getYesterdayStart().getTime());
        }

        // 请求参数加密
        Map<String, String> request = MapUtil.hashMap("bill_date", billDate,
                        "account_type", MapUtil.getString(params, "account_type"),
                        "algorithm", MapUtil.getString(params, "algorithm"),
                        "tar_type", MapUtil.getString(params, "tar_type"),
                        "sub_mchid", subMchId
                );

        return buildWeixinAuthorizationResponse("/v3/bill/sub-merchant-fundflowbill", MapUtil.getString(params,"channel_id", "**********"), request);
    }

    @SneakyThrows
    @Override
    public Map<String, String> getWeixinFundflowBillRequest(Map<String, Object> params) {
        String billDate = MapUtil.getString(params, "bill_date");
        if(StringUtil.isEmpty(billDate)){
            billDate = new SafeSimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.getYesterdayStart().getTime());
        }

        // 请求参数加密
        Map<String, String> request = MapUtil.hashMap("bill_date", billDate,
                        "account_type", MapUtil.getString(params, "account_type"),
                        "tar_type", MapUtil.getString(params, "tar_type")
                );
        return buildWeixinAuthorizationResponse("/v3/bill/fundflowbill", MapUtil.getString(params,"channel_id", "**********"), request);
    }

    @SneakyThrows
    private Map<String, String> buildWeixinAuthorizationResponse(String path, String channelId, Map<String, String> params){
        com.wosai.upay.common.util.MapUtil.removeNullValues(params);
        String uri  = path + "?" + WebUtils.buildQuery(params, "utf-8");
        StringBuilder signStr = new StringBuilder();
        signStr.append("GET\n");
        signStr.append(uri).append("\n");
        signStr.append(System.currentTimeMillis() / 1000).append("\n");
        signStr.append(UUID.randomUUID().toString().replaceAll("-", "")).append("\n");
        signStr.append("\n");
        Map<String, String> signResponse = signService.wechantV3Sign(channelId, signStr.toString());
        if(MapUtil.isEmpty(signResponse)){
            throw new CommonInvalidParameterException("签名失败");
        }

        return MapUtil.hashMap("uri", uri, "Authorization", MapUtil.getString(signResponse, "authorization"));
    }

    @Override
    public Map<String, String> getWeixinDownloadUrlRequest(Map<String, String> params) {
        String url = MapUtil.getString(params, "url");
        if(StringUtil.isEmpty(url)){
            throw new CommonInvalidParameterException("下载链接不能为空");
        }
        HttpUrl httpurl = HttpUrl.parse(url);
        StringBuilder signStr = new StringBuilder();
        signStr.append("GET\n");
        signStr.append(httpurl.encodedPath()).append("?").append(httpurl.encodedQuery()).append("\n");
        signStr.append(System.currentTimeMillis() / 1000).append("\n");
        signStr.append(UUID.randomUUID().toString().replaceAll("-", "")).append("\n");
        signStr.append("\n");
        String channelId = MapUtil.getString(params,"channel_id", "**********");
        Map<String, String> signResponse = signService.wechantV3Sign(channelId, signStr.toString());
        if(MapUtil.isEmpty(signResponse)){
            throw new CommonInvalidParameterException("签名失败");
        }
        return MapUtil.hashMap("Authorization", MapUtil.getString(signResponse, "authorization"));
    }

    @SneakyThrows
    @Override
    public Map<String, String> getAlipayTradebillRequest(Map<String, Object> params) {
        String merchantSn = MapUtil.getString(params, TransactionParam.MERCHANT_SN);
        if(StringUtil.isEmpty(merchantSn)){
            throw new CommonInvalidParameterException("商户号不能为空");
        }
        Map<String, Object> merchant = merchantService.getMerchantByMerchantSn(merchantSn);
        if(MapUtil.isEmpty(merchant)){
            throw new CommonInvalidParameterException("商户不存在");
        }
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPageSize(3);
        pageInfo.setPage(1);
        com.wosai.upay.common.bean.ListResult storeList = storeService.findStores(pageInfo, MapUtil.hashMap(Terminal.MERCHANT_ID, MapUtil.getString(merchant, DaoConstants.ID), Terminal.STATUS, Terminal.STATUS_ACTIVATED));
        if(storeList.getTotal() == 0){
            throw new CommonInvalidParameterException("商户下没有有效门店");
        }
        Map<String, Object> alipayTradeParams = new HashMap<>();
        // 支付宝接口需要app_auth_token，这个数据存储在alipay-authinto项目中，要调用获取交易参数接口拿到
        for (Map<String, Object> store : storeList.getRecords()) {
            // 二级支付方式按照顺序获取，依次为：BSC、QRCODE、WAP、MINI、APP、H5
            for(int subPayway : Arrays.asList(Order.SUB_PAYWAY_BARCODE, Order.SUB_PAYWAY_QRCODE, Order.SUB_PAYWAY_WAP, 4, 5, 6)){
                try{
                    Map<String, Object> configParams = supportService.getAllParams(MapUtil.getString(store, Store.SN), null, SupportService.PAYWAY_ALIPAY2, subPayway);
                    MapUtil.addKeysIfNotExist(configParams, alipayTradeParams, TransactionParam.ALIPAY_V2_TRADE_PARAMS, TransactionParam.ALIPAY_WAP_V2_TRADE_PARAMS,
                            TransactionParam.ALIPAY_MINI_V2_TRADE_PARAMS, TransactionParam.ALIPAY_APP_V2_TRADE_PARAMS, TransactionParam.ALIPAY_H5_V2_TRADE_PARAMS);
                    if(MapUtil.isNotEmpty(alipayTradeParams)){
                        break;
                    }
                }catch (Exception e){
                    log.info("获取交易参数出错", e);
                    continue;
                }
            }
            if(MapUtil.isNotEmpty(alipayTradeParams)){
                break;
            }
        }
        if(MapUtil.isEmpty(alipayTradeParams)){
            throw new CommonInvalidParameterException("商户不是直连商户");
        }

        String billDate = MapUtil.getString(params, "bill_date");
        if(StringUtil.isEmpty(billDate)){
            billDate = new SafeSimpleDateFormat("yyyy-MM-dd").format(DateTimeUtil.getYesterdayStart().getTime());
        }

        Map<String, Object> tradeConfig = (Map)alipayTradeParams.values().stream().findFirst().get();
        SafeSimpleDateFormat dateFormat = new SafeSimpleDateFormat(AlipayConstants.DATE_TIME_FORMAT);
        // 请求参数加密
        RequestV2Builder builder = new RequestV2Builder();
        builder.set(ProtocolV2Fields.APP_ID, MapUtil.getString(tradeConfig,TransactionParam.APP_ID));
        builder.set(ProtocolV2Fields.METHOD, "alipay.data.dataservice.bill.downloadurl.query");
        builder.set(ProtocolV2Fields.CHARSET, AlipayConstants.CHARSET_UTF8);
        builder.set(ProtocolV2Fields.SIGN_TYPE,AlipayConstants.SIGN_TYPE_RSA);
        builder.set(ProtocolV2Fields.TIMESTAMP,dateFormat.format(new Date()));
        builder.set(ProtocolV2Fields.VERSION,"1.0");
        builder.set(ProtocolV2Fields.APP_AUTH_TOKEN, MapUtil.getString(tradeConfig, TransactionParam.APP_AUTH_TOKEN));
        builder.bizSet("bill_type", MapUtil.getString(params, "bill_type", "trade"));
        builder.bizSet("bill_date", billDate);

        Map<String, String> request = builder.build();
        String signContent = RsaSignature.getSignCheckContent(request);

        String sign = signService.signWithDataAndType(MapUtil.getString(tradeConfig,TransactionParam.APP_ID), signContent, AlipayConstants.SIGN_TYPE_RSA);
        if(StringUtil.isEmpty(sign)){
            throw new CommonInvalidParameterException("签名失败");
        }
        builder.set(ProtocolV2Fields.SIGN, sign);
        return builder.build();
    }
}
