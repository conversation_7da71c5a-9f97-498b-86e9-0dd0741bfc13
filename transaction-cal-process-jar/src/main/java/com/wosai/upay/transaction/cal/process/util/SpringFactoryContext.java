package com.wosai.upay.transaction.cal.process.util;

import org.springframework.beans.BeansException;
import org.springframework.beans.factory.BeanFactory;
import org.springframework.beans.factory.BeanFactoryAware;
import org.springframework.beans.factory.config.ConfigurableListableBeanFactory;
import org.springframework.stereotype.Component;

@Component
public class SpringFactoryContext implements BeanFactoryAware {

    private static ConfigurableListableBeanFactory beanFactory;


    @Override
    public void setBeanFactory(BeanFactory b) throws BeansException {
        beanFactory = (ConfigurableListableBeanFactory) b;
    }

    public static String resolve(String pattern) {
        return beanFactory.resolveEmbeddedValue(pattern);
    }

}