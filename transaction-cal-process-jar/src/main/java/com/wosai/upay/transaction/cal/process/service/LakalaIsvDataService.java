package com.wosai.upay.transaction.cal.process.service;

import com.wosai.upay.transaction.cal.process.util.FtpUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * Created by wuji<PERSON><PERSON> on 2022/4/1.
 * 拉卡拉渠道数据每日上送
 */
@Component
@Slf4j
public class LakalaIsvDataService {

    @Value("${lakala.sftp.host}")
    private String ftpHost;

    @Value("${lakala.sftp.username}")
    private String ftpUsername;

    @Value("${lakala.sftp.password}")
    private String ftpPassword;

    @Value("${lakala.sftp.port}")
    private int ftpPort;

    @Value("${lakala.sftp.path}")
    private String ftpPath;

    @Autowired
    private OdpsService odpsService;


    /**
     * 上送数据
     * @param date 20220222
     */
    @SneakyThrows
    public void send(String date){
        log.info("send to kafka start");
        long start = System.currentTimeMillis();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String folder = date;
        Date today = sdf.parse(date);
        String pt = LocalDate.parse(new SimpleDateFormat("yyyy-MM-dd").format(today)).minusDays(1).format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        waitForDataReady(pt);
        File csvFile = null;
        String fileName = null;
        String fileEndName = null;
        String sql = null;
        //发送机构数据
        sql = getSqlByName("odps/lkl-agency.sql");
        csvFile = odpsService.getCsvFile(sql.replace("{pt}", pt));
        csvFile = getOrDefaultEmptyFile(csvFile);
        fileName = "agency-" + date + ".csv";
        fileEndName = "agency-" + date + ".end";
        sendToFtp(folder, csvFile.toURI().toURL(), fileName);
        sendToFtp(folder, getEmptyFile().toURI().toURL(), fileEndName);
        csvFile.delete();
        //发送商户数据
        sql = getSqlByName("odps/lkl-merchant.sql");
        csvFile = odpsService.getCsvFile(sql.replace("{pt}", pt));
        csvFile = getOrDefaultEmptyFile(csvFile);
        fileName = "merchant-" + date + ".csv";
        fileEndName = "merchant-" + date + ".end";
        sendToFtp(folder, csvFile.toURI().toURL(), fileName);
        sendToFtp(folder, getEmptyFile().toURI().toURL(), fileEndName);
        csvFile.delete();
        //发送交易数据
        sql = getSqlByName("odps/lkl-transaction.sql");
        csvFile = odpsService.getCsvFile(sql.replace("{pt}", pt));
        csvFile = getOrDefaultEmptyFile(csvFile);
        fileName = "transaction-" + date + ".csv";
        fileEndName = "transaction-" + date + ".end";
        sendToFtp(folder, csvFile.toURI().toURL(), fileName);
        sendToFtp(folder, getEmptyFile().toURI().toURL(), fileEndName);
        csvFile.delete();
        //发送提现数据
        sql = getSqlByName("odps/lkl-withdraw.sql");
        csvFile = odpsService.getCsvFile(sql.replace("{pt}", pt));
        csvFile = getOrDefaultEmptyFile(csvFile);
        fileName = "withdraw-" + date + ".csv";
        fileEndName = "withdraw-" + date + ".end";
        sendToFtp(folder, csvFile.toURI().toURL(), fileName);
        sendToFtp(folder, getEmptyFile().toURI().toURL(), fileEndName);
        csvFile.delete();
        log.info("send to lakala finish, cost: {} s", (System.currentTimeMillis() - start)/1000.0);
    }

    @SneakyThrows
    private void waitForDataReady(String pt){
        String sql = getSqlByName("odps/lkl-check.sql");
        while (true){
            List<Map<String, Object>> result = odpsService.getResult(sql.replace("{pt}", pt));
            log.info("lakala check odps sql result: {}", result);
            //根据sql查询回来的数量判断数据是否存在, 数量为0， 表示数据还不存在, 需要等待
            boolean ready = true;
            if(result != null && result.size() == 1){
                Map<String, Object> row = result.get(0);
                for (Object value : row.values()) {
                    if(Objects.equals(0, value)){
                        ready = false;
                        break;
                    }
                }
            }else{
                ready = false;
            }
            if(ready){
                break;
            }else{
                log.info("lakala odps data is not ready, wait for 5 minutes");
                Thread.sleep(1000 * 60 * 5);
            }
        }
    }


    @SneakyThrows
    public String getSqlByName(String name){
        InputStream inputStream = new ClassPathResource(name).getInputStream();
        return IOUtils.readLines(inputStream, "utf8").stream().filter(line -> !line.trim().startsWith("--")).collect(Collectors.joining(" "));
    }

    /**
     * 上送到ftp
     * @param folder
     * @param url
     * @param fileName
     * @throws IOException
     */
    public void sendToFtp(String folder, URL url, String fileName) throws IOException {
        InputStream inputStream = url.openStream();
        boolean success = FtpUtil.uploadFileSFTP(ftpHost, ftpUsername, ftpPassword, ftpPort, ftpPath + "/" + folder, fileName, inputStream);
        inputStream.close();
        if(!success){
            throw new RuntimeException("ftpUpload failed");
        }
    }


    @SneakyThrows
    public File getOrDefaultEmptyFile(File file){
        if(file != null){
            return file;
        }else{
           return getEmptyFile();
        }
    }

    @SneakyThrows
    private File getEmptyFile(){
        File empty = File.createTempFile("empty", "");
        empty.deleteOnExit();
        return empty;
    }

}
