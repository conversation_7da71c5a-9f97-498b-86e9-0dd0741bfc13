package com.wosai.upay.transaction.cal.process.businesslog;

import com.wosai.data.bean.BeanUtil;
import com.wosai.sp.business.logstash.dto.PlatformEnum;
import com.wosai.sp.business.logstash.dto.ValidList;
import com.wosai.sp.business.logstash.dto.req.BsOpLogCreateReqDto;
import com.wosai.sp.business.logstash.service.BusinessOpLogService;
import com.wosai.upay.transaction.cal.process.util.ConstantUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;

/**
 * 商户业务日志业务类
 */
@Component
@Slf4j
public class BusinessOpLogBiz {

    @Resource
    private BusinessOpLogService businessOpLogService;


    /**
     * 商户授权推送业务日志
     *
     * @param traceId
     * @param storeId
     * @param merchantId
     * @param platform
     * @param opUserId
     * @param opUserName
     * @param remark
     * @param before
     * @param after
     */
    public void sendMerchantAuthorizeConfigBusinessLog(String traceId, String storeId, String merchantId, String platform, String opUserId, String opUserName,
                                                       String remark, Map<String, Object> before, Map<String, Object> after) {
        ValidList<BsOpLogCreateReqDto.Diff> diffValidList = buildValidList(TemplateCodeEnum.MERCHANT_AUTHORIZE_CONFIG, before, after);
        createOuterBusinessLogForAsync(traceId, TemplateCodeEnum.MERCHANT_AUTHORIZE_CONFIG.getCode(), storeId, merchantId, diffValidList,
                String.format("%s#%s", TemplateCodeEnum.MERCHANT_AUTHORIZE_CONFIG.getTableName(), TemplateCodeEnum.MERCHANT_AUTHORIZE_CONFIG.getFiledNameList().get(0)),
                remark, opUserId, opUserName, convertPlatformEnum(platform));
    }

    /**
     * 创建外部业务日志
     *
     * @param logTemplateCode 模块CODE
     * @param opObjectId      操作对象
     * @param diffValidList
     * @param remark
     * @param opUserId
     * @param opUserName
     * @link https://sqb.feishu.cn/wiki/ST0KwOl6kilNIqkhfjdcuiZunOc
     */
    public void createOuterBusinessLogForAsync(String traceId, String logTemplateCode, String opObjectId, String rootObjectId,
                                               ValidList<BsOpLogCreateReqDto.Diff> diffValidList,
                                               String sameSaveColumnCode,
                                               String remark, String opUserId, String opUserName, PlatformEnum platform) {
        try {
            BsOpLogCreateReqDto reqDto = BsOpLogCreateReqDto.builder()
                    .outerSceneTraceId(StringUtils.isBlank(traceId) ? UUID.randomUUID().toString() : traceId)
                    .logTemplateCode(logTemplateCode)
                    .opObjectId(opObjectId)
                    .rootObjectId(rootObjectId)
                    .opUserId(StringUtils.defaultIfEmpty(opUserId, ConstantUtil.SYSTEM_NAME))
                    .opUserName(StringUtils.defaultIfEmpty(opUserName, ConstantUtil.SYSTEM_NAME))
                    .remark(remark)
                    .sameSaveColumnCode(sameSaveColumnCode)
                    .platformCode(Objects.isNull(platform) ? PlatformEnum.SPA.getCode() : platform.getCode())
                    .diffList(diffValidList)
                    .build();
            businessOpLogService.createBusinessLogForAsync(reqDto);
        } catch (Exception e) {
            log.error("createOuterBusinessLogForAsync failed. opObjectId:{},traceId:{}", opObjectId, traceId, e);
        }
    }


    /**
     * 构建差异列表
     *
     * @param before 　更新前
     * @param after  　更新后
     * @return
     */
    private ValidList<BsOpLogCreateReqDto.Diff> buildValidList(TemplateCodeEnum templateCodeEnum, Map<String, Object> before, Map<String, Object> after) {
        List<BsOpLogCreateReqDto.Diff> diffValidList = new ArrayList<>();
        for (String filedName : templateCodeEnum.getFiledNameList()) {
            String valueBefore = BeanUtil.getPropString(before, filedName);
            String valueAfter = BeanUtil.getPropString(after, filedName);
            if (!Objects.equals(valueBefore, valueAfter)) {
                BsOpLogCreateReqDto.Diff build = BsOpLogCreateReqDto.Diff.builder()
                        .columnCode(String.format("%s#%s", templateCodeEnum.getTableName(), filedName))
                        .valueBefore(valueBefore)
                        .valueAfter(valueAfter)
                        .build();
                diffValidList.add(build);
            }
        }
        return ValidList.createInstance(diffValidList);
    }

    private PlatformEnum convertPlatformEnum(String platform) {
        try {
            return PlatformEnum.valueOf(platform);
        } catch (Exception e) {
            return PlatformEnum.SPA;
        }
    }


}
