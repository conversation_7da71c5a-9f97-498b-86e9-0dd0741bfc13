package com.wosai.upay.transaction.cal.process.controller;

import com.wosai.upay.transaction.cal.process.model.response.ScreenData;
import com.wosai.upay.transaction.cal.process.service.DigitalScreenService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/1/8.
 */
@RestController
@RequestMapping("/digitalScreen")
@Slf4j
public class DigitalScreenController {

    @Autowired
    private DigitalScreenService digitalScreenService;


    @RequestMapping("/getData")
    public ScreenData getData(@RequestParam(required = false) String day) {
        return digitalScreenService.getScreenData(day);
    }


}
