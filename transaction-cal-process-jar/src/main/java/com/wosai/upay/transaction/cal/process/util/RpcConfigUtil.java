package com.wosai.upay.transaction.cal.process.util;


import com.googlecode.jsonrpc4j.spring.JsonProxyFactoryBean;

import java.util.HashMap;

/**
 * RpcConfigUtil
 *
 * <AUTHOR>
 * @date 2019-09-18 20:29
 */
public class RpcConfigUtil {
    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, Class serviceInterface) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(120000);
        factoryBean.setConnectionTimeoutMillis(500);
        return factoryBean;
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingBean(String serviceUrl, Class serviceInterface,int connectTimeOut,int readTimeOut) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(readTimeOut);
        factoryBean.setConnectionTimeoutMillis(connectTimeOut);
        return factoryBean;
    }

    public static JsonProxyFactoryBean getJsonProxyFactoryWareTracingMerchantContractBean(String serviceUrl, Class serviceInterface) {
        JsonProxyFactoryBean factoryBean = new JsonProxyFactoryBean();
        factoryBean.setServiceUrl(serviceUrl);
        factoryBean.setServiceInterface(serviceInterface);
        factoryBean.setReadTimeoutMillis(30000);
        factoryBean.setConnectionTimeoutMillis(500);
        return factoryBean;
    }
}
