package com.wosai.upay.transaction.cal.process;

import com.wosai.database.instrumentation.springboot.EnableDataSourceTranslate;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;
import tk.mybatis.spring.annotation.MapperScan;

/**
 * Application
 *
 * <AUTHOR>
 * @date 2019-09-09 19:00
 */
@EnableScheduling
@MapperScan("com.wosai.upay.transaction.cal.process.mapper")
@EnableDataSourceTranslate
@SpringBootApplication
public class Application {
    public static void main(String[] args) {
        SpringApplication.run(Application.class, args);
    }
}
