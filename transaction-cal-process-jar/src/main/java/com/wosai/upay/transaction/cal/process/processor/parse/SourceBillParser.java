package com.wosai.upay.transaction.cal.process.processor.parse;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTask;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs;
import com.wosai.upay.transaction.cal.process.processor.SourceBill;
import org.apache.commons.lang3.StringUtils;

/**
 * SourceBillParser
 *
 * <AUTHOR>
 * @date 2019-09-12 14:54
 */
public interface SourceBillParser {

    default SourceBill parse(TbSourceBillInputTaskWithBLOBs tbSourceBillInputTask) {
        if (tbSourceBillInputTask == null || StringUtils.isEmpty(tbSourceBillInputTask.getFileUrl())) {
            throw new CommonInvalidParameterException("文件URL为空");
        }

        if (tbSourceBillInputTask.getConfig() == null) {
            throw new CommonInvalidParameterException("账单配置为空");
        }

        if (tbSourceBillInputTask.getFileUrl().endsWith(".csv")) {
            return this.parseCsv(tbSourceBillInputTask);
        }
        if (tbSourceBillInputTask.getFileUrl().endsWith(".xls")) {
            return this.parseExcel(tbSourceBillInputTask);
        }
        if (tbSourceBillInputTask.getFileUrl().endsWith(".xlsx")) {
            return this.parseExcel(tbSourceBillInputTask);
        }

        throw new CommonInvalidParameterException("仅支持csv、xls格式账单");
    }

    SourceBill parseCsv(TbSourceBillInputTaskWithBLOBs tbSourceBillInputTask);

    SourceBill parseExcel(TbSourceBillInputTaskWithBLOBs tbSourceBillInputTask);
}
