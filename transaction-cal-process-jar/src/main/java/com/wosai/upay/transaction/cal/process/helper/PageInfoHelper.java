package com.wosai.upay.transaction.cal.process.helper;

import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.common.bean.PageInfo;

/**
 * PageInfoHelper
 *
 * <AUTHOR>
 * @date 2019-09-12 10:58
 */
public final class PageInfoHelper {

    private static final int DEFAULT_PAGE = 1;

    private static final int DEFAULT_PAGE_SIZE = 10;

    public static PageInfo checkAndProcess(PageInfo pageInfo) {
        if (pageInfo == null) {
            pageInfo = new PageInfo(1, 10);
        }

        // check null
        if (pageInfo.getPage() == null) {
            pageInfo.setPage(DEFAULT_PAGE);
        }
        if (pageInfo.getPageSize() == null) {
            pageInfo.setPageSize(DEFAULT_PAGE_SIZE);
        }

        // check lower bounds
        if (pageInfo.getPage() < 1 || pageInfo.getPageSize() < 1) {
            throw new CommonInvalidParameterException("page、pageSize 必须为正整数");
        }

        return pageInfo;
    }
}
