package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBill;
import com.wosai.upay.transaction.cal.process.model.domain.TbSummaryBillExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbSummaryBillMapper {
    long countByExample(TbSummaryBillExample example);

    int deleteByExample(TbSummaryBillExample example);

    int insert(TbSummaryBill record);

    int insertSelective(TbSummaryBill record);

    List<TbSummaryBill> selectByExampleWithBLOBs(TbSummaryBillExample example);

    List<TbSummaryBill> selectByExample(TbSummaryBillExample example);

    int updateByExampleSelective(@Param("record") TbSummaryBill record, @Param("example") TbSummaryBillExample example);

    int updateByExampleWithBLOBs(@Param("record") TbSummaryBill record, @Param("example") TbSummaryBillExample example);

    int updateByExample(@Param("record") TbSummaryBill record, @Param("example") TbSummaryBillExample example);

    int insertSelectiveOrUpdate(TbSummaryBill record);
}