package com.wosai.upay.transaction.cal.process.util;

import java.util.Map;

import com.ctrip.framework.apollo.Config;
import com.ctrip.framework.apollo.ConfigService;
import com.wosai.common.utils.WosaiJsonUtils;
import com.wosai.data.bean.BeanUtil;

public class OssAccessKeyUtil {

    private static String ACCESS_KEY = "acp_pay";
    private static String ACCESS_KEY_ID = "AccessKey_ID";
    private static String ACCESS_KEY_SECRET = "AccessKey_Secret";
    private static String NAMESPACE_SECRET = "yunwei.accesskey";
    private static Config config = ConfigService.getConfig(NAMESPACE_SECRET);

    private static Map<String, String> getAccessKeyInfo() {
        String accessStr = config.getProperty(ACCESS_KEY, "{}");
        return WosaiJsonUtils.parseObject(accessStr, Map.class);
    }
    
    public static String getAccessKeyId() {
        return BeanUtil.getPropString(getAccessKeyInfo(), ACCESS_KEY_ID);
    }

    public static String getAccessKeySecret() {
        return BeanUtil.getPropString(getAccessKeyInfo(), ACCESS_KEY_SECRET);
    }

    public static void addOssKeyChangeEvent(Runnable task) {
        config.addChangeListener(r ->{
            if(r.isChanged(ACCESS_KEY)) {
                task.run();
            }
        });
    }
}
