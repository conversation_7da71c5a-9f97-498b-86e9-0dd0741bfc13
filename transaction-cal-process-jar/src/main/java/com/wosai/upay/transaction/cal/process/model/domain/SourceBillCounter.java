package com.wosai.upay.transaction.cal.process.model.domain;

import lombok.Data;

/**
 * SourceBillCounter
 *
 * <AUTHOR>
 * @date 2019-10-16 17:12
 */
@Data
public class SourceBillCounter {

    /**
     * 重复数据行号集合（逗号隔开）
     */
    private String subNums;

    /**
     * 上游商户id
     */
    private String sourceMerchantId;

    /**
     * 上游商户门店id/二级商户id
     */
    private String sourceLevel2MerchantId;

    /**
     * 维度（度量）
     */
    private String dimension;

    /**
     * 值出现次数
     */
    private Integer counter;
}
