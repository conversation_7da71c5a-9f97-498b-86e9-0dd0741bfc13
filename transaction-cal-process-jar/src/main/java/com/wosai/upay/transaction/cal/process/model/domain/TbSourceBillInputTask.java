package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.Date;

public class TbSourceBillInputTask {
    private Integer id;

    private Integer sourceBillType;

    private Date sourceBillStartDate;

    private Date sourceBillEndDate;

    private Integer taskStatus;

    private String fileUrl;

    private String createBy;

    private String confirmBy;

    private Date createAt;

    private Date updateAt;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getSourceBillType() {
        return sourceBillType;
    }

    public void setSourceBillType(Integer sourceBillType) {
        this.sourceBillType = sourceBillType;
    }

    public Date getSourceBillStartDate() {
        return sourceBillStartDate;
    }

    public void setSourceBillStartDate(Date sourceBillStartDate) {
        this.sourceBillStartDate = sourceBillStartDate;
    }

    public Date getSourceBillEndDate() {
        return sourceBillEndDate;
    }

    public void setSourceBillEndDate(Date sourceBillEndDate) {
        this.sourceBillEndDate = sourceBillEndDate;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public String getConfirmBy() {
        return confirmBy;
    }

    public void setConfirmBy(String confirmBy) {
        this.confirmBy = confirmBy;
    }

    public Date getCreateAt() {
        return createAt;
    }

    public void setCreateAt(Date createAt) {
        this.createAt = createAt;
    }

    public Date getUpdateAt() {
        return updateAt;
    }

    public void setUpdateAt(Date updateAt) {
        this.updateAt = updateAt;
    }
}