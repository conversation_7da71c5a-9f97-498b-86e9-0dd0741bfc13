package com.wosai.upay.transaction.cal.process.model.domain;

import lombok.Data;
import tk.mybatis.mapper.annotation.KeySql;

import javax.persistence.Id;
import java.util.Date;

@Data
public class StatementTask {

    @Id
    @KeySql(useGeneratedKeys = true)
    private Long id;

    private Long planId;

    private Integer type;

    private String pSn;

    private Integer status;

    private Date createAt;

    private Date updateAt;

    private Date deleteAt;

    private String merchantIds;

    private String isvCode;

    private String createBy;

}