package com.wosai.upay.transaction.cal.process.mapper.dao;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.TypeHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.Map;
import java.util.Objects;


public class JsonBlobTypeHandler implements TypeHandler<Map<String, Object>> {
    private static final Logger logger = LoggerFactory.getLogger(JsonBlobTypeHandler.class);
    private ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public void setParameter(PreparedStatement ps, int i, Map<String, Object> parameter, JdbcType jdbcType) throws SQLException {
        if (parameter != null) {
            try {
                byte[] data = objectMapper.writeValueAsBytes(parameter);
                ps.setBytes(i, data);
            } catch (JsonProcessingException e) {
                logger.warn("error in setParameter", e);
            }
        } else {
            ps.setBytes(i, null);
        }
    }

    @Override
    public Map<String, Object> getResult(ResultSet rs, String columnName) throws SQLException {
        return convertBytesToObject(rs.getBytes(columnName));
    }

    @Override
    public Map<String, Object> getResult(ResultSet rs, int columnIndex) throws SQLException {
        return convertBytesToObject(rs.getBytes(columnIndex));
    }

    @Override
    public Map<String, Object> getResult(CallableStatement cs, int columnIndex) throws SQLException {
        return convertBytesToObject(cs.getBytes(columnIndex));
    }

    private Map<String, Object> convertBytesToObject(byte[] vals) {
        if (Objects.nonNull(vals)) {
            try {
                Map<String, Object> data = objectMapper.readValue(vals, Map.class);
                return data;
            } catch (IOException e) {
                logger.warn("error in convertBytesToObject", e);
            }
        }
        return null;
    }

}