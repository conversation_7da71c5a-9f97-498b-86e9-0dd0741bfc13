package com.wosai.upay.transaction.cal.process.controller;


import com.google.common.collect.Maps;
import com.wosai.mpay.util.StringUtils;
import com.wosai.upay.common.bean.OrderBy;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.cal.process.constant.StatementType;
import com.wosai.upay.transaction.cal.process.model.domain.StatementTask;
import com.wosai.upay.transaction.cal.process.model.dto.*;
import com.wosai.upay.transaction.cal.process.scheduler.FeeRateDeficitScheduler;
import com.wosai.upay.transaction.cal.process.scheduler.StatementTaskScheduler;
import com.wosai.upay.transaction.cal.process.service.LakalaIsvDataService;
import com.wosai.upay.transaction.cal.process.service.StatementServiceImpl;
import com.wosai.upay.transaction.cal.process.service.StatementTaskServiceImpl;
import com.wosai.upay.transaction.cal.process.service.TaskSchedulerServiceImpl;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import com.wosai.upay.transaction.cal.process.util.OdpsUtils;
import com.wosai.upay.task.center.service.TaskLogService;
import com.wosai.upay.transaction.cal.process.util.OssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.file.Files;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneOffset;
import java.util.*;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/task")
@Slf4j
public class TaskController {

    @Autowired
    private TaskSchedulerServiceImpl taskSchedulerService;
    @Autowired
    private MerchantService merchantService;
    @Autowired
    private TaskLogService taskLogService;
    @Autowired
    private StatementServiceImpl statementService;
    @Autowired
    private StatementTaskServiceImpl statementTaskService;

    @Autowired
    private StatementTaskScheduler statementTaskScheduler;
    @Autowired
    private LakalaIsvDataService lakalaIsvDataService;

    @Autowired
    private OdpsUtils odpsUtils;

    @Autowired
    FeeRateDeficitScheduler feeRateDeficitScheduler;

    @Autowired
    OssUtils ossUtils;

    @Value("${hopeedu.filePath}")
    private String hopeEduFilePath;

    @RequestMapping("/yesterday")
    public void sendYesterdayStatement() {
        long timeStart = DateTimeUtil.getYesterdayStart().getTimeInMillis();
        long timeEnd = DateTimeUtil.getTodayStart().getTimeInMillis();
        taskSchedulerService.sendStatements(timeStart, timeEnd, null);
    }


    @RequestMapping("/time")
    public void sendStatementByTime(Long timeStart, Long timeEnd) {
        taskSchedulerService.sendStatements(timeStart, timeEnd, null);
    }

    @RequestMapping("/yesterday/merchantAuthorize")
    public void sendYesterdayMerchantAuthorizeStatement() {
        long timeStart = DateTimeUtil.getYesterdayStart().getTimeInMillis();
        long timeEnd = DateTimeUtil.getTodayStart().getTimeInMillis();
        taskSchedulerService.sendStatements(timeStart, timeEnd, StatementType.TYPE_MERCHANT_AUTHORIZE);
    }


    @RequestMapping("/byId")
    public void sendStatementById(String applyDate, Long taskId) {
        long timeStart = LocalDate.parse(applyDate).minusDays(1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long timeEnd = LocalDate.parse(applyDate).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        StatementTask task = statementTaskService.findById(taskId);
        taskSchedulerService.sendOneStatement(timeStart, timeEnd, task);
    }

    @RequestMapping("/byIdList")
    public void sendStatementByIdList(String applyDate, String taskIdList) {
        long timeStart = LocalDate.parse(applyDate).minusDays(1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long timeEnd = LocalDate.parse(applyDate).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        for (String s : taskIdList.split(",")) {
            StatementTask task = statementTaskService.findById(Long.valueOf(s));
            taskSchedulerService.sendOneStatement(timeStart, timeEnd, task);
        }
    }


    @RequestMapping("/sn")
    public void sendStamentByMerchantSn(String merchantSns, String applyDate, Boolean skip) {
        PageInfo pageInfo = new PageInfo();
        pageInfo.setPage(1);
        pageInfo.setPageSize(200);
        Map map = Maps.newHashMap();
        map.put("merchant_sns", Arrays.asList(merchantSns.split(",")));

        List<String> merchantIdList = merchantService.findMerchants(pageInfo, map).getRecords()
                .stream()
                .map(t -> MapUtils.getString(t, "id"))
                .collect(Collectors.toList());
        log.info("merchantIdList: {}", merchantIdList);

        if (!skip) {
            Map queryFilter = Maps.newHashMap();
            queryFilter.put("apply_date", applyDate);
            queryFilter.put("user_id", "script-v2");

            PageInfo pageInfo1 = new PageInfo();
            pageInfo1.setPage(1);
            pageInfo1.setPageSize(100);
            pageInfo1.setOrderBy(Collections.singletonList(new OrderBy("id", OrderBy.OrderType.ASC)));

            List<Map> list = new ArrayList();

            List<Map> listResult = taskLogService.findTaskApplyLogs(pageInfo1, queryFilter).getRecords();
            list.addAll(listResult);

            while (listResult != null && listResult.size() > 0) {
                pageInfo1.setPage(pageInfo1.getPage() + 1);
                listResult = taskLogService.findTaskApplyLogs(pageInfo1, queryFilter).getRecords();
                list.addAll(listResult);
            }

            for (Map record : list) {
                String payload = MapUtils.getString(record, "payload", "");
                if (merchantIdList.parallelStream().anyMatch(payload::contains)) {
                    String applyLogId = MapUtils.getString(record, DaoConstants.ID);
                    log.info("match payload: {} ,applyLogId: {}", payload, applyLogId);
                    taskLogService.deleteTaskApplyLog(applyLogId);
                }
            }
            log.info("删除tasklog 结束");
        }

        long timeStart = LocalDate.parse(applyDate).minusDays(1).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();
        long timeEnd = LocalDate.parse(applyDate).atStartOfDay().toInstant(ZoneOffset.of("+8")).toEpochMilli();

        taskSchedulerService.sendStatementsByMerchantIds(timeStart, timeEnd, merchantIdList);
        log.info("sendStatements 结束");

    }

    @RequestMapping("/refresh")
    public String refreshPath(String merchantSns) {

        List<String> merchantSnList = Arrays.asList(merchantSns.split(","));

        log.info("merchantSnList: {}", merchantSnList);

        for (String merchantSn : merchantSnList) {
            PageInfo pageInfo = new PageInfo();
            pageInfo.setPage(1);
            pageInfo.setPageSize(100);

            StatementTaskFilterParam taskFilterParam = new StatementTaskFilterParam();
            taskFilterParam.setMerchantSn(merchantSn);
            for (StatementTaskDto taskDto : statementTaskService.selectTaskList(pageInfo, taskFilterParam).getRecords()) {
                log.info("getTask task={}", taskDto);
                PlanListParam listParam = new PlanListParam();
                listParam.setId(Long.valueOf(taskDto.getPlanId()));
                List<PlanQueryResult> records = statementService.listPlans(pageInfo, listParam).getRecords();
                for (PlanQueryResult plan : records) {
                    log.info("getPlan plan={}", taskDto);
                    if ("month".equals(plan.getFolderName()) && plan.getPath().contains("shouqianba")) {
                        PlanUpdateReq req = new PlanUpdateReq();
                        req.setId(plan.getId());
                        req.setFolderName("");
                        log.info("updatePlan id={}", plan.getId());
                        statementService.updatePlan(req);
                    }
                }

            }
        }
        return "ok";
    }

    @RequestMapping("/isv")
    public void isv(String isvCode, String date) {
        StatementTaskFilterParam taskFilterParam = new StatementTaskFilterParam();
        taskFilterParam.setIsvCode(isvCode);
        List<StatementTaskDto> task = statementTaskService.findTask(taskFilterParam);
        if (task.isEmpty()) {
            log.info("not found task for isvCode: {}", isvCode);
            return;
        }
        StatementTaskDto statementTaskDto = task.get(0);
        PlanQueryResult plan = statementService.getPlanById(Long.valueOf(statementTaskDto.getPlanId()));
        odpsUtils.getIsv(isvCode, date, plan.getVersion());
    }

    @RequestMapping("/sendStatements")
    public void sendStatementsByDate() {
        statementTaskScheduler.sendIsvStatements();
    }

    @RequestMapping("/sendStatementsNew")
    public void sendStatementsByDateNew() {
        statementTaskScheduler.sendIsvStatementsNew();
    }

    /**
     * 发送lakala数据 跑的 date - 1 天的数据
     * @param date
     */
    @RequestMapping("/sendLakalaData")
    public void sendLakalaData(String date){
        if(StringUtils.isEmpty(date)){
            date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        }
        String finalDate = date;
        new Thread(() -> {
            try{
                lakalaIsvDataService.send(finalDate);
            }catch (Exception e){
                log.error("sendLakalaData error " + e.getMessage(), e);
            }
        }).start();
    }

    @RequestMapping("/sendFeeRateDeficit")
    public void sendFeeRateDeficit(String date){
        if(StringUtils.isEmpty(date)){
            date = new SimpleDateFormat("yyyyMMdd").format(new Date());
        }
        String finalDate = date;
        new Thread(() -> {
            try{
                feeRateDeficitScheduler.doTask(finalDate);
            }catch (Exception e){
                log.error("feeRateDeficit task error " + e.getMessage(), e);
            }
        }).start();
    }

    @GetMapping("/getHopeEduOrder/order/{day}/{instNo}/{keySign}/{fileName}.txt")
    public String hopeEduFileDownload(@PathVariable String day, @PathVariable String instNo, @PathVariable String keySign, @PathVariable String fileName, HttpServletResponse response) {

        String keySign2 = taskSchedulerService.getKeySign(day);
        if (!keySign2.equals(keySign)) {
            return "下载文件失败";
        }

        if(!taskSchedulerService.hopeEduBillAllowRequest()) {
            return "超过下载次数限制, "+TaskSchedulerServiceImpl.HOPE_EDU_TIME_WINDOW + "分钟内最大"+TaskSchedulerServiceImpl.HOPE_EDU_MAX_COUNT+"次。";
        }

        String filePath = String.format("%s/order/%s/%s/%s/%s.txt", hopeEduFilePath, day, instNo, keySign, fileName);
        String downloadFilePath = ossUtils.downloadFileToOssByFilePath(filePath);

        if (StringUtils.isEmpty(downloadFilePath)) {
            return "下载文件失败";
        }
        log.info("hope edu download file: " + downloadFilePath);

        File file = new File(downloadFilePath);
        if(!file.exists()){
            return "下载文件失败";
        }
        response.reset();
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("utf-8");
        response.setContentLength((int) file.length());
        response.setHeader("Content-Disposition", "attachment;filename=" + file.getName() );

        try(BufferedInputStream bis = new BufferedInputStream(Files.newInputStream(file.toPath()));) {
            byte[] buff = new byte[1024];
            OutputStream os  = response.getOutputStream();
            int i = 0;
            while ((i = bis.read(buff)) != -1) {
                os.write(buff, 0, i);
                os.flush();
            }
            file.deleteOnExit();
        } catch (IOException e) {
            log.error(e.getMessage() , e);
            return "下载失败";
        }
        return "下载成功";
    }
}
