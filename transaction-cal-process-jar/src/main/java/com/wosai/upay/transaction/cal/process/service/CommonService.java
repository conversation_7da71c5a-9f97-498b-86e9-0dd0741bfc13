package com.wosai.upay.transaction.cal.process.service;

import com.wosai.aop.gateway.model.ClientSideNoticeSendModel;
import com.wosai.aop.gateway.service.ClientSideNoticeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

@Component
@Slf4j
public class CommonService {
    @Autowired
    private ClientSideNoticeService clientSideNoticeService;

    /**
     * 发送aop通知
     * @param merchantId
     * @param clientSides
     * @param devCode
     * @param templateCode
     * @param thumbnailUrl
     * @param data
     */
    public void sendAopNotice(String merchantId, List<String> clientSides, String devCode, String templateCode, String thumbnailUrl, Map<String, String> data){
        ClientSideNoticeSendModel sendModel = buildClientSideNoticeSendModel(merchantId, clientSides, devCode, templateCode, "super_admin", data, thumbnailUrl);
        try{
            clientSideNoticeService.send(sendModel);
            log.info("推送成功,acccountId是{}", merchantId);
        }catch (Throwable e){
            log.error("send app notice error {}", e.getMessage(), e);
        }
    }


    /**
     * 构建ClientSideNoticeSendModel
     * @param accountId
     * @param clientSides
     * @param devCode
     * @param templateCode
     * @param role
     * @param data
     * @param thumbnailUrl
     * @return
     */
    private ClientSideNoticeSendModel buildClientSideNoticeSendModel(String accountId, List<String> clientSides, String devCode, String templateCode, String role, Map<String, String> data, String thumbnailUrl){
        ClientSideNoticeSendModel sendModel = new ClientSideNoticeSendModel();
        sendModel.setAccountId(accountId);
        sendModel.setClientSides(clientSides);
        sendModel.setDevCode(devCode);
        sendModel.setTemplateCode(templateCode);
        sendModel.setTimestamp(System.currentTimeMillis());
        sendModel.setRole(role);
        sendModel.setData(data);
        sendModel.setThumbnailUrl(thumbnailUrl);
        return sendModel;
    }
}
