package com.wosai.upay.transaction.cal.process.exception;

import com.fasterxml.jackson.databind.JsonNode;
import com.googlecode.jsonrpc4j.ErrorData;
import com.googlecode.jsonrpc4j.ErrorResolver;
import com.wosai.common.exception.RuntimeWithCodeException;
import lombok.extern.slf4j.Slf4j;

import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Set;

/**
 * 自定义 error 解析器
 *
 * <AUTHOR>
 * @date 2019-09-11 14:50
 */
@Slf4j
public class CustomErrorResolver implements ErrorResolver {

    @Override
    public JsonError resolveError(Throwable t, Method method, List<JsonNode> arguments) {
        log.error("resolveError:{},{},{}", t, method, arguments);

        String throwableName = t.getClass().getName();
        ErrorData defaultErrorData = new ErrorData(throwableName, t.getMessage());

        // 喔噻 通用自定义运行时异常
        if (t instanceof RuntimeWithCodeException) {
            return new JsonError(((RuntimeWithCodeException) t).getCode(), t.getMessage(), defaultErrorData);
        }

        // javax validation 异常
        if (t.getClass().equals(ConstraintViolationException.class)) {
            ConstraintViolationException exception = (ConstraintViolationException) t;
            Set<ConstraintViolation<?>> violations = exception.getConstraintViolations();
            final StringBuilder stringBuilder = new StringBuilder();
            for (ConstraintViolation constraintViolation : violations) {
                stringBuilder.append(constraintViolation.getMessage());
            }
            String message = stringBuilder.toString();
            return new JsonError(400, message,
                    new ErrorData(exception.getClass().getName(), message));
        }

        ErrorData errorData = new ErrorData(t.getClass().getName(), t.getMessage());
        return new JsonError(1111, "未知错误", errorData);
    }
}
