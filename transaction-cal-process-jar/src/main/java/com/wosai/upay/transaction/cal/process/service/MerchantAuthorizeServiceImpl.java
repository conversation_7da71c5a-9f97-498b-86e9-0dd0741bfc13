package com.wosai.upay.transaction.cal.process.service;

import com.github.pagehelper.PageHelper;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.app.dto.QueryMerchantUserReq;
import com.wosai.app.dto.V2.UcMerchantUserSimpleInfo;
import com.wosai.app.service.v2.MerchantUserServiceV2;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.data.util.CollectionUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.pantheon.util.StringUtil;
import com.wosai.trade.service.exception.TradeManageBizException;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.cal.process.businesslog.BusinessOpLogBiz;
import com.wosai.upay.transaction.cal.process.constant.MerchantAuthorizeConstant;
import com.wosai.upay.transaction.cal.process.mapper.MerchantAuthorizeMapper;
import com.wosai.upay.transaction.cal.process.mapper.StatementPlanMapper;
import com.wosai.upay.transaction.cal.process.mapper.StatementTaskMapper;
import com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorize;
import com.wosai.upay.transaction.cal.process.model.domain.MerchantAuthorizeExample;
import com.wosai.upay.transaction.cal.process.model.domain.StatementTask;
import com.wosai.upay.transaction.cal.process.model.dto.*;
import com.wosai.upay.transaction.cal.process.model.response.MerchantAuthorizeVo;
import com.wosai.upay.transaction.cal.process.model.response.Page;
import com.wosai.upay.transaction.cal.process.util.RedisUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import java.util.*;
import java.util.stream.Collectors;

@Service
@AutoJsonRpcServiceImpl
@Slf4j
public class MerchantAuthorizeServiceImpl implements MerchantAuthorizeService {
    @Autowired
    private MerchantAuthorizeMapper merchantAuthorizeMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private CommonService commonService;

    @Autowired
    private BusinessOpLogBiz businessOpLogBiz;

    @Autowired
    private StatementTaskMapper statementTaskMapper;

    @Autowired
    StatementPlanMapper statementPlanMapper;

    @Autowired
    private RedisUtils redisUtils;

    @Autowired
    private MerchantUserServiceV2 merchantUserServiceV2;


    @Autowired
    private LarkService larkService;


    @Value("${config.aop.terminal-merchant-authorize-dev-code}")
    private String merchantAuthorizeDevCode;

    @Value("${config.aop.terminal-merchant-pending-authorize-template-code}")
    private String merchantPendingAuthorizeTemplateCode;

    @Value("${config.aop.terminal-merchant-authorize-success-template-code}")
    private String merchantAuthorizeSuccessTemplateCode;

    @Override
    public boolean createMerchantAuthorize(MerchantAuthorizeDto merchantAuthorizeDto) {
        String lockKey = "transaction-cal-process:createMerchantAuthorize" + merchantAuthorizeDto.getTaskId();
        String lockValue = UUID.randomUUID().toString();
        try {
            // 获取锁
            if (!redisUtils.setNX(lockKey, lockValue, 1)) {
                return false;
            }

            validateTaskAndMerchant(merchantAuthorizeDto);
            if (isMerchantAlreadyAuthorized(merchantAuthorizeDto)) {
                return false;
            }

            if (isAuthorizationCountExceeded(merchantAuthorizeDto)) {
                handleAuthorizationLimitExceeded(merchantAuthorizeDto);
            }

            MerchantAuthorize merchantAuthorize = buildMerchantAuthorize(merchantAuthorizeDto);
            int result = merchantAuthorizeMapper.insert(merchantAuthorize);
            if (result != 0) {
                // 发送Aop通知
                HashMap<String, String> data = new HashMap<>();
                data.put(MerchantAuthorizeConstant.TASK_ID, String.valueOf(merchantAuthorizeDto.getTaskId()));
                data.put(MerchantAuthorizeConstant.MERCHANT_SN, merchantAuthorizeDto.getMerchantSn());
                sendAop(getMerchantIdBySn(merchantAuthorizeDto.getMerchantSn()), merchantPendingAuthorizeTemplateCode, data);
            }
            return result != 0;
        } catch (Exception e) {
            log.error("创建商户授权失败", e);
            throw new CommonInvalidParameterException(e.getMessage());
        } finally {
            redisUtils.deleteIfEqual(lockKey, lockValue);
        }
    }

    private void handleAuthorizationLimitExceeded(MerchantAuthorizeDto merchantAuthorizeDto) {
        // 任务关联商户达到上限
        StatementTask statementTask = statementTaskMapper.selectByPrimaryKey(merchantAuthorizeDto.getTaskId());
        // 查询计划关联的所有任务
        com.github.pagehelper.Page<StatementTaskDto> page = PageHelper.startPage(1, 300).doSelectPage(() -> {
            statementTaskMapper.selectTaskList(null
                    , statementTask.getType()
                    , null, null
                    , statementTask.getPlanId(), null
                    , null
            );
        });
        // 找到第一个没有超过授权数量的任务
        Optional<StatementTaskDto> optionalStatementTaskDto = page.getResult().stream()
                .filter(statementTaskDto -> {
                    merchantAuthorizeDto.setTaskId(Long.valueOf(statementTaskDto.getTaskId()));
                    return !isAuthorizationCountExceeded(merchantAuthorizeDto);
                })
                .findFirst();

        if (optionalStatementTaskDto.isPresent()) {
            // 找到了没有超过授权数量的任务 返回对应的任务id
            StatementTaskDto statementTaskDto = optionalStatementTaskDto.get();
            larkService.larkMessage(String.format("任务id: %s 商户授权数量超过上限。商户号: %s 未绑定成功,请更换新的任务id taskId: %s 进行绑定", merchantAuthorizeDto.getTaskId(), merchantAuthorizeDto.getMerchantSn(), statementTaskDto.getTaskId()));
            throw new CommonInvalidParameterException(String.format("任务id: %s 商户授权数量超过上限。商户号: %s 未绑定成功,请更换新的任务id taskId: %s 进行绑定", merchantAuthorizeDto.getTaskId(), merchantAuthorizeDto.getMerchantSn(), statementTaskDto.getTaskId()));
        } else {
            // 创建新的任务
            StatementTask newStatementTask = new StatementTask();
            newStatementTask.setPlanId(statementTask.getPlanId());
            newStatementTask.setType(statementTask.getType());
            newStatementTask.setPSn(statementTask.getPSn());
            newStatementTask.setMerchantIds("");
            newStatementTask.setIsvCode(statementTask.getIsvCode());
            newStatementTask.setStatus(1);
            newStatementTask.setCreateBy("自动创建");
            int insert = statementTaskMapper.insert(newStatementTask);
            if (insert == 0) {
                throw new CommonInvalidParameterException("创建新的任务失败");
            }
            // 找到新的任务id
            com.github.pagehelper.Page<StatementTaskDto> pageV2 = PageHelper.startPage(1, 1).doSelectPage(() -> {
                statementTaskMapper.selectTaskList(null
                        , statementTask.getType()
                        , null, null
                        , statementTask.getPlanId(), null
                        , null
                );
            });
            if (pageV2.getResult().size() == 0) {
                throw new CommonInvalidParameterException("查询新的任务失败");
            }
            StatementTaskDto statementTaskDto = pageV2.getResult().get(0);
            larkService.larkMessage(String.format("任务id: %s 商户授权数量超过上限。商户号: %s 未绑定成功,请更换新的任务id taskId: %s 进行绑定", merchantAuthorizeDto.getTaskId(), merchantAuthorizeDto.getMerchantSn(), statementTaskDto.getTaskId()));
            throw new CommonInvalidParameterException(String.format("任务id: %s 商户授权数量超过上限。商户号: %s 未绑定成功,请更换新的任务id taskId: %s 进行绑定", merchantAuthorizeDto.getTaskId(), merchantAuthorizeDto.getMerchantSn(), statementTaskDto.getTaskId()));
        }
    }

    private void validateTaskAndMerchant(MerchantAuthorizeDto merchantAuthorizeDto) {
        StatementTask statementTask = statementTaskMapper.selectByPrimaryKey(merchantAuthorizeDto.getTaskId());
        if (statementTask == null) {
            throw new CommonInvalidParameterException("任务不存在");
        }

        Map merchantBySn = merchantService.getMerchantBySn(merchantAuthorizeDto.getMerchantSn());
        if (merchantBySn == null) {
            throw new CommonInvalidParameterException("商户不存在");
        }
    }

    private boolean isMerchantAlreadyAuthorized(MerchantAuthorizeDto merchantAuthorizeDto) {
        String merchantId = getMerchantIdBySn(merchantAuthorizeDto.getMerchantSn());
        MerchantAuthorizeExample example = new MerchantAuthorizeExample();
        example.createCriteria().andTaskIdEqualTo(merchantAuthorizeDto.getTaskId()).andMerchantIdEqualTo(merchantId);
        return !merchantAuthorizeMapper.selectByExample(example).isEmpty();
    }

    private boolean isAuthorizationCountExceeded(MerchantAuthorizeDto merchantAuthorizeDto) {
        MerchantAuthorizeExample example = new MerchantAuthorizeExample();
        example.createCriteria().andTaskIdEqualTo(merchantAuthorizeDto.getTaskId());
        return merchantAuthorizeMapper.countByExample(example) > 900;
    }

    private MerchantAuthorize buildMerchantAuthorize(MerchantAuthorizeDto merchantAuthorizeDto) {
        MerchantAuthorize merchantAuthorize = new MerchantAuthorize();
        merchantAuthorize.setTaskId(merchantAuthorizeDto.getTaskId());
        merchantAuthorize.setMerchantId(getMerchantIdBySn(merchantAuthorizeDto.getMerchantSn()));
        merchantAuthorize.setCtime(System.currentTimeMillis());
        merchantAuthorize.setMtime(System.currentTimeMillis());
        merchantAuthorize.setStatus(MerchantAuthorizeConstant.UN_AUTHORIZED);
        return merchantAuthorize;
    }


    private String getMerchantIdBySn(String merchantSn) {
        Map merchantBySn = merchantService.getMerchantBySn(merchantSn);
        return MapUtil.getString(merchantBySn, DaoConstants.ID);
    }

    @Override
    @Transactional
    public boolean updateMerchantAuthorize(UpdateMerchantAuthorizeDto updateMerchantAuthorizeDto) {
        if (updateMerchantAuthorizeDto.getTaskId() == null) {
            throw new CommonInvalidParameterException("任务不存在");
        }
        if (updateMerchantAuthorizeDto.getStatus() == null || MerchantAuthorizeConstant.AUTHORIZED != updateMerchantAuthorizeDto.getStatus()) {
            throw new CommonInvalidParameterException("授权状态不正确");
        }
        MerchantAuthorize merchantAuthorize = new MerchantAuthorize();
        merchantAuthorize.setStatus(updateMerchantAuthorizeDto.getStatus());
        merchantAuthorize.setMtime(System.currentTimeMillis());
        Map merchantBySn = merchantService.getMerchantBySn(updateMerchantAuthorizeDto.getMerchantSn());
        if (merchantBySn == null) {
            throw new CommonInvalidParameterException("商户不存在");
        }
        String lockKey = "transaction-cal-process:" + "updateMerchantAuthorize";
        String lockValue = UUID.randomUUID().toString();
        int result = 0;
        try {
            // 获取锁
            if (!redisUtils.setNX(lockKey, lockValue, 2)) {
                return false;
            }
            String merchantId = MapUtil.getString(merchantBySn, DaoConstants.ID);
            MerchantAuthorizeExample example = new MerchantAuthorizeExample();
            MerchantAuthorizeExample.Criteria criteria = example.createCriteria();
            criteria.andTaskIdEqualTo(updateMerchantAuthorizeDto.getTaskId());
            criteria.andMerchantIdEqualTo(merchantId);
            List<MerchantAuthorize> merchantAuthorizes = merchantAuthorizeMapper.selectByExample(example);
            if (merchantAuthorizes.size() == 0) {
                throw new CommonInvalidParameterException("授权记录不存在");
            }
            MerchantAuthorize merchantAuthoriz = merchantAuthorizes.get(0);
            if (merchantAuthoriz.getStatus() != MerchantAuthorizeConstant.UN_AUTHORIZED) {
                throw new CommonInvalidParameterException("授权记录已经授权");
            }
            // 更新授权状态
            result = merchantAuthorizeMapper.updateByExampleSelective(merchantAuthorize, example);
            // 授权成功，更新任务中的商户id列表
            if (result != 0) {
                StatementTask statementTask = statementTaskMapper.selectByPrimaryKey(updateMerchantAuthorizeDto.getTaskId());
                String merchantIds = statementTask.getMerchantIds();
                if (StringUtil.isEmpty(merchantIds)) {
                    statementTask.setMerchantIds(merchantId);
                } else {
                    List<String> merchantIdList = new ArrayList<>(Arrays.asList(merchantIds.split(",")));
                    merchantIdList.add(merchantId);
                    statementTask.setMerchantIds(merchantIdList.stream().distinct().collect(Collectors.joining(",")));
                }
                statementTaskMapper.updateByPrimaryKeySelective(statementTask);
                HashMap<String, String> data = new HashMap<>();
                data.put(MerchantAuthorizeConstant.TASK_ID, String.valueOf(updateMerchantAuthorizeDto.getTaskId()));
                data.put(MerchantAuthorizeConstant.MERCHANT_SN, updateMerchantAuthorizeDto.getMerchantSn());
                sendAop(merchantId, merchantAuthorizeSuccessTemplateCode, data);
                //记录商户业务日志
                Map before = CollectionUtil.hashMap(MerchantAuthorizeConstant.STATUS, "否");
                Map after = CollectionUtil.hashMap(MerchantAuthorizeConstant.STATUS, "是");
                businessOpLogBiz.sendMerchantAuthorizeConfigBusinessLog("", merchantId, "", "收钱吧APP", updateMerchantAuthorizeDto.getUc_user_id(), updateMerchantAuthorizeDto.getUc_user_id(), String.valueOf(updateMerchantAuthorizeDto.getTaskId()), before, after);
            }
            return result != 0;
        } catch (Exception e) {
            log.error("更新商户授权状态失败", e);
            throw new CommonInvalidParameterException("更新商户授权状态失败");
        } finally {
            redisUtils.deleteIfEqual(lockKey, lockValue);
        }
    }

    @Override
    public MerchantAuthorizeVo queryMerchantAuthorize(MerchantAuthorizeReq merchantAuthorizeReq) {
        MerchantAuthorizeExample example = new MerchantAuthorizeExample();
        MerchantAuthorizeExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(merchantAuthorizeReq.getTaskId());
        Map merchantBySn = merchantService.getMerchantBySn(merchantAuthorizeReq.getMerchantSn());
        if (merchantBySn == null) {
            throw new CommonInvalidParameterException("商户不存在");
        }
        String merchantId = MapUtil.getString(merchantBySn, DaoConstants.ID);
        criteria.andMerchantIdEqualTo(merchantId);
        List<MerchantAuthorize> merchantAuthorizes = merchantAuthorizeMapper.selectByExample(example);
        MerchantAuthorizeVo merchantAuthorizeVo = new MerchantAuthorizeVo();
        if (merchantAuthorizes.size() > 0) {
            MerchantAuthorize merchantAuthorize = merchantAuthorizes.get(0);
            merchantAuthorizeVo = MerchantAuthorizeVo.builder()
                    .id(merchantAuthorize.getId())
                    .taskId(merchantAuthorize.getTaskId())
                    .merchantId(merchantAuthorize.getMerchantId())
                    .status(merchantAuthorize.getStatus())
                    .ctime(merchantAuthorize.getCtime())
                    .mtime(merchantAuthorize.getMtime())
                    .build();
        }
        return merchantAuthorizeVo;
    }

    @Override
    public Page<MerchantAuthorizeVo> queryMerchantAuthorizes(MerchantAuthorizePageReq merchantAuthorizePageReq) {
        MerchantAuthorizeExample example = new MerchantAuthorizeExample();
        MerchantAuthorizeExample.Criteria criteria = example.createCriteria();
        criteria.andTaskIdEqualTo(merchantAuthorizePageReq.getTaskId());
        if (merchantAuthorizePageReq.getStatus() != null) {
            criteria.andStatusEqualTo(merchantAuthorizePageReq.getStatus());
        }
        long total = merchantAuthorizeMapper.countByExample(example);
        MerchantAuthorizeExample example2 = new MerchantAuthorizeExample();
        MerchantAuthorizeExample.Criteria criteria2 = example2.createCriteria();
        criteria2.andTaskIdEqualTo(merchantAuthorizePageReq.getTaskId());
        if (merchantAuthorizePageReq.getStatus() != null) {
            criteria2.andStatusEqualTo(merchantAuthorizePageReq.getStatus());
        }
        List<MerchantAuthorize> merchantAuthorizes = merchantAuthorizeMapper.selectByExampleWithPage(example2, (merchantAuthorizePageReq.getPageNum() - 1) * merchantAuthorizePageReq.getPageSize(), merchantAuthorizePageReq.getPageSize());
        List<MerchantAuthorizeVo> merchantAuthorizeVos = new ArrayList<>();
        for (MerchantAuthorize merchantAuthorize : merchantAuthorizes) {
            MerchantAuthorizeVo merchantAuthorizeVo = MerchantAuthorizeVo.builder()
                    .id(merchantAuthorize.getId())
                    .taskId(merchantAuthorize.getTaskId())
                    .merchantId(merchantAuthorize.getMerchantId())
                    .status(merchantAuthorize.getStatus())
                    .ctime(merchantAuthorize.getCtime())
                    .mtime(merchantAuthorize.getMtime())
                    .build();
            merchantAuthorizeVos.add(merchantAuthorizeVo);
        }
        return new Page<>(merchantAuthorizePageReq.getPageNum(), merchantAuthorizePageReq.getPageSize(), total, merchantAuthorizeVos);
    }

    @Override
    public void retryMerchantAuthorizeAop(MerchantAuthorizeDto merchantAuthorizeDto) {
        String lockKey = "transaction-cal-process:" + "retryMerchantAuthorizeAop";
        String lockValue = UUID.randomUUID().toString();
        try {
            // 获取锁
            if (!redisUtils.setNX(lockKey, lockValue, 2)) {
                return;
            }
            StatementTask statementTask = statementTaskMapper.selectByPrimaryKey(merchantAuthorizeDto.getTaskId());
            if (statementTask == null) {
                throw new CommonInvalidParameterException("任务不存在");
            }
            MerchantAuthorizeReq merchantAuthorizeReq = new MerchantAuthorizeReq();
            merchantAuthorizeReq.setTaskId(merchantAuthorizeDto.getTaskId());
            merchantAuthorizeReq.setMerchantSn(merchantAuthorizeDto.getMerchantSn());
            MerchantAuthorizeVo merchantAuthorizeVo = queryMerchantAuthorize(merchantAuthorizeReq);
            if (merchantAuthorizeVo == null || merchantAuthorizeVo.getStatus() != MerchantAuthorizeConstant.UN_AUTHORIZED) {
                throw new CommonInvalidParameterException("授权记录不存在或者已经授权");
            }
            // 发送Aop通知
            HashMap<String, String> data = new HashMap<>();
            data.put(MerchantAuthorizeConstant.TASK_ID, String.valueOf(merchantAuthorizeDto.getTaskId()));
            data.put(MerchantAuthorizeConstant.MERCHANT_SN, merchantAuthorizeDto.getMerchantSn());
            sendAop(merchantAuthorizeVo.getMerchantId(), merchantPendingAuthorizeTemplateCode, data);
        } catch (Exception e) {
            log.error("重试商户授权Aop通知失败", e);
        } finally {
            redisUtils.deleteIfEqual(lockKey, lockValue);
        }
    }

    private void sendAop(String merchantId, String templateCode, HashMap<String, String> data) {
        QueryMerchantUserReq queryMerchantUserReq = new QueryMerchantUserReq();
        queryMerchantUserReq.setRole_id(MerchantAuthorizeConstant.BOSS_ROLE_ID);
        queryMerchantUserReq.setMerchant_id(merchantId);
        List<UcMerchantUserSimpleInfo> merchantUserSimpleInfo = merchantUserServiceV2.getMerchantUserSimpleInfo(queryMerchantUserReq);
        if (CollectionUtils.isEmpty(merchantUserSimpleInfo)) {
            throw TradeManageBizException.createExc("用户信息不存在!");
        }
        for (UcMerchantUserSimpleInfo ucMerchantUserSimpleInfo : merchantUserSimpleInfo) {
            // 发送授权通过通知
            commonService.sendAopNotice(ucMerchantUserSimpleInfo.getUc_user_id(), Collections.singletonList("TERMINALAPP"), merchantAuthorizeDevCode, templateCode, null, data);
        }

    }

}
