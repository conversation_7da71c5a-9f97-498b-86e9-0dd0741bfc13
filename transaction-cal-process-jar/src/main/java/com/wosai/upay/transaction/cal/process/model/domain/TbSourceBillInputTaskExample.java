package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class TbSourceBillInputTaskExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TbSourceBillInputTaskExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIsNull() {
            addCriterion("source_bill_type is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIsNotNull() {
            addCriterion("source_bill_type is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeEqualTo(Integer value) {
            addCriterion("source_bill_type =", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotEqualTo(Integer value) {
            addCriterion("source_bill_type <>", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeGreaterThan(Integer value) {
            addCriterion("source_bill_type >", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_bill_type >=", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeLessThan(Integer value) {
            addCriterion("source_bill_type <", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("source_bill_type <=", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIn(List<Integer> values) {
            addCriterion("source_bill_type in", values, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotIn(List<Integer> values) {
            addCriterion("source_bill_type not in", values, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_type between", value1, value2, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_type not between", value1, value2, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateIsNull() {
            addCriterion("source_bill_start_date is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateIsNotNull() {
            addCriterion("source_bill_start_date is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_start_date =", value, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_start_date <>", value, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateGreaterThan(Date value) {
            addCriterionForJDBCDate("source_bill_start_date >", value, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_start_date >=", value, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateLessThan(Date value) {
            addCriterionForJDBCDate("source_bill_start_date <", value, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_start_date <=", value, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateIn(List<Date> values) {
            addCriterionForJDBCDate("source_bill_start_date in", values, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("source_bill_start_date not in", values, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("source_bill_start_date between", value1, value2, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillStartDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("source_bill_start_date not between", value1, value2, "sourceBillStartDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateIsNull() {
            addCriterion("source_bill_end_date is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateIsNotNull() {
            addCriterion("source_bill_end_date is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_end_date =", value, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateNotEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_end_date <>", value, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateGreaterThan(Date value) {
            addCriterionForJDBCDate("source_bill_end_date >", value, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_end_date >=", value, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateLessThan(Date value) {
            addCriterionForJDBCDate("source_bill_end_date <", value, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("source_bill_end_date <=", value, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateIn(List<Date> values) {
            addCriterionForJDBCDate("source_bill_end_date in", values, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateNotIn(List<Date> values) {
            addCriterionForJDBCDate("source_bill_end_date not in", values, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("source_bill_end_date between", value1, value2, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andSourceBillEndDateNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("source_bill_end_date not between", value1, value2, "sourceBillEndDate");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(Integer value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(Integer value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(Integer value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(Integer value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(Integer value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<Integer> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<Integer> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(Integer value1, Integer value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andFileUrlIsNull() {
            addCriterion("file_url is null");
            return (Criteria) this;
        }

        public Criteria andFileUrlIsNotNull() {
            addCriterion("file_url is not null");
            return (Criteria) this;
        }

        public Criteria andFileUrlEqualTo(String value) {
            addCriterion("file_url =", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotEqualTo(String value) {
            addCriterion("file_url <>", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThan(String value) {
            addCriterion("file_url >", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlGreaterThanOrEqualTo(String value) {
            addCriterion("file_url >=", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThan(String value) {
            addCriterion("file_url <", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLessThanOrEqualTo(String value) {
            addCriterion("file_url <=", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlLike(String value) {
            addCriterion("file_url like", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotLike(String value) {
            addCriterion("file_url not like", value, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlIn(List<String> values) {
            addCriterion("file_url in", values, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotIn(List<String> values) {
            addCriterion("file_url not in", values, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlBetween(String value1, String value2) {
            addCriterion("file_url between", value1, value2, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andFileUrlNotBetween(String value1, String value2) {
            addCriterion("file_url not between", value1, value2, "fileUrl");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNull() {
            addCriterion("create_by is null");
            return (Criteria) this;
        }

        public Criteria andCreateByIsNotNull() {
            addCriterion("create_by is not null");
            return (Criteria) this;
        }

        public Criteria andCreateByEqualTo(String value) {
            addCriterion("create_by =", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotEqualTo(String value) {
            addCriterion("create_by <>", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThan(String value) {
            addCriterion("create_by >", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByGreaterThanOrEqualTo(String value) {
            addCriterion("create_by >=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThan(String value) {
            addCriterion("create_by <", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLessThanOrEqualTo(String value) {
            addCriterion("create_by <=", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByLike(String value) {
            addCriterion("create_by like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotLike(String value) {
            addCriterion("create_by not like", value, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByIn(List<String> values) {
            addCriterion("create_by in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotIn(List<String> values) {
            addCriterion("create_by not in", values, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByBetween(String value1, String value2) {
            addCriterion("create_by between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andCreateByNotBetween(String value1, String value2) {
            addCriterion("create_by not between", value1, value2, "createBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByIsNull() {
            addCriterion("confirm_by is null");
            return (Criteria) this;
        }

        public Criteria andConfirmByIsNotNull() {
            addCriterion("confirm_by is not null");
            return (Criteria) this;
        }

        public Criteria andConfirmByEqualTo(String value) {
            addCriterion("confirm_by =", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotEqualTo(String value) {
            addCriterion("confirm_by <>", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByGreaterThan(String value) {
            addCriterion("confirm_by >", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByGreaterThanOrEqualTo(String value) {
            addCriterion("confirm_by >=", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByLessThan(String value) {
            addCriterion("confirm_by <", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByLessThanOrEqualTo(String value) {
            addCriterion("confirm_by <=", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByLike(String value) {
            addCriterion("confirm_by like", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotLike(String value) {
            addCriterion("confirm_by not like", value, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByIn(List<String> values) {
            addCriterion("confirm_by in", values, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotIn(List<String> values) {
            addCriterion("confirm_by not in", values, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByBetween(String value1, String value2) {
            addCriterion("confirm_by between", value1, value2, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andConfirmByNotBetween(String value1, String value2) {
            addCriterion("confirm_by not between", value1, value2, "confirmBy");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}