package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutput;
import com.wosai.upay.transaction.cal.process.model.domain.TbBillOutputExample;

import java.util.Date;
import java.util.List;

import com.wosai.upay.transaction.cal.process.model.dto.BillOutput;
import com.wosai.upay.transaction.cal.process.model.dto.TradeInfoDto;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.session.RowBounds;

public interface TbBillOutputMapper {
    long countByExample(TbBillOutputExample example);

    TradeInfoDto statisticalTradeTotal(@Param("taskId") Integer taskId);

    int deleteByExample(TbBillOutputExample example);

    int deleteByPrimaryKey(Long id);

    int insert(TbBillOutput record);

    int insertSelective(TbBillOutput record);

    int batchInsert(@Param("list") List<TbBillOutput> tbBillOutputList);

    List<TbBillOutput> selectByExample(TbBillOutputExample example);

    TbBillOutput selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") TbBillOutput record, @Param("example") TbBillOutputExample example);

    int updateByExample(@Param("record") TbBillOutput record, @Param("example") TbBillOutputExample example);

    int updateByPrimaryKeySelective(TbBillOutput record);

    int updateByPrimaryKey(TbBillOutput record);

    List<BillOutput> findBillOutputList(@Param("billType") Integer billType,
                                        @Param("billSourceCompany") Integer billSourceCompany,
                                        @Param("billSourceClassify") Integer billSourceClassify,
                                        @Param("startDate") Date startDate,
                                        @Param("endDate") Date endDate,
                                        @Param("sourceMerchantId") String sourceMerchantId,
                                        @Param("sourceMerchantName") String sourceMerchantName,
                                        @Param("sqbMerchantSn") String sqbMerchantSn,
                                        @Param("serviceProviderId") String serviceProviderId,
                                        @Param("lastId") Long lastId);
}