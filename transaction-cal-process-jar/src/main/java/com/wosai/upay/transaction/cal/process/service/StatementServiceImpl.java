package com.wosai.upay.transaction.cal.process.service;

import com.alibaba.fastjson.JSON;
import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.google.common.collect.ImmutableMap;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.cal.process.helper.PageInfoHelper;
import com.wosai.upay.transaction.cal.process.mapper.StatementPlanMapper;
import com.wosai.upay.transaction.cal.process.model.domain.StatementPlan;
import com.wosai.upay.transaction.cal.process.model.dto.*;
import com.wosai.web.api.ListResult;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.BeanWrapper;
import org.springframework.beans.BeanWrapperImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.validation.Valid;
import java.util.*;

import static java.util.stream.Collectors.toList;

@Service
@AutoJsonRpcServiceImpl
public class StatementServiceImpl implements StatementService {


    @Autowired
    StatementPlanMapper statementPlanMapper;

    @Override
    public int addPlan(PlanAddReq req) {
        StatementPlan plan = new StatementPlan();
        BeanUtils.copyProperties(req, plan);
        plan.setStatus(1)
                .setAgreementConfig(JSON.toJSONString(new AgreementConfig()
                        .setFolderName(req.getFolderName())
                        .setHost(req.getHost())
                        .setPassword(req.getPassword())
                        .setPath(req.getPath())
                        .setPort(req.getPort())
                        .setUsername(req.getUsername())))
                .setStatementConfig(JSON.toJSONString(new StatementConfig()
                        .setLanguage(req.getLanguage())
                        .setSplit_type(req.getSplitType())
                        .setSheet_params(ImmutableMap.of("sheet_type", req.getSheetType()))
                        .setSheet_detail_params(ImmutableMap.of("sheet_detail_type", req.getSheetDetailType()))
                        .setVersion(req.getVersion())
                        .setPeriodStartTime(req.getPeriodStartTime())
                        .setPeriodEndTime(req.getPeriodEndTime())));

        return statementPlanMapper.insertSelective(plan);
    }

    @Override
    public int updatePlan(@Valid PlanUpdateReq req) {
        Long planId = req.getId();
        PlanQueryResult dbPlan = getPlanById(planId);
        BeanUtils.copyProperties(req, dbPlan, nullPropNames(req));
        StatementPlan plan = new StatementPlan();
        BeanUtils.copyProperties(dbPlan, plan);

        plan.setAgreementConfig(JSON.toJSONString(new AgreementConfig()
                .setFolderName(dbPlan.getFolderName())
                .setHost(dbPlan.getHost())
                .setPassword(dbPlan.getPassword())
                .setPath(dbPlan.getPath())
                .setPort(dbPlan.getPort())
                .setUsername(dbPlan.getUsername())))
                .setStatementConfig(JSON.toJSONString(new StatementConfig()
                        .setLanguage(dbPlan.getLanguage())
                        .setSplit_type(dbPlan.getSplitType())
                        .setSheet_params(ImmutableMap.of("sheet_type", dbPlan.getSheetType()))
                        .setSheet_detail_params(ImmutableMap.of("sheet_detail_type", dbPlan.getSheetDetailType()))
                        .setVersion(req.getVersion())
                        .setPeriodStartTime(req.getPeriodStartTime())
                        .setPeriodEndTime(req.getPeriodEndTime())));
        return statementPlanMapper.updateByPrimaryKeySelective(plan);
    }

    @Override
    public int updatePlanStatus(Long id, Integer status) {
        return statementPlanMapper.updateByPrimaryKeySelective(new StatementPlan()
                .setId(id)
                .setStatus(status));
    }

    @Override
    public PlanQueryResult getPlanById(Long id) {
        return toQueryResult(Optional.ofNullable(statementPlanMapper.selectByPrimaryKey(id)).orElseThrow(() -> new CommonInvalidParameterException("计划不存在")));
    }

    @Override
    public int delPlan(Long id) {
        return statementPlanMapper.updateByPrimaryKeySelective(new StatementPlan()
                .setId(id)
                .setDeleteAt(new Date()));
    }

    @Override
    public ListResult<PlanQueryResult> listPlans(PageInfo pageInfo, PlanListParam listParam) {
        pageInfo = PageInfoHelper.checkAndProcess(pageInfo);
        Page page = PageHelper.startPage(pageInfo.getPage(), pageInfo.getPageSize());
        List<PlanQueryResult> result = statementPlanMapper.listPlan(listParam)
                .stream()
                .map(this::toQueryResult)
                .collect(toList());
        ListResult<PlanQueryResult> listResult = new ListResult<>(result);
        listResult.setTotal(page.getTotal());
        return listResult;
    }


    private PlanQueryResult toQueryResult(StatementPlan plan) {
        PlanQueryResult result = new PlanQueryResult();
        BeanUtils.copyProperties(plan, result);
        AgreementConfig agreementConfig = JSON.parseObject(plan.getAgreementConfig(), AgreementConfig.class);
        StatementConfig statementConfig = JSON.parseObject(plan.getStatementConfig(), StatementConfig.class);
        return result.setUsername(agreementConfig.getUsername())
                .setPassword(agreementConfig.getPassword())
                .setHost(agreementConfig.getHost())
                .setPort(agreementConfig.getPort())
                .setPath(agreementConfig.getPath())
                .setFolderName(agreementConfig.getFolderName())
                .setLanguage(statementConfig.getLanguage())
                .setSplitType(statementConfig.getSplit_type())
                .setSheetType((Integer) statementConfig.getSheet_params().get("sheet_type"))
                .setSheetDetailType((Integer) statementConfig.getSheet_detail_params().get("sheet_detail_type"))
                .setVersion(statementConfig.getVersion())
                .setPeriodStartTime(statementConfig.getPeriodStartTime())
                .setPeriodEndTime(statementConfig.getPeriodEndTime());
    }


    private static String[] nullPropNames(Object source) {
        final BeanWrapper src = new BeanWrapperImpl(source);
        java.beans.PropertyDescriptor[] pds = src.getPropertyDescriptors();
        Set<String> emptyNames = new HashSet<>();
        for (java.beans.PropertyDescriptor pd : pds) {
            Object srcValue = src.getPropertyValue(pd.getName());
            if (srcValue == null) emptyNames.add(pd.getName());
        }
        String[] result = new String[emptyNames.size()];
        return emptyNames.toArray(result);
    }
}

