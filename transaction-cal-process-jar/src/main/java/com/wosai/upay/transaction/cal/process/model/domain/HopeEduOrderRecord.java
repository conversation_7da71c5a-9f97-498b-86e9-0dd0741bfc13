package com.wosai.upay.transaction.cal.process.model.domain;

import lombok.Data;

/**
 * @version 1.0
 * @author: yuhai
 * @program: transaction-cal-process
 * @className HopeEduOrderRecord
 * @description: 院校通对账文件数据对象
 * @create: 2025-05-28 18:19
 **/
@Data
public class HopeEduOrderRecord {

    /**
     * 日期
     */
    private String date;

    /**
     * 商户号
     */
    private String merchantNo = "";
    /**
     * 终端号(设备号在系统中的映射)
     */
    private String terminalId = "";
    /**
     * 终端交易时间，yyyy-MM-dd HH:mm:ss，全局统一时间格式
     */
    private String terminalTime = "";
    /**
     * 退款完成时间，yyyy-MM-dd HH:mm:ss
     */
    private String refundCompleteTime = "";
    /**
     * 金额，单位分
     */
    private String totalFee = "";
    /**
     * 手续费金额，单位分
     */
    private String serviceFee = "";
    /**
     * 退款金额，单位分
     */
    private String refundFee = "";
    /**
     * 结余金额，单位分
     */
    private String balanceFee = "";
    /**
     * 交易类型1 微信 2支付宝 3银行卡 4 现金 5无卡支付 6qq钱包 7百度钱包8京东钱包 10翼支付 11云闪付
     */
    private String payType = "";
    /**
     * 支付方式，刷卡支付、扫码支付、公众号支付等
     */
    private String payMethod = "";
    /**
     * 支付状态，1支付成功，2支付失败，3支付中，4已撤销，5退款成功，6退款失败
     */
    private String payStatusCode = "";
    /**
     * 对接方平台一订单号/交易单号
     */
    private String outTradeNo = "";
    /**
     * 退款订单号/退款原单号
     */
    private String outRefundNo = "";
    /**
     * 终端流水号，此处传商户发起预支付或公众号支付时所传入的交易流水号
     */
    private String terminalTrace = "";
    /**
     * 渠道订单号
     */
    private String channelTradeNo = "";
    /**
     * 清算日期yyyy-MM-dd
     */
    private String liquidationDate = "";
    /**
     * 用户标识，付款方用户id，“微信openid”、“支付宝账户”、“qq号”等
     */
    private String userId = "";
    /**
     * 银行卡类型0储蓄卡，1信用卡, 2未知
     */
    private String cardType = "";
    /**
     * 设备SN
     */
    private String attach = "";


    private static final String sep = ",";

    public String toTxtString() {
        return this.merchantNo + sep
                + this.terminalId + sep
                + this.terminalTime + sep
                + this.refundCompleteTime + sep
                + this.totalFee + sep
                + this.serviceFee + sep
                + this.refundFee + sep
                + this.balanceFee + sep
                + this.payType + sep
                + this.payMethod + sep
                + this.payStatusCode + sep
                + this.outTradeNo + sep
                + this.outRefundNo + sep
                + this.terminalTrace + sep
                + this.channelTradeNo + sep
                + this.liquidationDate + sep
                + this.userId + sep
                + this.cardType + sep
                + this.attach;
    }
}
