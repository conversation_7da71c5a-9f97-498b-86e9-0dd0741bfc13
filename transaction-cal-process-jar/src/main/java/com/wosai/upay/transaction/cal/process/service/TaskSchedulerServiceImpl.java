package com.wosai.upay.transaction.cal.process.service;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.exception.ExcelAnalysisException;
import com.alibaba.excel.read.builder.ExcelReaderBuilder;
import com.alibaba.excel.read.builder.ExcelReaderSheetBuilder;
import com.alibaba.excel.read.metadata.ReadSheet;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.alibaba.fastjson.JSON;
import com.wosai.app.dto.GroupSimpleInfo;
import com.wosai.app.dto.GroupUserMerchantAuthInfo;
import com.wosai.app.dto.QueryGroupMerchantAuthReq;
import com.wosai.app.service.GroupService;
import com.wosai.data.bean.BeanUtil;
import com.wosai.data.util.CollectionUtil;
import com.wosai.itsys.cornucopia.admin.service.IArOrderService;
import com.wosai.mpay.exception.MpayException;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.sales.core.model.Organization;
import com.wosai.sales.core.service.OrganizationService;
import com.wosai.upay.common.dao.DaoConstants;
import com.wosai.upay.common.util.JacksonUtil;
import com.wosai.upay.core.model.Merchant;
import com.wosai.upay.core.service.MerchantService;
import com.wosai.upay.transaction.cal.process.constant.StatementType;
import com.wosai.upay.transaction.cal.process.interceptor.CalculateMethodTimeConsuming;
import com.wosai.upay.transaction.cal.process.mapper.StatementPlanMapper;
import com.wosai.upay.transaction.cal.process.mapper.StatementTaskMapper;
import com.wosai.upay.transaction.cal.process.model.domain.StatementPlan;
import com.wosai.upay.transaction.cal.process.model.domain.StatementTask;
import com.wosai.upay.transaction.cal.process.model.domain.StatementTaskEx;
import com.wosai.upay.transaction.cal.process.model.dto.AgreementConfig;
import com.wosai.upay.transaction.cal.process.model.dto.StatementConfig;
import com.wosai.upay.transaction.cal.process.service.listener.HopeEduTradeRecordDataListener;
import com.wosai.upay.transaction.cal.process.util.*;
import com.wosai.upay.task.center.model.StatementTaskLog;
import com.wosai.upay.task.center.service.ExportService;
import com.wosai.upay.task.center.service.TaskLogService;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.TransactionService;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.ValueOperations;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.*;
import java.net.MalformedURLException;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.stream.Collectors;

@Slf4j
@Service
public class TaskSchedulerServiceImpl {

    public static final String HAIDILAO_GROUP_SN = "18800004252";

    public static final String XINXIWANG_GROUP_SN = "18800006143"; // 新希望集团sn

    @Value("${hopeedu.billKey}")
    private String hopeEduBillKey;

    @Value("${hopeedu.intoNo}")
    private String hopeEduIntoNo;

    @Value("${hopeedu.filePath}")
    private String hopeEduFilePath;

    private static final long ONE_DAY = 24 * 60 * 60 * 1000L;

    @Resource
    private OrderService orderService;

    @Resource
    private TransactionService transactionService;

    @Autowired
    private OssUtils ossUtils;

    @Autowired
    private ExportService exportService;

    @Autowired
    private TaskLogService taskLogService;

    @Autowired
    private StatementTaskMapper statementTaskMapper;

    @Autowired
    private StatementPlanMapper planMapper;

    @Autowired
    private MerchantService merchantService;

    @Autowired
    private GroupService groupService;


    private final String ossUrl = "http://wosai-images.oss-cn-hangzhou.aliyuncs.com/";

    @Autowired
    private OdpsUtils odpsUtils;

    @Autowired
    private OrganizationService organizationService;
    @Autowired
    private LarkService larkService;

    @Autowired
    private VariableThreadUtil variableThreadUtil;

    private static final int ONE_DAY_IN_MILLISECONDS = 24 * 60 * 60 * 1000;


    @Autowired
    private RedisTemplate redisTemplate;

    // 院校通请求限制缓存key
    private static final String HOPE_EDU_BILL_REQUEST_KEY = "transaction_cal_process:hopeedu:request";

    public static final Long HOPE_EDU_TIME_WINDOW = 10L;

    public static final Long HOPE_EDU_MAX_COUNT = 10L;

    /**
     * 定时任务
     *
     * @param timeEnd
     * @param timeStart
     */
    @CalculateMethodTimeConsuming(name = "对账单推送")
    public void sendStatements(long timeStart, long timeEnd,Integer type) {
        //获取需要执行的对账单任务
        List<StatementTask> statementTasks = statementTaskMapper.selectAllAliveTaskByType(type);
        if (!CollectionUtils.isEmpty(statementTasks)) {
            CountDownLatch countDownLatch = new CountDownLatch(statementTasks.size());
            for (StatementTask statementTask : statementTasks) {
                variableThreadUtil.getPushExecutorService().submit(()->{
                    try {
                        sendOneStatement(timeStart, timeEnd, statementTask);
                    }catch (Exception e){
                        log.error("推送对账单出错{}。error info{}", JacksonUtil.toJsonString(statementTask),e);
                    }finally {
                        countDownLatch.countDown();
                    }
                });
            }

            try {
                countDownLatch.await(2, TimeUnit.HOURS);
            } catch (InterruptedException e) {
                log.error("对账单推送异常,start:{},end:{},type:{},error:{}", timeStart, timeEnd, type, e.getMessage());
            }
        }
    }

    @CalculateMethodTimeConsuming(name = "isv对账单推送")
    public void sendStatementsForIsv(long timeStart, long timeEnd) {
        List<StatementTask> statementTasks = statementTaskMapper.selectAllAliveTaskByType(StatementType.TYPE_ISV);
        if(!CollectionUtils.isEmpty(statementTasks)){
            Map<String, StatementTaskEx> taskMapping = statementTasks.stream().collect(Collectors.toMap(StatementTask::getIsvCode, o -> new StatementTaskEx(o, planMapper.selectByPrimaryKey(o.getPlanId()))));
            Set<String> searchISVs = new HashSet<>();
            //需要加上单引号
            for (String isv : taskMapping.keySet()) {
                searchISVs.add("\'" + isv + "\'");
            }
            String isvCodes = String.join(",", searchISVs);
            String date = LocalDateTime.ofInstant(Instant.ofEpochMilli(timeStart), ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
            String isvTable = odpsUtils.buildIsvTable(isvCodes, date);
            Map<String, String> tunnel = odpsUtils.tunnel(isvTable, taskMapping.keySet(), date, taskMapping);
            try {
                for (Map.Entry<String, String> tunnelMap : tunnel.entrySet()) {
                    String isvCode = tunnelMap.getKey();
                    String filePath = tunnelMap.getValue();
                    File file = new File(filePath);
                    StatementTaskEx statementTaskEx = taskMapping.get(isvCode);
                    upload(statementTaskEx.getStatementTask(), statementTaskEx.getStatementPlan(), timeStart, timeEnd, null, file.toURI().toURL(), ".csv");
                    file.deleteOnExit();
                }
            } catch (IOException e) {
                throw new RuntimeException(e);
            }
        }
    }
    /**
     * 根据task执行任务
     *
     * @param timeStart
     * @param timeEnd
     * @param statementTask
     */
    public void sendOneStatement(long timeStart, long timeEnd, StatementTask statementTask) {
        StatementPlan statementPlan = planMapper.selectByPrimaryKey(statementTask.getPlanId());
        //执行对账单任务

        if (StringUtils.isNotBlank(statementTask.getPSn())) {
            //如果是集团商户,获取merchantId list
            GroupSimpleInfo groupSimpleInfo = groupService.getGroupBySn(statementTask.getPSn());
            String groupId = groupSimpleInfo.getId();
            List<GroupUserMerchantAuthInfo> groupUserMerchantAuths = groupService.getGroupUserMerchantAuths(new QueryGroupMerchantAuthReq().setGroup_id(groupId));
            List<String> merchantIds = new ArrayList<>();
            for (GroupUserMerchantAuthInfo groupUserMerchantAuth : groupUserMerchantAuths) {
                merchantIds.add(groupUserMerchantAuth.getMerchant_id());
            }
            String collect = merchantIds.stream().collect(Collectors.joining(",", "", ","));
            //放入merchantIds
            statementTask.setMerchantIds(collect);
        }

        if (statementTask.getType() == StatementType.TYPE_MERCHANT_AUTHORIZE
                || statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION
                || statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION_GROUP) {
            StatementConfig statementConfig = new StatementConfig();
            try {
                statementConfig = JSON.parseObject(statementPlan.getStatementConfig(), StatementConfig.class);
            } catch (Exception e) {
                log.error("解析statementConfig出错,statementPlan:{},error:{}", statementPlan, e.getMessage());
            }
            timeStart = timeStart + (statementConfig.getPeriodStartTime() == null ? 0L : statementConfig.getPeriodStartTime());
            timeEnd = timeEnd + (statementConfig.getPeriodEndTime() == null ? 0L : statementConfig.getPeriodEndTime());
            long now = System.currentTimeMillis();
            if (now < timeEnd) {
                //如果当前时间小于结束时间，则将结束时间减去一天
                timeStart = timeStart - ONE_DAY_IN_MILLISECONDS;
                timeEnd = timeEnd - ONE_DAY_IN_MILLISECONDS;
            }
        }
        if (statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION
                || statementTask.getType() == StatementType.TYPE_SHARING_BILL_PAYER
                || statementTask.getType() == StatementType.TYPE_SHARING_BILL_RECEIVER
                || statementTask.getType() == StatementType.TYPE_MERCHANT_AUTHORIZE) {//商户对账单


            //对账单
            //拆分多个merchant_id
            for (String merchantId : statementTask.getMerchantIds().split(",")) {
                doSafeSendStatement(statementTask, statementPlan, timeStart, timeEnd, merchantId);
            }

        } else if (statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION_GROUP) {//集团商户对账单
            doSafeSendStatement(statementTask, statementPlan, timeStart, timeEnd, null);
        } else if (statementTask.getType() == StatementType.TYPE_ISV) { //isv对账单
            doSafeSendStatement(statementTask, statementPlan, timeStart, timeEnd, null);
        }
    }

    /**
     * 手动补账单专用
     *
     * @param timeStart
     * @param timeEnd
     */
    public void sendStatementsByMerchantIds(long timeStart, long timeEnd, List<String> merchantIdList) {
        if (merchantIdList == null) {
            log.info("merchantIdList is null,return");
            return;
        }

        //获取需要执行的对账单任务
        for (StatementTask statementTask : statementTaskMapper.selectAllAliveTask()) {
            StatementPlan statementPlan = planMapper.selectByPrimaryKey(statementTask.getPlanId());
            //执行对账单任务
            //拆分多个merchant_id
            if (statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION) {
                if (statementTask.getMerchantIds().contains(",")) {
                    for (String merchantId : statementTask.getMerchantIds().split(",")) {
                        if (merchantIdList.contains(merchantId)) {
                            doSafeSendStatement(statementTask, statementPlan, timeStart, timeEnd, merchantId);
                        }
                    }
                } else {
                    if (merchantIdList.contains(statementTask.getMerchantIds())) {
                        doSafeSendStatement(statementTask, statementPlan, timeStart, timeEnd, statementTask.getMerchantIds());
                    }
                }
            }
        }
    }

    private void doSafeSendStatement(StatementTask statementTask, StatementPlan statementPlan, long startTime, long endTime, String merchantId) {
        try {
            log.info("执行对账单任务 statementTask: {} , statementPlan: {}, merchantId: {}", statementTask, statementPlan
                    , merchantId);
            doSendStatement(statementTask, statementPlan, startTime, endTime, merchantId);
        } catch (Throwable e) {
            log.error("对账单执行失败! statementTask: {} , statementPlan: {}, merchantId: {}", statementTask, statementPlan
                    , merchantId, e);
            larkWarning(statementTask.getId() + "", statementTask.toString());
        }
    }

    /**
     * 执行对账单任务
     *
     * @return
     */
    private void doSendStatement(StatementTask statementTask, StatementPlan statementPlan, long startTime, long endTime, String merchantId) throws IOException {
        //创建对账单导出任务
        Map<String, Object> taskInfo = new HashMap<>();
        Map<String, Object> requst = new HashMap<>();

        taskInfo.put("type", statementTask.getType());
        taskInfo.put("tile", statementPlan.getName() + "-" + statementTask.getId() + "-" + startTime + "-" + endTime);
        taskInfo.put("user_id", "script-v2");
        taskInfo.put("apply_system", "9");

        requst.put("date_start", startTime);
        requst.put("date_end", endTime);

        //设置statement_config
        StatementConfig statementConfig = JSON.parseObject(statementPlan.getStatementConfig(), StatementConfig.class);
        requst.put("statementConfig", statementConfig);
        requst.put("object_id", "sp-v2");
        if (statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION) {
            //对账单
            requst.put("merchant_id", merchantId);

        } else if (statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION_GROUP) {
            //集团对账单
            requst.put("group_sn", statementTask.getPSn());
            GroupSimpleInfo groupSimpleInfo = groupService.getGroupBySn(statementTask.getPSn());
            String groupId = groupSimpleInfo.getId();
            String groupName = groupSimpleInfo.getName();
            requst.put("group_id", groupId);
            requst.put("group_name", groupName);
            List<GroupUserMerchantAuthInfo> groupUserMerchantAuths = groupService.getGroupUserMerchantAuths(new QueryGroupMerchantAuthReq().setGroup_id(groupId));
            List<String> merchantIds = new ArrayList<>();
            for (GroupUserMerchantAuthInfo groupUserMerchantAuth : groupUserMerchantAuths) {
                merchantIds.add(groupUserMerchantAuth.getMerchant_id());
            }
            requst.put("merchant_ids", merchantIds);
            // 如果是海底捞，则改写object_id, 以便能获取到集团的对账单导出相关的配置
            if(HAIDILAO_GROUP_SN.equals(statementTask.getPSn())){
                requst.put("object_id", groupId);
                taskInfo.put("includes", "1,2,3");
            }


        } else if (statementTask.getType() == StatementType.TYPE_ISV) {
            sendIsv(statementTask, statementPlan, startTime, endTime);
            return;
        } else if(statementTask.getType() == StatementType.TYPE_SHARING_BILL_RECEIVER){
            Map<String, Object> merchant = merchantService.getMerchantByMerchantId(merchantId);
            requst.put("receiver_merchant_sn", MapUtil.getString(merchant, Merchant.SN));
            taskInfo.put("type", StatementTaskLog.TYPE_SHARING_BILL);
        } else if(statementTask.getType() == StatementType.TYPE_SHARING_BILL_PAYER){
            requst.put("merchant_id", merchantId);
            taskInfo.put("type", StatementTaskLog.TYPE_SHARING_BILL);
        } else if (statementTask.getType() == StatementType.TYPE_MERCHANT_AUTHORIZE) {
            requst.put("merchant_id", merchantId);
            taskInfo.put("type", StatementTaskLog.TYPE_TRANSACTION);
        }
        else {
            throw new IllegalArgumentException("taskType不正确!");
        }

        Map resultMap = exportService.createExportStatementTask(taskInfo, requst);

        if (MapUtil.isEmpty(resultMap)) {
            throw new RuntimeException("createExportStatementTask error!");
        }

        // 检查对账单任务状态
        String taskId = MapUtils.getString(resultMap, DaoConstants.ID);


        for (int i = 0; i < 500; i++) {
            try {
                Thread.sleep(1000 * 3);
            } catch (InterruptedException e) {
                log.error("sleep is error:", e);
            }

            Map taskApplyLog = taskLogService.getTaskApplyLog(taskId);
            int applyStatus = MapUtils.getIntValue(taskApplyLog, StatementTaskLog.APPLY_STATUS);

            if (applyStatus == StatementTaskLog.APPLY_STATUS_SUCCESS) {
                //导出成功
                log.info("taskApplyLog: {}", taskApplyLog);
                Map applyResult = StringUtil.getMapFromJsonObject(MapUtils.getObject(taskApplyLog, StatementTaskLog.APPLY_RESULT));

                String urlPath = MapUtils.getString(applyResult, "statement_oss_url", null);
                String statementOssUrl = ossUrl.concat(urlPath);
                String fileType;
                if (urlPath.contains("?")) {
                    fileType = urlPath.substring(urlPath.indexOf("."), urlPath.indexOf("?"));
                } else {
                    fileType = urlPath.substring(urlPath.indexOf("."));
                }
                log.info("statementOssUrl: {}", statementOssUrl);

                //发送ftp和email
                upload(statementTask, statementPlan, startTime, endTime, merchantId, new URL(statementOssUrl), fileType);

                break;

            } else if (applyStatus == StatementTaskLog.APPLY_STATUS_FAIL) {
                // 导出失败
                throw new RuntimeException("导出失败! " + JSON.toJSONString(taskApplyLog));
            }

        }

    }

    /**
     * isv账单
     */
    private void sendIsv(StatementTask statementTask, StatementPlan statementPlan, long startTime, long endTime) {
        String date = LocalDateTime.ofInstant(Instant.ofEpochMilli(startTime), ZoneId.systemDefault()).toLocalDate().format(DateTimeFormatter.ofPattern("yyyyMMdd"));
        AtomicReference<String> version = new AtomicReference<>("1.0");
        Optional.ofNullable(statementPlan.getStatementConfig()).map(config -> JSON.parseObject(config, StatementConfig.class))
                .map(StatementConfig::getVersion)
                .ifPresent(v -> version.set(v));
        String filepath = odpsUtils.getIsv(statementTask.getIsvCode(), date, version.get());
        if (StringUtils.isBlank(filepath)) {
            log.info("no trade found for isv {}",statementTask.getIsvCode());
            //isv当天没有交易
            return;
        }
        try {
            File file = new File(filepath);
            upload(statementTask, statementPlan, startTime, endTime, null, file.toURI().toURL(), ".csv");
            file.deleteOnExit();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    private void upload(StatementTask statementTask, StatementPlan statementPlan, long startTime, long endTime, String merchantId, URL url, String fileType) throws IOException {
        //文件名
        String sn = "";
        String name = "";
        if (statementTask.getType() == StatementTaskLog.TYPE_TRANSACTION
                || statementTask.getType() == StatementType.TYPE_SHARING_BILL_RECEIVER
                || statementTask.getType() == StatementType.TYPE_SHARING_BILL_PAYER
                || statementTask.getType() == StatementType.TYPE_MERCHANT_AUTHORIZE) {
            Map merchant = merchantService.getMerchant(merchantId);
            sn = MapUtils.getString(merchant, Merchant.SN);
            name = MapUtils.getString(merchant, Merchant.NAME);
        } else if (statementTask.getType() == StatementType.TYPE_ISV) {
            Map<String, Object> map = BeanUtil.getPart(organizationService
                            .getSimpleOrganization(CollectionUtil.hashMap("code", statementTask.getIsvCode()))
                    , Arrays.asList(
                            "id", Organization.NAME, Organization.CODE
                    ));
            sn = statementTask.getIsvCode();
            name = MapUtils.getString(map, Organization.NAME);
        } else {
            sn = statementTask.getPSn();
            GroupSimpleInfo groupSimpleInfo = groupService.getGroupBySn(sn);
            name = groupSimpleInfo.getName();
            // 特殊处理海底捞 需要转为csv发送, 此处转换 url
            if(HAIDILAO_GROUP_SN.equals(sn)){
                url = conventToCsv(url);
                fileType = ".csv";
            } else if (XINXIWANG_GROUP_SN.equals(sn)) {
                url = conventToXinxiwangTxt(url);
                fileType = ".txt";
            }
        }
        // 添加分账对账单名称，和交易对账单做区分，防止被覆盖
        String typeName = "";
        if(statementTask.getType() == StatementType.TYPE_SHARING_BILL_RECEIVER || statementTask.getType() == StatementType.TYPE_SHARING_BILL_PAYER){
            typeName = "_分账对账单";
        }
        String filename = String.format("%s%s", String.format("%s_%s%s-%s-%s", sn, name, typeName,
                DateTimeUtil.format(startTime), DateTimeUtil.format(endTime)), fileType);

        //处理非法字符
        filename = filename.replace("/", "");

        log.info("filename: {}", filename);

        if (XINXIWANG_GROUP_SN.equals(sn)) {
            // 新希望不发FTP 不发邮件
            return;
        }

        //发送ftp
        if (StringUtils.isNotEmpty(statementPlan.getAgreementConfig())) {
            AgreementConfig agreementConfig = JSON.parseObject(statementPlan.getAgreementConfig(), AgreementConfig.class);
            try (InputStream input = url.openStream()) {
                String host = agreementConfig.getHost();
                String username = agreementConfig.getUsername();
                String password = agreementConfig.getPassword();
                int port = agreementConfig.getPort();

                // 格式化子目录格式
                // date/merchant; merchant/date; date; merchant
                String folderName = agreementConfig.getFolderName()
                        .replace("date", new SimpleDateFormat("yyyyMMdd").format(startTime))
                        .replace("month", new SimpleDateFormat("yyyyMM").format(startTime))
                        .replace("merchant", sn);

                String path = agreementConfig.getPath() + "/" + folderName;
                log.info("ftp path: {}", path);

                boolean success;
                if (statementPlan.getAgreementType() == 0) {
                    //ftp
                    success = FtpUtil.uploadFileFTP(host, username, password, port, path, filename, input);
                } else {
                    //sftp
                    success = FtpUtil.uploadFileSFTP(host, username, password, port, path, filename, input);
                }
                if (!success) {
                    throw new RuntimeException("ftpUpload failed");
                }
            }
        }


        //发送邮件
        if (StringUtils.isNotEmpty(statementPlan.getEmail())) {
            for (String email : statementPlan.getEmail().split(",")) {
                try (InputStream input = url.openStream()) {
                    EmailUtil.sendToFile("商户对账单", "商户对账单见附件", email, null, filename, input);
                }
            }
        }
    }


    private void larkWarning(String applySn, String message) {
        String text = String.format(" %s 对账单任务生成失败 具体信息:%s", applySn, message);
        larkService.larkMessage(text);
    }


    @SneakyThrows
    private URL conventToCsv(URL url){
        String fileFullPath = "../" + UUID.randomUUID() + ".csv";
        String absolutePath = new File(fileFullPath).getAbsolutePath();
        try (InputStream in = new BufferedInputStream(url.openStream());
             FileOutputStream fileOutputStream = new FileOutputStream(absolutePath)) {
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = in.read(buffer, 0, buffer.length)) != -1) {
                fileOutputStream.write(buffer, 0, bytesRead);
            }
        }
        ExcelUtil.convertExcelToCsv(url, 1, 5, absolutePath);
        File file = new File(absolutePath);
        file.deleteOnExit();
        return file.toURI().toURL();
    }


    private URL conventToXinxiwangTxt(URL url) {
        HopeEduTradeRecordDataListener hopeEduTradeRecordDataListener = null;
        try (InputStream inputStream = url.openStream()) {

            // 拼接文件路径
            String date = new SimpleDateFormat("yyyyMMdd").format(new Date(System.currentTimeMillis() - ONE_DAY));
            String[] fs = url.getFile().split("_");
            if (fs.length > 3) {
                date = fs[2].replace("-", "");
            }
            String keySign = getKeySign(date);
            String filePath = String.format("%s/order/%s/%s/%s/%s_%s.txt", hopeEduFilePath, date, hopeEduIntoNo, keySign, hopeEduIntoNo, date);

            ExcelTypeEnum excelType = url.getFile().contains(".xlsx") ? ExcelTypeEnum.XLSX : ExcelTypeEnum.XLS;
            // 读取Excel转换
            hopeEduTradeRecordDataListener = new HopeEduTradeRecordDataListener(filePath, orderService, transactionService, ossUtils);
            ExcelReader excelReader = new ExcelReader(inputStream, excelType, hopeEduTradeRecordDataListener);

            ReadSheet readSheet = new ReadSheet(1);
            readSheet.setAutoTrim(false);
            excelReader.read(readSheet);

            File file = new File(filePath);
            file.deleteOnExit();
            return file.toURI().toURL();
        } catch (ExcelAnalysisException e) {
            log.error("hope edu handle fail. "+ e.getMessage(), e); // Can not find sheet:1, 文件不存在sheet 1
            if (Objects.nonNull(hopeEduTradeRecordDataListener)) {
                hopeEduTradeRecordDataListener.writeTxtFile(new ArrayList<>());
            }
        } catch (IOException e) {
            log.error("hope edu handle fail. "+ e.getMessage(), e);
        }
        return url;
    }

    public String getKeySign(String date) {
        String signStr = "day="+date+"&inst_no="+hopeEduIntoNo+"&key="+hopeEduBillKey;

        return DigestUtils.md5Hex(signStr).toLowerCase();
    }

    public boolean hopeEduBillAllowRequest() {
        ValueOperations<String, Integer> ops = redisTemplate.opsForValue();

        // 使用 increment 操作原子性地增加计数器
        Long count = ops.increment(HOPE_EDU_BILL_REQUEST_KEY, 1);
        System.out.println("count: " + count);
        if (count == 1) {
            // 第一次访问，设置过期时间
            System.out.println("设置超时时间");
            redisTemplate.expire(HOPE_EDU_BILL_REQUEST_KEY, HOPE_EDU_TIME_WINDOW, TimeUnit.MINUTES);
        }
        return count <= HOPE_EDU_MAX_COUNT;
    }
}
