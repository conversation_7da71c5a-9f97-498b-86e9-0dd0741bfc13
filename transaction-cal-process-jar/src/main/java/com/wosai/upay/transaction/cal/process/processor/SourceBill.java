package com.wosai.upay.transaction.cal.process.processor;

import com.alibaba.fastjson.JSON;
import com.wosai.upay.transaction.cal.process.model.dto.SummaryBillRemark;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;
import java.util.Map;

/**
 * SourceBill
 *
 * <AUTHOR>
 * @date 2019-09-12 16:36
 */
@Accessors(chain = true)
@Data
public class SourceBill {

    /**
     * 任务 id
     */
    private String taskId;

    /**
     * 上游账单类型
     */
    private Integer sourceBillType;

    /**
     * 服务商唯一标识（ID/PID/NO）
     */
    private String serviceProviderId;

    /**
     * 服务商名称（或简称）
     */
    private String serviceProviderName;

    /**
     * 交易月份（必填）（格式：201908）
     */
    private String tradeMonth;

    /**
     * 账单详情
     */
    private List<Detail> detailList;

    /**
     * 备注
     */
    private String remark;

    /**
     * 政策id
     */
    private String policyId;

    /**
     * 对方名称
     */
    private String ncName;

    /**
     * 我方名称
     */
    private String companyName;

    /**
     * 业务性质
     */
    private String propertyId;

    /**
     * 上游账单详情类
     */
    @Data
    public static class Detail {

        /**
         * 账单明细行编号（不包含头信息，行号为明细行从 1 起始的编号）
         */
        private Integer subNum;

        /**
         * 收钱吧商户号
         */
        private String sqbMerchantSn;

        /**
         * 收钱吧商户名称
         */
        private String sqbMerchantName;

        /**
         * 上游商户唯一标识（ID/PID/NO）
         */
        private String sourceMerchantId;

        /**
         * 上游商户名称（或简称）
         */
        private String sourceMerchantName;

        /**
         * 上游二级商户（门店）唯一标识（ID/PID/NO）
         */
        private String sourceLevel2MerchantId;

        /**
         * 上游二级商户（门店）名称
         */
        private String sourceLevel2MerchantName;

        /**
         * 维度（度量）（比如对于花呗分期，该值为花呗分期期数） 拉卡拉的时候为银联服务费
         */
        private String dimension;

        /**
         * 有效交易笔数
         */
        private Integer sourceValidTradeNum = 0;

        /**
         * 有效交易金额（单位：分）
         */
        private Long sourceValidTradeAmount = 0L;

        /**
         * 有效退款笔数
         */
        private Integer sourceValidRefundNum = 0;

        /**
         * 有效退款金额（单位：分）
         */
        private Long sourceValidRefundAmount = 0L;

        /**
         * 结算依据类型
         */
        private String sourceSettlementBasisType;

        /**
         * 结算依据（单位：分）
         */
        private Long sourceSettlementBasis = 0L;

        /**
         * 商户费率（小数格式，非百分制格式）
         */
        private String sourceMerchantFeeRate;

        /**
         * 结算费率（小数格式，非百分制格式）
         */
        private String sourceSettlementFeeRate = "0.0";

        /**
         * 封顶费率
         */
        private String cappingRate;

        /**
         * 应结算总金额（单位：分）
         */
        private Long sourceSettlementAmount = 0L;

        /**
         * 备注
         */
        private String remark;

        /**
         * 服务商唯一标识（ID/PID/NO）
         */
        private String serviceProviderId;

        private Map<String, Object> extraParams;

        /**
         * 未结算原因
         */
        private String notSettlementReason;

        /**
         * 支付源
         */
        private String source;

        /**
         * 支付源头成本（单位：分）
         */
        private Long sourceCost = 0L;

        /**
         * 渠道成本（单位：分）
         */
        private Long channelCost = 0L;

        /**
         * 商户手续费（单位：分）
         */
        private Long merchantCommission = 0L;

        /**
         * 设备号
         */
        private String deviceNo;

        public Integer getSubNum() {
            return subNum;
        }

        public void setSubNum(Integer subNum) {
            this.subNum = subNum;
        }

        public String getSourceMerchantId() {
            return sourceMerchantId;
        }

        public void setSourceMerchantId(String sourceMerchantId) {
            if (sourceMerchantId != null) {
                sourceMerchantId = sourceMerchantId.replace("\t", "");
            }
            this.sourceMerchantId = sourceMerchantId;
        }

        public String getSourceMerchantName() {
            return sourceMerchantName;
        }

        public void setSourceMerchantName(String sourceMerchantName) {
            if (sourceMerchantName != null) {
                sourceMerchantName = sourceMerchantName.replace("\t", "");
            }
            this.sourceMerchantName = sourceMerchantName;
        }

        public String getSourceLevel2MerchantId() {
            return sourceLevel2MerchantId;
        }

        public void setSourceLevel2MerchantId(String sourceLevel2MerchantId) {
            if (sourceLevel2MerchantId != null) {
                sourceLevel2MerchantId = sourceLevel2MerchantId.replace("\t", "");
            }
            this.sourceLevel2MerchantId = sourceLevel2MerchantId;
        }

        public String getSourceLevel2MerchantName() {
            return sourceLevel2MerchantName;
        }

        public void setSourceLevel2MerchantName(String sourceLevel2MerchantName) {
            if (sourceLevel2MerchantName != null) {
                sourceLevel2MerchantName = sourceLevel2MerchantName.replace("\t", "");
            }
            this.sourceLevel2MerchantName = sourceLevel2MerchantName;
        }

        public String getDimension() {
            return dimension;
        }

        public void setDimension(String dimension) {
            if (dimension != null) {
                dimension = dimension.replace("\t", "");
            }
            this.dimension = dimension;
        }

        public Integer getSourceValidTradeNum() {
            return sourceValidTradeNum;
        }

        public void setSourceValidTradeNum(Integer sourceValidTradeNum) {
            this.sourceValidTradeNum = sourceValidTradeNum;
        }

        public Long getSourceValidTradeAmount() {
            return sourceValidTradeAmount;
        }

        public void setSourceValidTradeAmount(Long sourceValidTradeAmount) {
            this.sourceValidTradeAmount = sourceValidTradeAmount;
        }

        public Long getSourceValidRefundAmount() {
            return sourceValidRefundAmount;
        }

        public void setSourceValidRefundAmount(Long sourceValidRefundAmount) {
            this.sourceValidRefundAmount = sourceValidRefundAmount;
        }

        public Integer getSourceValidRefundNum() {
            return sourceValidRefundNum;
        }

        public void setSourceValidRefundNum(Integer sourceValidRefundNum) {
            this.sourceValidRefundNum = sourceValidRefundNum;
        }

        public String getSourceSettlementBasisType() {
            return sourceSettlementBasisType;
        }

        public void setSourceSettlementBasisType(String sourceSettlementBasisType) {
            if (sourceSettlementBasisType != null) {
                sourceSettlementBasisType = sourceSettlementBasisType.replace("\t", "");
            }
            this.sourceSettlementBasisType = sourceSettlementBasisType;
        }

        public Long getSourceSettlementBasis() {
            return sourceSettlementBasis;
        }

        public void setSourceSettlementBasis(Long sourceSettlementBasis) {
            this.sourceSettlementBasis = sourceSettlementBasis;
        }

        public String getSourceMerchantFeeRate() {
            return sourceMerchantFeeRate;
        }

        public void setSourceMerchantFeeRate(String sourceMerchantFeeRate) {
            if (sourceMerchantFeeRate != null) {
                sourceMerchantFeeRate = sourceMerchantFeeRate.replace("\t", "");
            }
            this.sourceMerchantFeeRate = sourceMerchantFeeRate;
        }

        public String getSourceSettlementFeeRate() {
            return sourceSettlementFeeRate;
        }

        public void setSourceSettlementFeeRate(String sourceSettlementFeeRate) {
            if (sourceSettlementFeeRate != null) {
                sourceSettlementFeeRate = sourceSettlementFeeRate.replace("\t", "");
            }
            this.sourceSettlementFeeRate = sourceSettlementFeeRate;
        }

        public String getCappingRate() {
            return cappingRate;
        }

        public void setCappingRate(String cappingRate) {
            if (cappingRate != null) {
                cappingRate = cappingRate.replace("\t", "");
            }
            this.cappingRate = cappingRate;
        }

        public Long getSourceSettlementAmount() {
            return sourceSettlementAmount;
        }

        public void setSourceSettlementAmount(Long sourceSettlementAmount) {
            this.sourceSettlementAmount = sourceSettlementAmount;
        }

        public String getRemark() {
            return remark;
        }

        public void setRemark(String remark) {
            if (remark != null) {
                remark = remark.replace("\t", "");
            }
            this.remark = remark;
        }

        public String getServiceProviderId() {
            return serviceProviderId;
        }

        public void setServiceProviderId(String serviceProviderId) {
            if (serviceProviderId != null) {
                serviceProviderId = serviceProviderId.replace("\t", "");
            }
            this.serviceProviderId = serviceProviderId;
        }

        public Map<String, Object> getExtraParams() {
            return extraParams;
        }

        public void setExtraParams(Map<String, Object> extraParams) {
            this.extraParams = extraParams;
        }

        @Override
        public String toString() {
            return "Detail{" +
                    "subNum=" + subNum +
                    ", sourceMerchantId='" + sourceMerchantId + '\'' +
                    ", sourceMerchantName='" + sourceMerchantName + '\'' +
                    ", sourceLevel2MerchantId='" + sourceLevel2MerchantId + '\'' +
                    ", sourceLevel2MerchantName='" + sourceLevel2MerchantName + '\'' +
                    ", dimension='" + dimension + '\'' +
                    ", sourceValidTradeNum=" + sourceValidTradeNum +
                    ", sourceValidTradeAmount=" + sourceValidTradeAmount +
                    ", sourceValidRefundNum=" + sourceValidRefundNum +
                    ", sourceValidRefundAmount=" + sourceValidRefundAmount +
                    ", sourceSettlementBasisType='" + sourceSettlementBasisType + '\'' +
                    ", sourceSettlementBasis=" + sourceSettlementBasis +
                    ", sourceMerchantFeeRate='" + sourceMerchantFeeRate + '\'' +
                    ", sourceSettlementFeeRate='" + sourceSettlementFeeRate + '\'' +
                    ", cappingRate='" + cappingRate + '\'' +
                    ", sourceSettlementAmount=" + sourceSettlementAmount +
                    ", remark='" + remark + '\'' +
                    ", extraParams='" + extraParams + '\'' +
                    '}';
        }
    }

    public String getTaskId() {
        return taskId;
    }

    public void setTaskId(String taskId) {
        this.taskId = taskId;
    }

    public Integer getSourceBillType() {
        return sourceBillType;
    }

    public void setSourceBillType(Integer sourceBillType) {
        this.sourceBillType = sourceBillType;
    }

    public String getServiceProviderId() {
        return serviceProviderId;
    }

    public void setServiceProviderId(String serviceProviderId) {
        if (serviceProviderId != null) {
            serviceProviderId = serviceProviderId.replace("\t", "");
        }
        this.serviceProviderId = serviceProviderId;
    }

    public String getServiceProviderName() {
        return serviceProviderName;
    }

    public void setServiceProviderName(String serviceProviderName) {
        if (serviceProviderName != null) {
            serviceProviderName = serviceProviderName.replace("\t", "");
        }
        this.serviceProviderName = serviceProviderName;
    }

    public String getTradeMonth() {
        return tradeMonth;
    }

    public void setTradeMonth(String tradeMonth) {
        if (tradeMonth != null) {
            tradeMonth = tradeMonth.replace("\t", "");
        }
        this.tradeMonth = tradeMonth;
    }

    public List<Detail> getDetailList() {
        return detailList;
    }

    public void setDetailList(List<Detail> detailList) {
        this.detailList = detailList;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        if (remark != null) {
            remark = remark.replace("\t", "");
        }
        this.remark = remark;
    }

    public String getPolicyId() {
        return policyId;
    }

    public void setPolicyId(String policyId) {
        if (policyId != null) {
            policyId = policyId.replace("\t", "");
        }
        this.policyId = policyId;
    }

    public String getNcName() {
        return ncName;
    }

    public void setNcName(String ncName) {
        if (ncName != null) {
            ncName = ncName.replace("\t", "");
        }
        this.ncName = ncName;
    }

    public String getCompanyName() {
        return companyName;
    }

    public void setCompanyName(String companyName) {
        if (companyName != null) {
            companyName = companyName.replace("\t", "");
        }
        this.companyName = companyName;
    }

    public String getPropertyId() {
        return propertyId;
    }

    public void setPropertyId(String propertyId) {
        if (propertyId != null) {
            propertyId = propertyId.replace("\t", "");
        }
        this.propertyId = propertyId;
    }

    public SummaryBillRemark queryRemark() {
        return JSON.parseObject(this.getRemark(), SummaryBillRemark.class);
    }
}
