package com.wosai.upay.transaction.cal.process.service;

import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategy;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import com.googlecode.jsonrpc4j.spring.AutoJsonRpcServiceImpl;
import com.wosai.mpay.util.JsonUtil;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.transaction.cal.process.model.response.ScreenData;
import com.wosai.upay.transaction.cal.process.util.DateTimeUtil;
import lombok.SneakyThrows;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * Created by w<PERSON><PERSON><PERSON><PERSON> on 2024/1/8.
 */
@Service
@AutoJsonRpcServiceImpl
public class DigitalScreenServiceImpl implements DigitalScreenService{

    public static ObjectMapper objectMapper;
    static {
        objectMapper = new ObjectMapper();
        objectMapper.setPropertyNamingStrategy(PropertyNamingStrategy.SNAKE_CASE);
        objectMapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    }

    private Cache<String, ScreenData> cache = CacheBuilder.newBuilder()
            .expireAfterWrite(1, TimeUnit.MINUTES)
            .maximumSize(1000) // 设置最大缓存大小为 1000 个条目
            .build();

    @Autowired
    private JdbcTemplate jdbcTemplate;

    @SneakyThrows
    @Override
    public ScreenData getScreenData(String day) {
        if(day == null){
            //未传，则取当天
            day = DateTimeUtil.format(System.currentTimeMillis());
        }
        String finalDay = day;
        return cache.get(day, () -> processGetScreenData(finalDay));
    }

    private ScreenData processGetScreenData(String day) {
        String yesterday = getYesterdayOfDate(day); // yyyyMMdd
        // 获取日数据
        ScreenData data = getDayData(yesterday);
        data.setDay(day);
        // 获取累计数据
        ScreenData yearData = getYearData(yesterday);
        data.setAccumulateCustomerUserCount(yearData.getAccumulateCustomerUserCount());
        data.setAccumulateMerchantCount(yearData.getAccumulateMerchantCount());
        data.setAccumulateTradeCount(yearData.getAccumulateTradeCount());
        data.setAccumulateTradeAmount(yearData.getAccumulateTradeAmount());
        data.setAccumulateCorpTradeAmount(yearData.getAccumulateCorpTradeAmount());
        return data;
    }


    @SneakyThrows
    public ScreenData getDayData(String day){
        String sql = "select * from digital_screen_daily_pay_bank_data where day <= '" + day + "' order by day desc limit 1;";
        Map<String, Object> result = jdbcTemplate.queryForMap(sql);
        result.put("corp_top_cities_trade", JsonUtil.jsonStrToObject((String) result.get("corp_top_cities_trade"), Map.class));
        result.put("accumulate_corp_merchant_count", result.get("corp_year_merchant_count"));
        masking(result);
        masking(MapUtil.getMap(result, "corp_top_cities_trade"));
        return objectMapper.readValue(JsonUtil.toJsonStr(result).getBytes(), ScreenData.class);
    }

    @SneakyThrows
    /**
     * 只返回累计的部分字段
     */
    public ScreenData getYearData(String day){
        String yearStart = day.substring(0, 4) + "0101";
        String sql = "select\n" +
                "    sum(new_customer_user_count) as accumulate_customer_user_count\n" +
                "    , sum(new_merchant_count) as accumulate_merchant_count\n" +
                "    , sum(trade_count) as accumulate_trade_count\n" +
                "    , sum(trade_amount) as accumulate_trade_amount\n" +
                "    , sum(corp_trade_amount) as accumulate_corp_trade_amount\n" +
                "\n" +
                "from digital_screen_daily_pay_bank_data where day <= '"+day+"' and day >= '"+yearStart+"'\n";
        Map<String, Object> result = jdbcTemplate.queryForMap(sql);
        masking(result);
        return objectMapper.readValue(JsonUtil.toJsonStr(result).getBytes(), ScreenData.class);
    }

    /**
     * 数据脱敏, 上浮20%
     */
    private void masking(Map<String,Object> data){
        for (String key : data.keySet()) {
            Object val = data.get(key);
            if(val instanceof Number) {
                data.put(key, getLongValue(((Number) val).doubleValue() * 1.2));
            }
        }
    }

    /**
     *
     * @param dateString
     * @return yyyyMMdd
     */
    public static String getYesterdayOfDate(String dateString) {
        // 将日期字符串解析为 LocalDate 对象
        LocalDate date = LocalDate.parse(dateString);

        // 获取昨天的日期
        LocalDate yesterday = date.minusDays(1);

        // 将昨天的日期转换为指定格式的字符串
        String yesterdayString = yesterday.format(DateTimeFormatter.ofPattern("yyyyMMdd"));

        return yesterdayString;
    }

    private Long getLongValue(Double d){
        return d.longValue();
    }

}
