package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTask;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskExample;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs;

import java.util.Date;
import java.util.List;

import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithType;
import org.apache.ibatis.annotations.Param;

public interface TbSourceBillInputTaskMapper {
    long countByExample(TbSourceBillInputTaskExample example);

    int deleteByExample(TbSourceBillInputTaskExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbSourceBillInputTaskWithBLOBs record);

    int insertSelective(TbSourceBillInputTaskWithBLOBs record);

    List<TbSourceBillInputTaskWithBLOBs> selectByExampleWithBLOBs(TbSourceBillInputTaskExample example);

    List<TbSourceBillInputTask> selectByExample(TbSourceBillInputTaskExample example);

    TbSourceBillInputTaskWithBLOBs selectByPrimaryKey(Integer id);

    List<TbSourceBillInputTaskWithType> selectJoinBillTypeList(@Param("sourceBillType") Integer sourceBillType,
                                                               @Param("sourceBillCompany") Integer sourceBillCompany,
                                                               @Param("sourceBillClassify") Integer sourceBillClassify,
                                                               @Param("taskStatus") Integer taskStatus,
                                                               @Param("createAtStart") Date createAtStart,
                                                               @Param("createAtEnd") Date createAtEnd,
                                                               @Param("tradeDateMonth") String tradeDateMonth);

    int updateByExampleSelective(@Param("record") TbSourceBillInputTaskWithBLOBs record, @Param("example") TbSourceBillInputTaskExample example);

    int updateByExampleWithBLOBs(@Param("record") TbSourceBillInputTaskWithBLOBs record, @Param("example") TbSourceBillInputTaskExample example);

    int updateByExample(@Param("record") TbSourceBillInputTask record, @Param("example") TbSourceBillInputTaskExample example);

    int updateByPrimaryKeySelective(TbSourceBillInputTaskWithBLOBs record);

    int updateByPrimaryKeyWithBLOBs(TbSourceBillInputTaskWithBLOBs record);

    int updateByPrimaryKey(TbSourceBillInputTask record);
}