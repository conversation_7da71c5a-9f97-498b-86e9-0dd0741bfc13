package com.wosai.upay.transaction.cal.process.service.listener;

import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.wosai.data.bean.BeanUtil;
import com.wosai.mpay.api.hopeedu.utils.HopeEduUtil;
import com.wosai.mpay.util.StringUtils;
import com.wosai.pantheon.util.MapUtil;
import com.wosai.upay.task.center.model.Payment;
import com.wosai.upay.task.center.model.Transaction;
import com.wosai.upay.transaction.cal.process.model.domain.HopeEduOrderRecord;
import com.wosai.upay.transaction.cal.process.model.domain.TradeRecordHeader;
import com.wosai.upay.transaction.cal.process.util.OssUtils;
import com.wosai.upay.transaction.model.Order;
import com.wosai.upay.transaction.service.OrderService;
import com.wosai.upay.transaction.service.TransactionService;
import lombok.extern.slf4j.Slf4j;

import java.io.BufferedWriter;
import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.*;

/**
 * @version 1.0
 * @author: yuhai
 * @program: transaction-cal-process
 * @className TradeRecordDataListener
 * @description: 院校通对账单以订单维度输出，因此要把订单号相同的交易流水合并
 * @create: 2025-05-26 14:59
 **/
@Slf4j
public class HopeEduTradeRecordDataListener extends AnalysisEventListener<List<String>> {

    private static final String ORDER_TYPE_REFUND = "退款";
    private static final String ORDER_TYPE_CANCEL = "撤单";
    private static final String ORDER_TYPE_CONSUME_CANCEL = "预授权完成撤销";
    private static final String ORDER_TYPE_STORE_IN_RETURN = "储值账户充值退款";
    private static final String ORDER_TYPE_STORE_PAY_RETURN = "储值账户核销退款";

    private OssUtils ossUtils;

    private String filePath;

    private OrderService orderService;

    private TransactionService transactionService;

    private List<List<String>> records = new ArrayList<>();

    private TradeRecordHeader header = new TradeRecordHeader();

    /**
     * 有退款订单暂存
     * key：订单编号
     * value：订单信息
     */
    private Map<String, Map<String, Object>> refundOrders = new HashMap<>();

    /**
     * 退款交易暂存
     * key：订单编号
     * value: 退款交易列表
     */
    private Map<String, List<Map<String, Object>>> refundTransactions = new HashMap<>();

    private int currentLine = 0;

    private static final int HEADER_LINE = 5;

    public HopeEduTradeRecordDataListener(String filePath, OrderService orderService,
                                          TransactionService transactionService, OssUtils ossUtils) {
        this.ossUtils = ossUtils;
        this.filePath = filePath;
        this.orderService = orderService;
        this.transactionService = transactionService;
    }

    @Override
    public void invoke(List<String> record, AnalysisContext analysisContext) {
        currentLine++;
        if (currentLine < HEADER_LINE) {
            return;
        } else if (currentLine == HEADER_LINE){
            // 添加标题头
            formatHeader(record);
            return;
        }

        records.add(record);
    }

    @Override
    public void doAfterAllAnalysed(AnalysisContext analysisContext) {
        log.info("hope edu 所有数据解析完成！总共解析到 " + records.size() + " 条数据。");

        List<HopeEduOrderRecord> hopeEduOrderRecords = new ArrayList<>();

        for (List<String> record : records) {
            hopeEduOrderRecords = formatRecord(record, hopeEduOrderRecords);
        }

        String filePath = writeTxtFile(hopeEduOrderRecords);
        records.clear();
        refundOrders.clear();
        refundTransactions.clear();
        log.info("hope edu file path: {}", filePath);
    }


    private List<HopeEduOrderRecord> formatRecord(List<String> record, List<HopeEduOrderRecord> hopeEduOrderRecords) {
        if (checkIgnore(getRecordValue(record, header.getPaymentChannel()))) {
            return hopeEduOrderRecords;
        }

        String tradeType = getRecordValue(record, header.getTradeType());
        String termincalSn = getRecordValue(record, header.getTerminalSn());
        String termId = getRecordValue(record, header.getTermId());
//        String shortTermId = HopeEduUtil.convertToTerminal(termId);
        String orderSn = getRecordValue(record, header.getMerchantOrderNo());
        String tradeSn = getRecordValue(record, header.getTradeSn());

        HopeEduOrderRecord hopeEduOrderRecord = new HopeEduOrderRecord();
        hopeEduOrderRecord.setDate(getRecordValue(record, header.getTradeDate()));

        String providerMchId = getRecordValue(record, header.getProviderMchId());
        hopeEduOrderRecord.setMerchantNo(providerMchId);
        hopeEduOrderRecord.setTerminalId(termId);
        hopeEduOrderRecord.setTerminalTime(getRecordValue(record, header.getTradeDate()) + " " + getRecordValue(record, header.getTradeTime()));

        if (ORDER_TYPE_REFUND.equals(tradeType)
                || ORDER_TYPE_CANCEL.equals(tradeType)
                || ORDER_TYPE_CONSUME_CANCEL.equals(tradeType)
                || ORDER_TYPE_STORE_IN_RETURN.equals(tradeType)
                || ORDER_TYPE_STORE_PAY_RETURN.equals(tradeType)) {
            // 退款交易需记录，同时暂存订单信息，供后续支付订单写入当天退款金额，交易列表也需要暂存，供后续获取退款交易信息
            Map<String, Object> order = getOrder(orderSn);
            if (Objects.isNull(order)) {
                return hopeEduOrderRecords;
            }
            Map<String, Object> transaction = getRefundTransaction(tradeSn, orderSn);
            if (Objects.isNull(transaction)) {
                return hopeEduOrderRecords;
            }

            // 退款交易
            hopeEduOrderRecord.setRefundCompleteTime(hopeEduOrderRecord.getTerminalTime());
            hopeEduOrderRecord.setTotalFee(order.get(Order.ORIGINAL_TOTAL).toString());

            // ⼿续费⾦额，单位分
            Long serviceFee = getServiceFee(transaction);
            hopeEduOrderRecord.setServiceFee(serviceFee.toString());

            // 退款⾦额，单位分
            Long refundFee = getRefundFee(transaction);
            hopeEduOrderRecord.setRefundFee(refundFee.toString());

            // 结余⾦额，单位分
            Long balanceFee = - (refundFee + serviceFee);
            hopeEduOrderRecord.setBalanceFee(balanceFee.toString());

            hopeEduOrderRecord.setOutRefundNo(orderSn);
            // 获取退款订单号，由于院校通要求订单号不能与支付订单号一致因此订单号取值为退款流水号
            hopeEduOrderRecord.setOutTradeNo(tradeSn);
            hopeEduOrderRecord.setPayStatusCode("5");
        }else {
            hopeEduOrderRecord.setRefundCompleteTime("");
            hopeEduOrderRecord.setTotalFee(yuan2cents(getRecordValue(record, header.getCollectionAmount())));
            hopeEduOrderRecord.setServiceFee(yuan2cents(getRecordValue(record, header.getPaymentHandlingFee())));

            Long refundFee = calcRefundFee(orderSn);
            hopeEduOrderRecord.setRefundFee(refundFee.toString());
            hopeEduOrderRecord.setBalanceFee(calcBalanceFee(hopeEduOrderRecord.getTotalFee(), hopeEduOrderRecord.getServiceFee()));
            hopeEduOrderRecord.setOutRefundNo("");
            hopeEduOrderRecord.setPayStatusCode("1");
            hopeEduOrderRecord.setOutTradeNo(orderSn);
        }

        hopeEduOrderRecord.setPayType(formatHopeEduPayType(getRecordValue(record, header.getPaymentChannel())));
        hopeEduOrderRecord.setPayMethod(formatHopeEduPayMethod(getRecordValue(record, header.getTradingMode())));
        hopeEduOrderRecord.setTerminalTrace(tradeSn);
        hopeEduOrderRecord.setChannelTradeNo(getRecordValue(record, header.getPaymentChannelOrderNo()));
        hopeEduOrderRecord.setLiquidationDate(getRecordValue(record, header.getTradeDate()));
        hopeEduOrderRecord.setUserId(getRecordValue(record, header.getPayerAccount()));
        hopeEduOrderRecord.setCardType("2");
        hopeEduOrderRecord.setAttach(getRecordValue(record, header.getDeviceId()));

        hopeEduOrderRecords.add(hopeEduOrderRecord);

        return hopeEduOrderRecords;
    }

    public String writeTxtFile(List<HopeEduOrderRecord> records) {
        String content = "";
        content = buildHopeEduTxtContent(records);
        String fileFullPath = filePath;
        Path filePathObj = Paths.get(fileFullPath);
        Path parentDir = filePathObj.getParent();
        if (parentDir != null && !Files.exists(parentDir)) {
            try {
                Files.createDirectories(parentDir); // 如果目录不存在，则创建目录
            } catch (IOException e) {
                log.error("hope edu 创建目录异常:", e);
            }
        }
        try (BufferedWriter writer = new BufferedWriter(new FileWriter(filePath))) {
            writer.write(content);
            writer.flush();
        } catch (Exception e) {
            log.error("hope edu 写入文件异常:", e);
        }
        log.info("hope edu 开始上传oss: {}", fileFullPath);
        // 上传到 OSS
        String fileName = ossUtils.sendToOssByFilePath(fileFullPath, true);
        if (org.apache.commons.lang3.StringUtils.isEmpty(fileName)) {
            log.error("message={}", "文件上传阿里云OSS失败");
            return "";
        } else {
            log.info("hope edu 操作成功={}", fileName);
            return fileName;
        }
    }

    private String buildHopeEduTxtContent(List<HopeEduOrderRecord> records) {
        if (Objects.isNull(records) || records.isEmpty()) {
            return "";
        }

        StringBuilder sb = new StringBuilder();

        for (HopeEduOrderRecord record: records) {
            sb.append(record.toTxtString()).append("\n");
        }
        return sb.toString();
    }


    private Map<String, Object> getOrder(String orderSn) {
        if (refundOrders.containsKey(orderSn)) {
            return refundOrders.get(orderSn);
        }

        Map<String, Object> order = orderService.getOrderByMerchantIdAndOrderSn(null, orderSn);
        if (Objects.isNull(order) || order.isEmpty()) {
            return null;
        }

        refundOrders.put(order.get(Order.SN).toString(), order);
        return order;
    }


    private Map<String, Object> getRefundTransaction(String refundTsn, String orderSn) {
        Map<String, Object> tran = null;

        if (refundTransactions.containsKey(orderSn)) {
            List<Map<String, Object>> trans = refundTransactions.get(orderSn);

            if (Objects.nonNull(trans) && !trans.isEmpty()) {
                for (Map<String, Object> transaction: trans) {
                    String tsn = transaction.get(Transaction.TSN).toString();
                    if (refundTsn.equals(tsn)) {
                        tran = transaction;
                    }
                }
                return tran;
            }
        }

        List<Map<String, Object>> transactions = transactionService.getTransactionListByOrderSn(orderSn);
        if (Objects.isNull(transactions) || transactions.isEmpty()) {
            // 查询失败返回
            return null;
        }

        for (Map<String, Object> transaction: transactions) {
            String tsn = transaction.get(Transaction.TSN).toString();
            if (refundTsn.equals(tsn)) {
                tran = transaction;
            }
        }
        refundTransactions.put(orderSn, transactions);
        return tran;
    }

    /**
     * 院校通订单忽略
     */
    private boolean checkIgnore(String payway) {
        return "院校通".equals(payway);
    }

    private String getRecordValue(List<String> record, Integer index) {
        if (Objects.isNull(index)) {
            return "";
        }
        String value = record.get(index);
        return StringUtils.isEmpty(value) ? "" : value;
    }

    private Long getServiceFee(Map<String, Object> transaction) {
        int type = MapUtil.getInteger(transaction, Transaction.TYPE, 0);
        Long fee = MapUtil.getLong(transaction, Transaction.FEE, 0L);

        // 30（支付）、31（预授权完成）、13（退款回退） 为正向，11（退款）、10（撤单） 为负向
        if (type == 30 || type == 31 || type == 13) {
            return fee;
        }else {
            return -fee;
        }

    }

    /**
     * 计算总的已退款金额，供支付订单填入
     * @param orderSn
     * @return
     */
    private Long calcRefundFee(String orderSn) {
        // 因为前面已经通过退款流水查过所有订单流水，因此缓存里有

        Map<String, Object> order = refundOrders.get(orderSn);

        if (Objects.isNull(order) || order.isEmpty()) {
            return 0L;
        }
        Long originalTotal = MapUtil.getLong(order, Order.ORIGINAL_TOTAL, 0L);
        Long netOriginal = MapUtil.getLong(order, Order.NET_ORIGINAL, 0L);
        return originalTotal - netOriginal;
    }

    private Long getRefundFee(Map<String, Object> transaction) {

        int type = MapUtil.getInteger(transaction, Transaction.TYPE, 0);
        Long refundFee = MapUtil.getLong(transaction, Transaction.EFFECTIVE_AMOUNT, 0L);
        if (type == 30 || type == 31 || type == 13) {
            return 0L;
        }
        return refundFee;
    }

    public static String yuan2cents(String yuan) {
        long cents = com.wosai.mpay.util.StringUtils.yuan2cents(yuan);
        return String.valueOf(cents);
    }

    private static String calcBalanceFee(String totalFee, String serviceFee) {
        Long balance = Long.parseLong(totalFee) - Long.parseLong(serviceFee);
        return balance.toString();
    }

    private static String formatHopeEduPayType(String payway) {
        if (StringUtils.isEmpty(payway)) {
            payway = "";
        }
        String paywayLow = payway.toLowerCase().trim();
        // `upay_transaction`.`statement_config` 表映射
        // 交易类型1 微信 2⽀付宝 3银⾏卡 4 现⾦ 5⽆卡⽀付 6qq钱包 7百度钱包8京东钱包 10翼⽀付 11云闪付
        if (paywayLow.contains("拉卡拉")) {
            return "16";
        } else if (paywayLow.contains("微信") || paywayLow.contains("wechat")) {
            return "1";
        } else if (paywayLow.contains("支付宝") || paywayLow.contains("alipay")) {
            return "2";
        } else if (paywayLow.contains("现金")) {
            return "4";
        } else if (paywayLow.contains("银行卡")) {
            return "3";
        } else if (paywayLow.contains("qq钱包")) {
            return "6";
        } else if (paywayLow.contains("百度")) {
            return "7";
        } else if (paywayLow.contains("京东")) {
            return "8";
        } else if (paywayLow.contains("翼支付")) {
            return "10";
        } else if (paywayLow.contains("云闪付")) {
            return "11";
        } else if (paywayLow.contains("apple")) {
            return "15";
        } else if (paywayLow.contains("移动和包")) {
            return "17";
        } else if (paywayLow.contains("索迪斯")) {
            return "18";
        } else if (paywayLow.contains("数字人民币")) {
            return "19";
        } else if (paywayLow.contains("富圈圈")) {
            return "20";
        } else if (paywayLow.contains("grabpay")) {
            return "21";
        } else if (paywayLow.contains("银行转账")) {
            return "22";
        } else if (paywayLow.contains("澳门通")) {
            return "23";
        } else {
            return "24";
        }
    }

    private String formatHopeEduPayMethod(String tradingMode) {
        if (StringUtils.isEmpty(tradingMode)) {
            tradingMode = "";
        }
//        map.put("1", "B扫C");
//        map.put("2", "C扫B");
//        map.put("3", "WAP支付");
//        map.put("4", "小程序支付");
        // 刷卡支付 1，扫码支付 2，公众号支付 3，wap 支付4

        String tradingModeLower = tradingMode.toUpperCase().trim();
        if (tradingModeLower.contains("B扫C") || tradingModeLower.contains("C扫B")) {
            return "2";
        } else if (tradingModeLower.contains("WAP支付")) {
            return "4";
        } else if (tradingModeLower.contains("小程序支付")) {
            return "3";
        }
        return "1";
    }


    private void formatHeader(List<String> headers) {

        for (int i = 0; i < headers.size(); i++) {
            String title = headers.get(i);
            title = StringUtils.isEmpty(title) ? "" : title.trim();

            if ("交易日期".equals(title)) {
                header.setTradeDate(i);
            } else if ("时间".equals(title)) {
                header.setTradeTime(i);
            } else if ("商户流水号".equals(title)) {
                header.setMerchantTradeSn(i);
            } else if ("收款通道/支付方式".equals(title)) {
                header.setPaymentChannel(i);
            } else if ("商品名".equals(title)) {
                header.setProductName(i);
            } else if ("商户内部订单号".equals(title)) {
                header.setMerchantInternalOrderNo(i);
            } else if ("商户订单号".equals(title)) {
                header.setMerchantOrderNo(i);
            } else if ("收款通道订单号".equals(title)) {
                header.setPaymentChannelOrderNo(i);
            } else if ("交易类型/交易模式".equals(title)) {
                header.setTradeType(i);
            } else if ("交易状态".equals(title)) {
                header.setTradeStatus(i);
            } else if ("付款账户".equals(title)) {
                header.setPayerAccount(i);
            } else if ("币种/货币类型".equals(title)) {
                header.setCurrencyType(i);
            } else if ("收款金额".equals(title)) {
                header.setCollectionAmount(i);
            } else if ("收钱吧商户优惠".equals(title)) {
                header.setShouqianbaMerchantDiscount(i);
            } else if ("收钱吧补贴优惠".equals(title)) {
                header.setShouqianbaSubsidyDiscount(i);
            } else if ("收款通道补贴优惠".equals(title)) {
                header.setPaymentChannelSubsidyDiscount(i);
            } else if ("收款通道商户预充值优惠".equals(title)) {
                header.setPaymentChannelPrepaidDiscount(i);
            } else if ("收款通道商户免充值优惠".equals(title)) {
                header.setPaymentChannelFreeDiscount(i);
            } else if ("消费者实付金额".equals(title)) {
                header.setConsumerActualPayment(i);
            } else if ("费率%".equals(title)) {
                header.setFeeRate(i);
            } else if ("支付手续费".equals(title)) {
                header.setPaymentHandlingFee(i);
            } else if ("实收金额".equals(title)) {
                header.setActualCollectionAmount(i);
            } else if ("技术服务费".equals(title)) {
                header.setTechnicalServiceFee(i);
            } else if ("结算金额".equals(title)) {
                header.setSettlementAmount(i);
            } else if ("商户号".equals(title)) {
                header.setMerchantSn(i);
            } else if ("商户名称".equals(title)) {
                header.setMerchantName(i);
            } else if ("门店号".equals(title)) {
                header.setStoreSn(i);
            } else if ("商户门店号".equals(title)) {
                header.setMerchantStoreSn(i);
            } else if ("门店名称".equals(title)) {
                header.setStoreName(i);
            } else if ("终端号".equals(title)) {
                header.setTerminalSn(i);
            } else if ("商户终端号".equals(title)) {
                header.setMerchantTerminalSn(i);
            } else if ("终端名称".equals(title)) {
                header.setTerminalName(i);
            } else if ("终端类型".equals(title)) {
                header.setTerminalType(i);
            } else if ("设备号".equals(title)) {
                header.setDeviceId(i);
            } else if ("操作员".equals(title)) {
                header.setOperator(i);
            } else if ("收银员".equals(title)) {
                header.setCashier(i);
            } else if ("备注".equals(title)) {
                header.setRemark(i);
            } else if ("分账标识".equals(title)) {
                header.setProfitSharingFlag(i);
            } else if ("分账金额".equals(title)) {
                header.setProfitSharingAmount(i);
            } else if ("收钱吧商户优惠类型".equals(title)) {
                header.setShouqianbaMerchantDiscountType(i);
            } else if ("交易模式".equals(title)) {
                header.setTradingMode(i);
            } else if ("智慧点单服务费".equals(title)) {
                header.setIntelligentOrderServiceFee(i);
            } else if ("流量服务费".equals(title)) {
                header.setTrafficServiceFee(i);
            } else if ("校园配送服务费".equals(title)) {
                header.setCampusDeliveryServiceFee(i);
            } else if ("第三方配送服务费".equals(title)) {
                header.setThirdPartyDeliveryServiceFee(i);
            } else if ("券包出售服务费".equals(title)) {
                header.setCouponPackageSalesServiceFee(i);
            } else if ("权益卡出售服务费".equals(title)) {
                header.setRightsCardSalesServiceFee(i);
            } else if ("储值充值服务费".equals(title)) {
                header.setStoredValueRechargeServiceFee(i);
            } else if ("花呗分期免息营销服务费".equals(title)) {
                header.setAlipayInstallmentFreeInterestFee(i);
            } else if ("先享后付服务费".equals(title)) {
                header.setPayLaterServiceFee(i);
            } else if ("流量服务费固定佣金".equals(title)) {
                header.setTrafficServiceFixedCommission(i);
            } else if ("流量服务费起步价".equals(title)) {
                header.setTrafficServiceStartingPrice(i);
            } else if ("零售外卖服务费".equals(title)) {
                header.setRetailTakeoutServiceFee(i);
            } else if ("线上门店技术服务费".equals(title)) {
                header.setOnlineStoreTechnicalServiceFee(i);
            } else if ("打包服务费".equals(title)) {
                header.setPackagingServiceFee(i);
            } else if ("配送管理助手费".equals(title)) {
                header.setDeliveryManagementAssistantFee(i);
            } else if ("应用方".equals(title)) {
                header.setApplicationParty(i);
            } else if ("交易流水号".equals(title)) {
                header.setTradeSn(i);
            } else if ("银联终端号".equals(title)) {
                header.setTermId(i);
            } else if ("收单机构商户号".equals(title)) {
                header.setProviderMchId(i);
            } else if ("交易场景".equals(title)) {
                header.setTradeScene(i);
            }
        }
    }

}
