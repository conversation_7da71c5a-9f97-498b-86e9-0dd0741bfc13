package com.wosai.upay.transaction.cal.process.util;/**
 * @description:
 * @author: lll
 * @date: 2022-03-16 2:32 下午
 */

import com.alibaba.fastjson.JSONObject;
import com.google.common.base.Charsets;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicHeader;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.io.IOException;
import java.util.HashMap;

/**
 *<AUTHOR>
 *@Date 2:32 下午
 */
@Component
public class SendVoiceMsgUtils {

    private static final Log logger = LogFactory.getLog(SendVoiceMsgUtils.class);

    private CloseableHttpClient httpclient;
    @Value("${jsonrpc.sms-gateway}")
    private String smsGateWayurl ;

    public SendVoiceMsgUtils() {
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectionRequestTimeout(1000)
                .setConnectTimeout(5000)
                .setSocketTimeout(60000)
                .build();
        httpclient = HttpClients.custom().setDefaultRequestConfig(requestConfig).build();
    }

    public void send(String templateId, String phoneNum) throws Exception {
        JSONObject json = new JSONObject();
        json.put("to", phoneNum);
        json.put("template", templateId);
        json.put("vars",new HashMap());
        HttpPost httpPost = new HttpPost(smsGateWayurl);
        StringEntity stringEntity = new StringEntity(json.toString(), Consts.UTF_8);
        stringEntity.setContentType(new BasicHeader("Content-Type", "application/json;charset=utf-8"));
        stringEntity.setContentEncoding(Consts.UTF_8.name());
        httpPost.setEntity(stringEntity);
        CloseableHttpResponse response = null;
        try {
            response = httpclient.execute(httpPost);
            HttpEntity httpEntity = response.getEntity();
            String result = EntityUtils.toString(httpEntity, Charsets.UTF_8);
            logger.info(String.format("语音短信发送结果:%s ", result));
        } catch (IOException e) {
            logger.error("发送语音短信失败： ", e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());
                } catch (Exception e) {
                }
            }
        }
        logger.info("response " + response + "phone number " + phoneNum);
    }
}
