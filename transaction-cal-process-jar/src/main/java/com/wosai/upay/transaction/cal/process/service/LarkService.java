package com.wosai.upay.transaction.cal.process.service;

import com.lark.chatbot.*;
import com.lark.chatbot.message.Message;
import com.lark.chatbot.message.TextMessage;
import com.wosai.upay.transaction.cal.process.util.SpringFactoryContext;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

@Component
public class LarkService {
    @Value("${lark.chatbot.url}")
    private String url;

    public static void larkMessage(String url, String content) {
        try {
            String active = SpringFactoryContext.resolve("${spring.profiles.active}");
            if ("prod".equals(active)) {
                LarkChatbotClient client = new LarkChatbotClient();
                client.send(url, getMessage(content));
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void larkMessage(String content){
        larkMessage(url, content);
    }

    private static Message getMessage(String content) {
        StringBuffer sb = new StringBuffer();
//        sb.append("主题：").append(subject).append("\n");
        sb.append("").append(content).append("\n");
        TextMessage message = new TextMessage(sb.toString());
        return message;
    }

}
