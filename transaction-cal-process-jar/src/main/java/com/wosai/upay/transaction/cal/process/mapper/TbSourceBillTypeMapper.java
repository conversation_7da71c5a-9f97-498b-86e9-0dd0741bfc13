package com.wosai.upay.transaction.cal.process.mapper;

import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillType;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillTypeExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface TbSourceBillTypeMapper {
    long countByExample(TbSourceBillTypeExample example);

    int deleteByExample(TbSourceBillTypeExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(TbSourceBillType record);

    int insertSelective(TbSourceBillType record);

    List<TbSourceBillType> selectByExample(TbSourceBillTypeExample example);

    TbSourceBillType selectByPrimaryKey(Integer id);

    List<TbSourceBillType> selectBySelective(TbSourceBillType tbSourceBillType);

    int updateByExampleSelective(@Param("record") TbSourceBillType record, @Param("example") TbSourceBillTypeExample example);

    int updateByExample(@Param("record") TbSourceBillType record, @Param("example") TbSourceBillTypeExample example);

    int updateByPrimaryKeySelective(TbSourceBillType record);

    int updateByPrimaryKey(TbSourceBillType record);
}