package com.wosai.upay.transaction.cal.process.processor.parse;

import com.alibaba.excel.ExcelReader;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.support.ExcelTypeEnum;
import com.google.common.collect.Maps;
import com.google.gson.Gson;
import com.wosai.common.exception.CommonInvalidParameterException;
import com.wosai.upay.transaction.cal.process.model.ParserConfig;
import com.wosai.upay.transaction.cal.process.model.domain.TbSourceBillInputTaskWithBLOBs;
import com.wosai.upay.transaction.cal.process.model.enums.SourceBillType;
import com.wosai.upay.transaction.cal.process.processor.SourceBill;
import com.wosai.upay.transaction.cal.process.util.CommonApolloUtil;
import com.wosai.upay.transaction.cal.process.util.CurrencyUtils;
import com.wosai.upay.transaction.cal.process.util.OssUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.IteratorUtils;
import org.apache.commons.csv.CSVFormat;
import org.apache.commons.csv.CSVParser;
import org.apache.commons.csv.CSVRecord;
import org.apache.commons.lang3.CharUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.*;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

@Slf4j
@Component
public class CommonSourceBillParser implements SourceBillParser {
    @Autowired
    private RestTemplate restTemplate;

    @Autowired
    private OssUtils ossUtils;

    @Override
    public SourceBill parseCsv(TbSourceBillInputTaskWithBLOBs task) {
        InputStream inputStream = null;
        Reader reader = null;
        SourceBill sourceBill;
        try {
            // 分开代码，利于定位异常
            String fileUrl = Arrays.stream(task.getFileUrl().split(",")).map(file -> ossUtils.restoreUrl(file)).collect(Collectors.joining(","));
            inputStream = readUrlToInputStream(fileUrl);
            reader = inputStreamToReader(inputStream);
            CSVParser csvRecords = parseReaderToCsvRecordIterable(reader);

            try {
                ParserConfig parserConfig = new Gson().fromJson(new String(task.getConfig()), ParserConfig.class);
                sourceBill = doParseCsv(csvRecords, parserConfig, task);
            } catch (Exception e) {
                if (e instanceof CommonInvalidParameterException) {
                    throw e;
                } else {
                    throw new CommonInvalidParameterException("解析上游账单失败，请确认账单文件格式，特别是账单类型是否匹配", e);
                }
            }
            sourceBill.setTaskId(task.getId().toString());
        } finally {
            // apache csv 解析器要求必须在解析完文件后才能关闭 Stream
            if (reader != null) {
                try {
                    reader.close();
                } catch (IOException e) {
                    log.error("解析文件后，关闭 Reader 异常", e);
                }
            }
            if (inputStream != null) {
                try {
                    inputStream.close();
                } catch (IOException e) {
                    log.error("解析文件后，关闭 InputStream 异常", e);
                }
            }
        }

        return sourceBill;
    }

    public SourceBill doParseCsv(CSVParser csvRecords, ParserConfig parserConfig, TbSourceBillInputTaskWithBLOBs task) {
        SourceBill sourceBill = new SourceBill();
        sourceBill.setSourceBillType(task.getSourceBillType());
        List<SourceBill.Detail> detailList = new ArrayList<>();
        int row = 1; // 行号
        Map<String, Integer> header = Maps.newHashMap(); //表头
        for (CSVRecord csvRecord : csvRecords) {
            List<String> list = IteratorUtils.toList(csvRecord.iterator());
            //list 每一行的值，row 被读取的行， 根据config取值。
            parserData(parserConfig, sourceBill, detailList, row, header, list); //有副作用 会往detailList中添加数据
            row++;
        }
        sourceBill.setDetailList(detailList);

        return sourceBill;
    }

    private void parserData(ParserConfig parserConfig, SourceBill sourceBill, List<SourceBill.Detail> detailList, int row, Map<String, Integer> header, List<String> list) {
        if (row == parserConfig.getServiceProviderNameRow()) { // 服务商名称
            sourceBill.setServiceProviderName(list.get(parserConfig.getServiceProviderNameColumn() - 1));
        }
        if (row == parserConfig.getServiceProviderIdRow()) { // 服务商PID
            sourceBill.setServiceProviderId(list.get(parserConfig.getServiceProviderIdColumn() - 1));
        }
        if (row == parserConfig.getTradeMonthRow()) { // 业务月份
            sourceBill.setTradeMonth(convertTradeMonth(list.get(parserConfig.getTradeMonthColumn() - 1)));
        }
        if (row == parserConfig.getTableHeader()) {
            //表头
            for (int i = 0; i < list.size(); i++) {
                if (list.get(i) != null) {
                    header.put(list.get(i).trim(), i);
                }
            }
        } else if (row > parserConfig.getTableHeader() && StringUtils.isNotBlank(list.get(0))) { // 详情
            SourceBill.Detail detail = new SourceBill.Detail();
            Map<String, Object> extraParams = new HashMap<>();
            // 商户PID
            String pid;
            if (SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE == sourceBill.getSourceBillType()) {
                pid = getByConfigAllowEmpty(list, header, parserConfig.getSourceMerchantId()).replace("`", "").replace("\t", "");
            } else {
                pid = getByConfig(list, header, parserConfig.getSourceMerchantId()).replace("`", "").replace("\t", "");
            }
            if (!NumberUtils.isDigits(pid)) {
                for (char c : pid.toCharArray()) {
                    if (!CharUtils.isAsciiAlphanumeric(c)) {
                        log.info("pid不是数字,跳过 pid={}", pid);
                        return;
                    }
                }
            }
            detail.setSourceMerchantId(pid);
            // 商户名称 (兼容蜻蜓)
            if (!StringUtils.isBlank(parserConfig.getSourceMerchantName())) {
                String sourceMerchantName;
                if (SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE == sourceBill.getSourceBillType()) {
                    sourceMerchantName = getByConfigAllowEmpty(list, header, parserConfig.getSourceMerchantName());
                } else {
                    sourceMerchantName = getByConfig(list, header, parserConfig.getSourceMerchantName());
                }
                detail.setSourceMerchantName(sourceMerchantName);
            }
            // 门店ID/二级商户号
            if (!StringUtils.isBlank(parserConfig.getSourceLevel2MerchantId())) {
                String sourceLevel2MerchantId;
                if (SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE == sourceBill.getSourceBillType()) {
                    sourceLevel2MerchantId = getByConfigAllowEmpty(list, header, parserConfig.getSourceLevel2MerchantId());
                } else {
                    sourceLevel2MerchantId = getByConfig(list, header, parserConfig.getSourceLevel2MerchantId());
                }
                detail.setSourceLevel2MerchantId(sourceLevel2MerchantId);
            }
            // 门店ID/二级商户名称
//                detail.setSourceLevel2MerchantName(getByConfig(csvRecord, header, parserConfig.getSourceLevel2MerchantId()));
            if (!StringUtils.isBlank(parserConfig.getHbfqNum())) {
                detail.setDimension(getByConfig(list, header, parserConfig.getHbfqNum())); // 花呗分期期数
            }

            // 交易信息：4 条（默认设 0）
            if (StringUtils.isNotBlank(parserConfig.getSourceValidTradeNum())) {
                detail.setSourceValidTradeNum(getNumByConfig(list, header, parserConfig.getSourceValidTradeNum())); // 有效交易笔数 = 有效交易笔数
            }
            if (!StringUtils.isBlank(parserConfig.getSourceValidTradeAmount())) {
                detail.setSourceValidTradeAmount(getAmountByConfig(list, header, parserConfig.getSourceValidTradeAmount(), parserConfig.getUnit())); // 有效交易金额（单位：分）= 有效交易实付金额（单位：元）
            }
            if (StringUtils.isNotBlank(parserConfig.getSourceValidRefundNum())) {
                detail.setSourceValidRefundNum(getNumByConfig(list, header, parserConfig.getSourceValidRefundNum())); // 有效交易退款笔数 = 有效交易退款笔数
            }
            if (StringUtils.isNotBlank(parserConfig.getSourceValidRefundAmount())) {
                detail.setSourceValidRefundAmount(-getAmountByConfig(list, header, parserConfig.getSourceValidRefundAmount(), parserConfig.getUnit())); // 有效交易退款金额（单位：分） = 有效交易退款实付金额（单位：元）（注意正负值）
            }
            //微信、蜻蜓账单的退款值列是正值，需要把负号修正过来
            if (sourceBill.getSourceBillType() == SourceBillType.TYPE_WEIXIN_DIRECT_TRADE ||
                    sourceBill.getSourceBillType() == SourceBillType.TYPE_WECHAT_INDIRECT_A ||
                    sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_DRAGONFLY) {
                detail.setSourceValidRefundAmount(Math.abs(detail.getSourceValidRefundAmount()));

            }
            // 其他
            if (Arrays.asList(SourceBillType.TYPE_WEIXIN_DIRECT_TRADE, SourceBillType.TYPE_WEIXIN_PUBLIC_HOSPITAL, SourceBillType.TYPE_WECHAT_OASIS_TRADE,
                    SourceBillType.TYPE_WEIXIN_SCHOOL_DIRECT_CAFETERIA, SourceBillType.TYPE_LKL, SourceBillType.TYPE_TL,
                    SourceBillType.TYPE_WEIXIN_SCHOOL_CAFETERIA, SourceBillType.TYPE_WECHAT_INDIRECT_A).contains(sourceBill.getSourceBillType())) {
                detail.setSourceSettlementBasisType("交易金额"); // 固定填写交易金额
            } else if (SourceBillType.TYPE_ALIPAY_DRAGONFLY == sourceBill.getSourceBillType()) {
                detail.setSourceSettlementBasisType("有效交易去重实名用户数"); // 固定填写有效交易去重实名用户数
            } else if (sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_HBFQ_TX) {
                detail.setSourceSettlementBasisType("交易实付金额");
            } else if (sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_OFFLINE_MINI_PROGRAM_BASIC_PAYMENT
                    || sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_SCHOOL_V2
                    || sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_SCHOOL
                    || sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT
                    || sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT) {
                //什么都不做
                detail.setSourceSettlementBasisType(" ");
            } else {
                detail.setSourceSettlementBasisType(getByConfig(list, header, parserConfig.getSourceSettlementBasisType())); // 结算依据类型
            }
            if (StringUtils.isNotBlank(parserConfig.getSourceSettlementBasis())) {
                detail.setSourceSettlementBasis(getAmountByConfig(list, header, parserConfig.getSourceSettlementBasis(), parserConfig.getUnit())); // 结算依据
            }
            if (!StringUtils.isBlank(parserConfig.getSourceMerchantFeeRate())) {
                detail.setSourceMerchantFeeRate(getByConfig(list, header, parserConfig.getSourceMerchantFeeRate()));  // 商户合约费率
            }

            detail.setSourceSettlementAmount(getAmountByConfig(list, header, parserConfig.getSourceSettlementAmount(), parserConfig.getUnit())); // 应结算总金额（单位：分）

            //手续费 需求待定
//            if (StringUtils.isNotBlank(parserConfig.getSourceMerchantFee())) {
//                detail.setSourceSettlementFeeRate(BigDecimal.valueOf(detail.getSourceSettlementAmount())
//                        .divide(BigDecimal.valueOf(detail.getSourceSettlementBasis()), 4, BigDecimal.ROUND_HALF_UP).toString());
//            }

            // 费率
            //如果结算费率不存在
            if (StringUtils.isBlank(parserConfig.getSourceSettlementFeeRate())) {
                if (detail.getSourceSettlementBasis() == 0L) {
                    detail.setSourceSettlementFeeRate("0.0");
                } else {
                    //以10000为分母
                    detail.setSourceSettlementFeeRate(BigDecimal.valueOf(detail.getSourceSettlementAmount())
                            .divide(BigDecimal.valueOf(detail.getSourceSettlementBasis()), 4, BigDecimal.ROUND_HALF_UP).toString());
                }
            } else if (parserConfig.getSourceSettlementFeeRate().contains("%")) {
                //微信直连
                detail.setSourceSettlementFeeRate(CurrencyUtils.convert100ToDf(getByConfig(list, header, parserConfig.getSourceSettlementFeeRate())));
            } else if (SourceBillType.TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE == sourceBill.getSourceBillType() && detail.getSourceSettlementAmount() != 0 && detail.getSourceSettlementBasis() != 0) {//新蓝海需要自己计算  应结算金额/结算依据值
                detail.setSourceSettlementFeeRate(String.valueOf(BigDecimal.valueOf(detail.getSourceSettlementAmount().doubleValue() / detail.getSourceSettlementBasis().doubleValue()).setScale(4, RoundingMode.HALF_UP).doubleValue()));
            } else if (SourceBillType.TYPE_ALIPAY_HBFQ_TX == sourceBill.getSourceBillType()) {
                detail.setSourceSettlementFeeRate(getByConfigAllowEmpty(list, header, parserConfig.getSourceSettlementFeeRate()));
            } else {
                detail.setSourceSettlementFeeRate(getByConfig(list, header, parserConfig.getSourceSettlementFeeRate()));// 结算费率
            }

//                detail.setRemark(csvRecord.get(header.get("备注（原因说明）"))); // 备注（原因说明）

            if (sourceBill.getSourceBillType() == SourceBillType.TYPE_WECHAT_OASIS_TRADE) {
                detail.setServiceProviderId(list.get(header.get("服务商商户号")));
            } else if (sourceBill.getSourceBillType() == SourceBillType.TYPE_WEIXIN_SCHOOL_DIRECT_CAFETERIA) {
                detail.setServiceProviderId("**********");
            } else if (sourceBill.getSourceBillType() == SourceBillType.TYPE_WEIXIN_SCHOOL_CAFETERIA) {
                detail.setServiceProviderId("261062443");
            } else if (!StringUtils.isBlank(sourceBill.getServiceProviderId())) {
                detail.setServiceProviderId(sourceBill.getServiceProviderId());
            }

            if (StringUtils.isNotBlank(parserConfig.getUnionServiceAmount())) {
                detail.setDimension(getAmountByConfig(list, header, parserConfig.getUnionServiceAmount(), parserConfig.getUnit()) + "");
            }

            // 支付宝花呗直连、支付宝花呗间连,新增三个额外字段。
            if (sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_HUABEI_INSTALLMENT || sourceBill.getSourceBillType() == SourceBillType.TYPE_ALIPAY_HUABEI_DIRECT) {
//                extraParams = new HashMap();
                if (StringUtils.isNotBlank(parserConfig.getTradeType())) {
                    try {
                        String tradeType = getByConfig(list, header, parserConfig.getTradeType());
                        extraParams.put("trade_type", tradeType);
                    } catch (Exception e) {
                        log.error("[commonSourceBillParser parserData] tradeType非必填信息,忽略报错信息", e);
                    }
                }
                if (StringUtils.isNotBlank(parserConfig.getIsSingleChannelTrade())) {
                    try {
                        String isSingleChannelTrade = getByConfig(list, header, parserConfig.getIsSingleChannelTrade());
                        extraParams.put("is_single_channel_trade", isSingleChannelTrade);
                    } catch (Exception e) {
                        log.error("[commonSourceBillParser parserData] isSingleChannelTrade非必填信息,忽略报错信息", e);
                    }
                }
                parserConfig.setSettlementState(CommonApolloUtil.getSettlementState());
                try {
                    String settlementState = getByConfig(list, header, parserConfig.getSettlementState());
                    extraParams.put("settlement_state", settlementState);
                    //2022年5月起，上游账单结算状态为N时，支付宝不给收钱吧进行结算。但“结算依据值”依然有数值，影响后续分润计算。
                    //当此数值为N时，“结算依据值”系统默认重置为0，不取原账单中的值。
                    if (settlementState.equals("N")) {
                        detail.setSourceSettlementBasis(0L);
                    }
                } catch (Exception e) {
                    log.error("[commonSourceBillParser parserData] settlementState非必填信息,忽略报错信息", e);
                }
            }

            if (!StringUtils.isEmpty(parserConfig.getMerchantLevel())) {

                String merchantLevel = getByConfig(list, header, parserConfig.getMerchantLevel());
                if (!StringUtils.isEmpty(merchantLevel)) {
                    extraParams.put("merchantLevel", merchantLevel);
                }

            }

            detail.setExtraParams(extraParams);
            detail.setSubNum(row - parserConfig.getTableHeader());


            detailList.add(detail);
        }
    }

    private long getAmountByConfig(List<String> csvRecord, Map<String, Integer> header, String field, String unit) {
        try {
            if ("元".equals(unit)) {
                return CurrencyUtils.yuanToFen(getByConfig(csvRecord, header, field).replace(",", ""));
            } else if ("分".equals(unit)) {
                return Math.round(Double.parseDouble(getByConfig(csvRecord, header, field).replace(",", "")));
            } else if ("角".equals(unit)) {
                return Long.parseLong(getByConfig(csvRecord, header, field).replace(",", "")) * 10;
            } else if ("1/10000分".equals(unit)) {
                return Long.parseLong(getByConfig(csvRecord, header, field).replace(",", "")) / 10000;
            } else {
                throw new CommonInvalidParameterException("解析" + field + "失败,没有设置正确的金额单位. 请检查配置");
            }
        } catch (Exception e) {
            log.error("getByConfig", e);
            if (e instanceof CommonInvalidParameterException) {
                throw e;
            } else {
                throw new CommonInvalidParameterException("解析" + field + "失败,无法转换为金额. 请检查配置");
            }
        }
    }

    private Integer getNumByConfig(List<String> csvRecord, Map<String, Integer> header, String field) {
        try {
            return Integer.valueOf(getByConfig(csvRecord, header, field).replace(",", ""));
        } catch (NumberFormatException e) {
            log.error("getNumByConfig", e);
            throw new CommonInvalidParameterException("解析" + field + "失败,无法转换为数字. 请检查配置");
        }
    }

    private String getByConfig(List<String> csvRecord, Map<String, Integer> header, String field) {
        try {

            //header 详情开始  field 从config取
            Integer index = header.get(field);
            if (index == null) {
                throw new CommonInvalidParameterException("获取表头[" + field + "]为空. 请检查配置");
            }
            //csvRecord 每一行的记录
            String s = csvRecord.get(index);
            if (s.equals("0.0")) {
                s = "0";
            }
            if (StringUtils.isEmpty(s)) {
                throw new CommonInvalidParameterException("解析" + field + "为空. 请检查配置");
            }
            return s.trim();
        } catch (Exception e) {
            log.error("getByConfig", e);
            throw new CommonInvalidParameterException("解析" + field + "失败. 请检查配置");
        }

    }

    private String getByConfigAllowEmpty(List<String> csvRecord, Map<String, Integer> header, String field) {
        try {
            Integer index = header.get(field);
            if (index == null) {
                throw new CommonInvalidParameterException("获取表头[" + field + "]为空. 请检查配置");
            }

            return csvRecord.get(index);
        } catch (Exception e) {
            log.error("getByConfig", e);
            throw new CommonInvalidParameterException("解析" + field + "失败. 请检查配置");
        }

    }

    private InputStream readUrlToInputStream(String url) {
        byte[] body = this.restTemplate.getForEntity(url, byte[].class).getBody();
        return new ByteArrayInputStream(body);
    }

    private Reader inputStreamToReader(InputStream inputStream) {
        Reader reader;
        try {
            reader = new InputStreamReader(inputStream, "GBK");
        } catch (UnsupportedEncodingException e) {
            throw new CommonInvalidParameterException("不支持的文件字符串，读取 GBK 字符集文件失败");
        }

        return reader;
    }

    private CSVParser parseReaderToCsvRecordIterable(Reader reader) {
        CSVParser csvRecords;
        try {
            csvRecords = CSVFormat.DEFAULT
                    .parse(reader);
        } catch (IOException e) {
            throw new CommonInvalidParameterException("解析CSV文件失败，请确认账单文件格式，特别是账单类型是否匹配", e);
        }

        return csvRecords;
    }

    @Override
    public SourceBill parseExcel(TbSourceBillInputTaskWithBLOBs task) {
//        throw new CommonInvalidParameterException("暂不支持此格式的文件解析");
        AtomicInteger subNum = new AtomicInteger(1); // 账单明细的行编号
        SourceBill sourceBill = new SourceBill();
        sourceBill.setTaskId(task.getId().toString());
        sourceBill.setSourceBillType(task.getSourceBillType());
        if (task.getSourceBillStartDate() != null) {
            LocalDateTime localDateTime = LocalDateTime.ofInstant(Instant.ofEpochMilli(task.getSourceBillStartDate().getTime()), ZoneOffset.ofHours(8));
            String month;
            if (localDateTime.getMonthValue() <= 9) {
                month = "0" + localDateTime.getMonthValue();
            } else {
                month = "" + localDateTime.getMonthValue();
            }
            sourceBill.setTradeMonth(localDateTime.getYear() + month);
        }
        List<SourceBill.Detail> detailList = new ArrayList<>();

        ParserConfig config = new Gson().fromJson(new String(task.getConfig()), ParserConfig.class);
        task.setFileUrl(Arrays.stream(task.getFileUrl().split(",")).map(file -> ossUtils.restoreUrl(file)).collect(Collectors.joining(",")));
        for (String fileUrl : task.getFileUrl().split(",")) {

            log.info("fileUrl: {}", fileUrl);
            byte[] body;
            try {
                body = this.restTemplate.getForEntity(fileUrl, byte[].class).getBody();
            } catch (Exception e) {
                throw new CommonInvalidParameterException("读取网络文件失败", e);
            }
            log.info("fileUrlEnd: {}", fileUrl);

            try (InputStream inputStream = new ByteArrayInputStream(body)) {
                log.info("ByteArrayInputStream: {}", fileUrl);

                Map<String, Integer> header = Maps.newHashMap(); //表头

                ExcelTypeEnum excelType = task.getFileUrl().endsWith(".xls") ? ExcelTypeEnum.XLS : ExcelTypeEnum.XLSX;
                ExcelReader excelReader = new ExcelReader(inputStream, excelType, new AnalysisEventListener<List<String>>() {

                    @Override
                    public void invoke(List<String> object, AnalysisContext context) {
                        int row = context.getCurrentRowNum() + 1;
                        parserData(config, sourceBill, detailList, row, header, object);
                    }

                    @Override
                    public void doAfterAllAnalysed(AnalysisContext context) {
                        sourceBill.setDetailList(detailList);
                        log.info("doAfterAllAnalysed: {}", fileUrl);
                    }
                });

                excelReader.read();

            } catch (Exception e) {
                if (e.getCause() instanceof CommonInvalidParameterException) {
                    throw new CommonInvalidParameterException(e.getCause().getMessage());
                } else {
                    throw new CommonInvalidParameterException("解析上游账单失败，请确认账单文件格式，特别是账单类型是否匹配", e);
                }
            }
        }

        return sourceBill;


    }

    /**
     * 将 2019年6月 格式转为 201906 格式
     */
    private String convertTradeMonth(String monthWithChinese) {
        if (StringUtils.isEmpty(monthWithChinese)) {
            throw new CommonInvalidParameterException("解析文件出错，原文件账单月份（奖励时间）不存在");
        }

        monthWithChinese = monthWithChinese.replace("年", "").replace("月", "");
        if (monthWithChinese.length() == 5) {
            monthWithChinese = monthWithChinese.substring(0, 4) + "0" + monthWithChinese.substring(4);
        }

        return monthWithChinese;
    }

}
