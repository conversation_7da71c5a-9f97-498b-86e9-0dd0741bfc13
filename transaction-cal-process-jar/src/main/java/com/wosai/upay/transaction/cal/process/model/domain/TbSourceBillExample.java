package com.wosai.upay.transaction.cal.process.model.domain;

import java.util.ArrayList;
import java.util.Date;
import java.util.Iterator;
import java.util.List;

public class TbSourceBillExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public TbSourceBillExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        protected void addCriterionForJDBCDate(String condition, Date value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value.getTime()), property);
        }

        protected void addCriterionForJDBCDate(String condition, List<Date> values, String property) {
            if (values == null || values.size() == 0) {
                throw new RuntimeException("Value list for " + property + " cannot be null or empty");
            }
            List<java.sql.Date> dateList = new ArrayList<java.sql.Date>();
            Iterator<Date> iter = values.iterator();
            while (iter.hasNext()) {
                dateList.add(new java.sql.Date(iter.next().getTime()));
            }
            addCriterion(condition, dateList, property);
        }

        protected void addCriterionForJDBCDate(String condition, Date value1, Date value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            addCriterion(condition, new java.sql.Date(value1.getTime()), new java.sql.Date(value2.getTime()), property);
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdIsNull() {
            addCriterion("source_bill_input_task_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdIsNotNull() {
            addCriterion("source_bill_input_task_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdEqualTo(Integer value) {
            addCriterion("source_bill_input_task_id =", value, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdNotEqualTo(Integer value) {
            addCriterion("source_bill_input_task_id <>", value, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdGreaterThan(Integer value) {
            addCriterion("source_bill_input_task_id >", value, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_bill_input_task_id >=", value, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdLessThan(Integer value) {
            addCriterion("source_bill_input_task_id <", value, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdLessThanOrEqualTo(Integer value) {
            addCriterion("source_bill_input_task_id <=", value, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdIn(List<Integer> values) {
            addCriterion("source_bill_input_task_id in", values, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdNotIn(List<Integer> values) {
            addCriterion("source_bill_input_task_id not in", values, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_input_task_id between", value1, value2, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andSourceBillInputTaskIdNotBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_input_task_id not between", value1, value2, "sourceBillInputTaskId");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthIsNull() {
            addCriterion("trade_date_month is null");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthIsNotNull() {
            addCriterion("trade_date_month is not null");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthEqualTo(Date value) {
            addCriterionForJDBCDate("trade_date_month =", value, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthNotEqualTo(Date value) {
            addCriterionForJDBCDate("trade_date_month <>", value, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthGreaterThan(Date value) {
            addCriterionForJDBCDate("trade_date_month >", value, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthGreaterThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("trade_date_month >=", value, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthLessThan(Date value) {
            addCriterionForJDBCDate("trade_date_month <", value, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthLessThanOrEqualTo(Date value) {
            addCriterionForJDBCDate("trade_date_month <=", value, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthIn(List<Date> values) {
            addCriterionForJDBCDate("trade_date_month in", values, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthNotIn(List<Date> values) {
            addCriterionForJDBCDate("trade_date_month not in", values, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("trade_date_month between", value1, value2, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andTradeDateMonthNotBetween(Date value1, Date value2) {
            addCriterionForJDBCDate("trade_date_month not between", value1, value2, "tradeDateMonth");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIsNull() {
            addCriterion("source_bill_type is null");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIsNotNull() {
            addCriterion("source_bill_type is not null");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeEqualTo(Integer value) {
            addCriterion("source_bill_type =", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotEqualTo(Integer value) {
            addCriterion("source_bill_type <>", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeGreaterThan(Integer value) {
            addCriterion("source_bill_type >", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_bill_type >=", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeLessThan(Integer value) {
            addCriterion("source_bill_type <", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeLessThanOrEqualTo(Integer value) {
            addCriterion("source_bill_type <=", value, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeIn(List<Integer> values) {
            addCriterion("source_bill_type in", values, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotIn(List<Integer> values) {
            addCriterion("source_bill_type not in", values, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_type between", value1, value2, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSourceBillTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("source_bill_type not between", value1, value2, "sourceBillType");
            return (Criteria) this;
        }

        public Criteria andSubNumIsNull() {
            addCriterion("sub_num is null");
            return (Criteria) this;
        }

        public Criteria andSubNumIsNotNull() {
            addCriterion("sub_num is not null");
            return (Criteria) this;
        }

        public Criteria andSubNumEqualTo(Integer value) {
            addCriterion("sub_num =", value, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumNotEqualTo(Integer value) {
            addCriterion("sub_num <>", value, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumGreaterThan(Integer value) {
            addCriterion("sub_num >", value, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("sub_num >=", value, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumLessThan(Integer value) {
            addCriterion("sub_num <", value, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumLessThanOrEqualTo(Integer value) {
            addCriterion("sub_num <=", value, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumIn(List<Integer> values) {
            addCriterion("sub_num in", values, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumNotIn(List<Integer> values) {
            addCriterion("sub_num not in", values, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumBetween(Integer value1, Integer value2) {
            addCriterion("sub_num between", value1, value2, "subNum");
            return (Criteria) this;
        }

        public Criteria andSubNumNotBetween(Integer value1, Integer value2) {
            addCriterion("sub_num not between", value1, value2, "subNum");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdIsNull() {
            addCriterion("source_merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdIsNotNull() {
            addCriterion("source_merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdEqualTo(String value) {
            addCriterion("source_merchant_id =", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdNotEqualTo(String value) {
            addCriterion("source_merchant_id <>", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdGreaterThan(String value) {
            addCriterion("source_merchant_id >", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdGreaterThanOrEqualTo(String value) {
            addCriterion("source_merchant_id >=", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdLessThan(String value) {
            addCriterion("source_merchant_id <", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdLessThanOrEqualTo(String value) {
            addCriterion("source_merchant_id <=", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdLike(String value) {
            addCriterion("source_merchant_id like", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdNotLike(String value) {
            addCriterion("source_merchant_id not like", value, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdIn(List<String> values) {
            addCriterion("source_merchant_id in", values, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdNotIn(List<String> values) {
            addCriterion("source_merchant_id not in", values, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdBetween(String value1, String value2) {
            addCriterion("source_merchant_id between", value1, value2, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantIdNotBetween(String value1, String value2) {
            addCriterion("source_merchant_id not between", value1, value2, "sourceMerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameIsNull() {
            addCriterion("source_merchant_name is null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameIsNotNull() {
            addCriterion("source_merchant_name is not null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameEqualTo(String value) {
            addCriterion("source_merchant_name =", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameNotEqualTo(String value) {
            addCriterion("source_merchant_name <>", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameGreaterThan(String value) {
            addCriterion("source_merchant_name >", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameGreaterThanOrEqualTo(String value) {
            addCriterion("source_merchant_name >=", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameLessThan(String value) {
            addCriterion("source_merchant_name <", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameLessThanOrEqualTo(String value) {
            addCriterion("source_merchant_name <=", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameLike(String value) {
            addCriterion("source_merchant_name like", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameNotLike(String value) {
            addCriterion("source_merchant_name not like", value, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameIn(List<String> values) {
            addCriterion("source_merchant_name in", values, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameNotIn(List<String> values) {
            addCriterion("source_merchant_name not in", values, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameBetween(String value1, String value2) {
            addCriterion("source_merchant_name between", value1, value2, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantNameNotBetween(String value1, String value2) {
            addCriterion("source_merchant_name not between", value1, value2, "sourceMerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdIsNull() {
            addCriterion("source_level2_merchant_id is null");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdIsNotNull() {
            addCriterion("source_level2_merchant_id is not null");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdEqualTo(String value) {
            addCriterion("source_level2_merchant_id =", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdNotEqualTo(String value) {
            addCriterion("source_level2_merchant_id <>", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdGreaterThan(String value) {
            addCriterion("source_level2_merchant_id >", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdGreaterThanOrEqualTo(String value) {
            addCriterion("source_level2_merchant_id >=", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdLessThan(String value) {
            addCriterion("source_level2_merchant_id <", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdLessThanOrEqualTo(String value) {
            addCriterion("source_level2_merchant_id <=", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdLike(String value) {
            addCriterion("source_level2_merchant_id like", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdNotLike(String value) {
            addCriterion("source_level2_merchant_id not like", value, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdIn(List<String> values) {
            addCriterion("source_level2_merchant_id in", values, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdNotIn(List<String> values) {
            addCriterion("source_level2_merchant_id not in", values, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdBetween(String value1, String value2) {
            addCriterion("source_level2_merchant_id between", value1, value2, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantIdNotBetween(String value1, String value2) {
            addCriterion("source_level2_merchant_id not between", value1, value2, "sourceLevel2MerchantId");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameIsNull() {
            addCriterion("source_level2_merchant_name is null");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameIsNotNull() {
            addCriterion("source_level2_merchant_name is not null");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameEqualTo(String value) {
            addCriterion("source_level2_merchant_name =", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameNotEqualTo(String value) {
            addCriterion("source_level2_merchant_name <>", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameGreaterThan(String value) {
            addCriterion("source_level2_merchant_name >", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameGreaterThanOrEqualTo(String value) {
            addCriterion("source_level2_merchant_name >=", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameLessThan(String value) {
            addCriterion("source_level2_merchant_name <", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameLessThanOrEqualTo(String value) {
            addCriterion("source_level2_merchant_name <=", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameLike(String value) {
            addCriterion("source_level2_merchant_name like", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameNotLike(String value) {
            addCriterion("source_level2_merchant_name not like", value, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameIn(List<String> values) {
            addCriterion("source_level2_merchant_name in", values, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameNotIn(List<String> values) {
            addCriterion("source_level2_merchant_name not in", values, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameBetween(String value1, String value2) {
            addCriterion("source_level2_merchant_name between", value1, value2, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andSourceLevel2MerchantNameNotBetween(String value1, String value2) {
            addCriterion("source_level2_merchant_name not between", value1, value2, "sourceLevel2MerchantName");
            return (Criteria) this;
        }

        public Criteria andDimensionIsNull() {
            addCriterion("dimension is null");
            return (Criteria) this;
        }

        public Criteria andDimensionIsNotNull() {
            addCriterion("dimension is not null");
            return (Criteria) this;
        }

        public Criteria andDimensionEqualTo(String value) {
            addCriterion("dimension =", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotEqualTo(String value) {
            addCriterion("dimension <>", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionGreaterThan(String value) {
            addCriterion("dimension >", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionGreaterThanOrEqualTo(String value) {
            addCriterion("dimension >=", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLessThan(String value) {
            addCriterion("dimension <", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLessThanOrEqualTo(String value) {
            addCriterion("dimension <=", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionLike(String value) {
            addCriterion("dimension like", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotLike(String value) {
            addCriterion("dimension not like", value, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionIn(List<String> values) {
            addCriterion("dimension in", values, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotIn(List<String> values) {
            addCriterion("dimension not in", values, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionBetween(String value1, String value2) {
            addCriterion("dimension between", value1, value2, "dimension");
            return (Criteria) this;
        }

        public Criteria andDimensionNotBetween(String value1, String value2) {
            addCriterion("dimension not between", value1, value2, "dimension");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumIsNull() {
            addCriterion("source_valid_trade_num is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumIsNotNull() {
            addCriterion("source_valid_trade_num is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumEqualTo(Integer value) {
            addCriterion("source_valid_trade_num =", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumNotEqualTo(Integer value) {
            addCriterion("source_valid_trade_num <>", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumGreaterThan(Integer value) {
            addCriterion("source_valid_trade_num >", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_valid_trade_num >=", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumLessThan(Integer value) {
            addCriterion("source_valid_trade_num <", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumLessThanOrEqualTo(Integer value) {
            addCriterion("source_valid_trade_num <=", value, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumIn(List<Integer> values) {
            addCriterion("source_valid_trade_num in", values, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumNotIn(List<Integer> values) {
            addCriterion("source_valid_trade_num not in", values, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_trade_num between", value1, value2, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeNumNotBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_trade_num not between", value1, value2, "sourceValidTradeNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountIsNull() {
            addCriterion("source_valid_trade_amount is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountIsNotNull() {
            addCriterion("source_valid_trade_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountEqualTo(Long value) {
            addCriterion("source_valid_trade_amount =", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountNotEqualTo(Long value) {
            addCriterion("source_valid_trade_amount <>", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountGreaterThan(Long value) {
            addCriterion("source_valid_trade_amount >", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("source_valid_trade_amount >=", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountLessThan(Long value) {
            addCriterion("source_valid_trade_amount <", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountLessThanOrEqualTo(Long value) {
            addCriterion("source_valid_trade_amount <=", value, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountIn(List<Long> values) {
            addCriterion("source_valid_trade_amount in", values, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountNotIn(List<Long> values) {
            addCriterion("source_valid_trade_amount not in", values, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountBetween(Long value1, Long value2) {
            addCriterion("source_valid_trade_amount between", value1, value2, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidTradeAmountNotBetween(Long value1, Long value2) {
            addCriterion("source_valid_trade_amount not between", value1, value2, "sourceValidTradeAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumIsNull() {
            addCriterion("source_valid_refund_num is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumIsNotNull() {
            addCriterion("source_valid_refund_num is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumEqualTo(Integer value) {
            addCriterion("source_valid_refund_num =", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumNotEqualTo(Integer value) {
            addCriterion("source_valid_refund_num <>", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumGreaterThan(Integer value) {
            addCriterion("source_valid_refund_num >", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumGreaterThanOrEqualTo(Integer value) {
            addCriterion("source_valid_refund_num >=", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumLessThan(Integer value) {
            addCriterion("source_valid_refund_num <", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumLessThanOrEqualTo(Integer value) {
            addCriterion("source_valid_refund_num <=", value, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumIn(List<Integer> values) {
            addCriterion("source_valid_refund_num in", values, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumNotIn(List<Integer> values) {
            addCriterion("source_valid_refund_num not in", values, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_refund_num between", value1, value2, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundNumNotBetween(Integer value1, Integer value2) {
            addCriterion("source_valid_refund_num not between", value1, value2, "sourceValidRefundNum");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountIsNull() {
            addCriterion("source_valid_refund_amount is null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountIsNotNull() {
            addCriterion("source_valid_refund_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountEqualTo(Long value) {
            addCriterion("source_valid_refund_amount =", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountNotEqualTo(Long value) {
            addCriterion("source_valid_refund_amount <>", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountGreaterThan(Long value) {
            addCriterion("source_valid_refund_amount >", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("source_valid_refund_amount >=", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountLessThan(Long value) {
            addCriterion("source_valid_refund_amount <", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountLessThanOrEqualTo(Long value) {
            addCriterion("source_valid_refund_amount <=", value, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountIn(List<Long> values) {
            addCriterion("source_valid_refund_amount in", values, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountNotIn(List<Long> values) {
            addCriterion("source_valid_refund_amount not in", values, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountBetween(Long value1, Long value2) {
            addCriterion("source_valid_refund_amount between", value1, value2, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceValidRefundAmountNotBetween(Long value1, Long value2) {
            addCriterion("source_valid_refund_amount not between", value1, value2, "sourceValidRefundAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeIsNull() {
            addCriterion("source_settlement_basis_type is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeIsNotNull() {
            addCriterion("source_settlement_basis_type is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeEqualTo(String value) {
            addCriterion("source_settlement_basis_type =", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotEqualTo(String value) {
            addCriterion("source_settlement_basis_type <>", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeGreaterThan(String value) {
            addCriterion("source_settlement_basis_type >", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeGreaterThanOrEqualTo(String value) {
            addCriterion("source_settlement_basis_type >=", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeLessThan(String value) {
            addCriterion("source_settlement_basis_type <", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeLessThanOrEqualTo(String value) {
            addCriterion("source_settlement_basis_type <=", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeLike(String value) {
            addCriterion("source_settlement_basis_type like", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotLike(String value) {
            addCriterion("source_settlement_basis_type not like", value, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeIn(List<String> values) {
            addCriterion("source_settlement_basis_type in", values, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotIn(List<String> values) {
            addCriterion("source_settlement_basis_type not in", values, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeBetween(String value1, String value2) {
            addCriterion("source_settlement_basis_type between", value1, value2, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisTypeNotBetween(String value1, String value2) {
            addCriterion("source_settlement_basis_type not between", value1, value2, "sourceSettlementBasisType");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisIsNull() {
            addCriterion("source_settlement_basis is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisIsNotNull() {
            addCriterion("source_settlement_basis is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisEqualTo(Long value) {
            addCriterion("source_settlement_basis =", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisNotEqualTo(Long value) {
            addCriterion("source_settlement_basis <>", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisGreaterThan(Long value) {
            addCriterion("source_settlement_basis >", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisGreaterThanOrEqualTo(Long value) {
            addCriterion("source_settlement_basis >=", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisLessThan(Long value) {
            addCriterion("source_settlement_basis <", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisLessThanOrEqualTo(Long value) {
            addCriterion("source_settlement_basis <=", value, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisIn(List<Long> values) {
            addCriterion("source_settlement_basis in", values, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisNotIn(List<Long> values) {
            addCriterion("source_settlement_basis not in", values, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisBetween(Long value1, Long value2) {
            addCriterion("source_settlement_basis between", value1, value2, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementBasisNotBetween(Long value1, Long value2) {
            addCriterion("source_settlement_basis not between", value1, value2, "sourceSettlementBasis");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateIsNull() {
            addCriterion("source_merchant_fee_rate is null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateIsNotNull() {
            addCriterion("source_merchant_fee_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateEqualTo(String value) {
            addCriterion("source_merchant_fee_rate =", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateNotEqualTo(String value) {
            addCriterion("source_merchant_fee_rate <>", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateGreaterThan(String value) {
            addCriterion("source_merchant_fee_rate >", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateGreaterThanOrEqualTo(String value) {
            addCriterion("source_merchant_fee_rate >=", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateLessThan(String value) {
            addCriterion("source_merchant_fee_rate <", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateLessThanOrEqualTo(String value) {
            addCriterion("source_merchant_fee_rate <=", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateLike(String value) {
            addCriterion("source_merchant_fee_rate like", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateNotLike(String value) {
            addCriterion("source_merchant_fee_rate not like", value, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateIn(List<String> values) {
            addCriterion("source_merchant_fee_rate in", values, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateNotIn(List<String> values) {
            addCriterion("source_merchant_fee_rate not in", values, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateBetween(String value1, String value2) {
            addCriterion("source_merchant_fee_rate between", value1, value2, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceMerchantFeeRateNotBetween(String value1, String value2) {
            addCriterion("source_merchant_fee_rate not between", value1, value2, "sourceMerchantFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateIsNull() {
            addCriterion("source_settlement_fee_rate is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateIsNotNull() {
            addCriterion("source_settlement_fee_rate is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateEqualTo(String value) {
            addCriterion("source_settlement_fee_rate =", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateNotEqualTo(String value) {
            addCriterion("source_settlement_fee_rate <>", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateGreaterThan(String value) {
            addCriterion("source_settlement_fee_rate >", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateGreaterThanOrEqualTo(String value) {
            addCriterion("source_settlement_fee_rate >=", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateLessThan(String value) {
            addCriterion("source_settlement_fee_rate <", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateLessThanOrEqualTo(String value) {
            addCriterion("source_settlement_fee_rate <=", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateLike(String value) {
            addCriterion("source_settlement_fee_rate like", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateNotLike(String value) {
            addCriterion("source_settlement_fee_rate not like", value, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateIn(List<String> values) {
            addCriterion("source_settlement_fee_rate in", values, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateNotIn(List<String> values) {
            addCriterion("source_settlement_fee_rate not in", values, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateBetween(String value1, String value2) {
            addCriterion("source_settlement_fee_rate between", value1, value2, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementFeeRateNotBetween(String value1, String value2) {
            addCriterion("source_settlement_fee_rate not between", value1, value2, "sourceSettlementFeeRate");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountIsNull() {
            addCriterion("source_settlement_amount is null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountIsNotNull() {
            addCriterion("source_settlement_amount is not null");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountEqualTo(Long value) {
            addCriterion("source_settlement_amount =", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountNotEqualTo(Long value) {
            addCriterion("source_settlement_amount <>", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountGreaterThan(Long value) {
            addCriterion("source_settlement_amount >", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("source_settlement_amount >=", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountLessThan(Long value) {
            addCriterion("source_settlement_amount <", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountLessThanOrEqualTo(Long value) {
            addCriterion("source_settlement_amount <=", value, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountIn(List<Long> values) {
            addCriterion("source_settlement_amount in", values, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountNotIn(List<Long> values) {
            addCriterion("source_settlement_amount not in", values, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountBetween(Long value1, Long value2) {
            addCriterion("source_settlement_amount between", value1, value2, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andSourceSettlementAmountNotBetween(Long value1, Long value2) {
            addCriterion("source_settlement_amount not between", value1, value2, "sourceSettlementAmount");
            return (Criteria) this;
        }

        public Criteria andIsImportIsNull() {
            addCriterion("is_import is null");
            return (Criteria) this;
        }

        public Criteria andIsImportIsNotNull() {
            addCriterion("is_import is not null");
            return (Criteria) this;
        }

        public Criteria andIsImportEqualTo(Boolean value) {
            addCriterion("is_import =", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotEqualTo(Boolean value) {
            addCriterion("is_import <>", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportGreaterThan(Boolean value) {
            addCriterion("is_import >", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportGreaterThanOrEqualTo(Boolean value) {
            addCriterion("is_import >=", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportLessThan(Boolean value) {
            addCriterion("is_import <", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportLessThanOrEqualTo(Boolean value) {
            addCriterion("is_import <=", value, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportIn(List<Boolean> values) {
            addCriterion("is_import in", values, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotIn(List<Boolean> values) {
            addCriterion("is_import not in", values, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportBetween(Boolean value1, Boolean value2) {
            addCriterion("is_import between", value1, value2, "isImport");
            return (Criteria) this;
        }

        public Criteria andIsImportNotBetween(Boolean value1, Boolean value2) {
            addCriterion("is_import not between", value1, value2, "isImport");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNull() {
            addCriterion("create_at is null");
            return (Criteria) this;
        }

        public Criteria andCreateAtIsNotNull() {
            addCriterion("create_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreateAtEqualTo(Date value) {
            addCriterion("create_at =", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotEqualTo(Date value) {
            addCriterion("create_at <>", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThan(Date value) {
            addCriterion("create_at >", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("create_at >=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThan(Date value) {
            addCriterion("create_at <", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtLessThanOrEqualTo(Date value) {
            addCriterion("create_at <=", value, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtIn(List<Date> values) {
            addCriterion("create_at in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotIn(List<Date> values) {
            addCriterion("create_at not in", values, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtBetween(Date value1, Date value2) {
            addCriterion("create_at between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andCreateAtNotBetween(Date value1, Date value2) {
            addCriterion("create_at not between", value1, value2, "createAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNull() {
            addCriterion("update_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIsNotNull() {
            addCriterion("update_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdateAtEqualTo(Date value) {
            addCriterion("update_at =", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotEqualTo(Date value) {
            addCriterion("update_at <>", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThan(Date value) {
            addCriterion("update_at >", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtGreaterThanOrEqualTo(Date value) {
            addCriterion("update_at >=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThan(Date value) {
            addCriterion("update_at <", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtLessThanOrEqualTo(Date value) {
            addCriterion("update_at <=", value, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtIn(List<Date> values) {
            addCriterion("update_at in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotIn(List<Date> values) {
            addCriterion("update_at not in", values, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtBetween(Date value1, Date value2) {
            addCriterion("update_at between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andUpdateAtNotBetween(Date value1, Date value2) {
            addCriterion("update_at not between", value1, value2, "updateAt");
            return (Criteria) this;
        }

        public Criteria andCappingRateIsNull() {
            addCriterion("capping_rate is null");
            return (Criteria) this;
        }

        public Criteria andCappingRateIsNotNull() {
            addCriterion("capping_rate is not null");
            return (Criteria) this;
        }

        public Criteria andCappingRateEqualTo(String value) {
            addCriterion("capping_rate =", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateNotEqualTo(String value) {
            addCriterion("capping_rate <>", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateGreaterThan(String value) {
            addCriterion("capping_rate >", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateGreaterThanOrEqualTo(String value) {
            addCriterion("capping_rate >=", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateLessThan(String value) {
            addCriterion("capping_rate <", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateLessThanOrEqualTo(String value) {
            addCriterion("capping_rate <=", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateLike(String value) {
            addCriterion("capping_rate like", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateNotLike(String value) {
            addCriterion("capping_rate not like", value, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateIn(List<String> values) {
            addCriterion("capping_rate in", values, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateNotIn(List<String> values) {
            addCriterion("capping_rate not in", values, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateBetween(String value1, String value2) {
            addCriterion("capping_rate between", value1, value2, "cappingRate");
            return (Criteria) this;
        }

        public Criteria andCappingRateNotBetween(String value1, String value2) {
            addCriterion("capping_rate not between", value1, value2, "cappingRate");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}