package com.wosai.upay.transaction.cal.process.util;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;

@Component
public class SnowFlakeUtil {
    private final static long START_STMP = 1681833600000L;
    private final static long DATA_CENTER_ID_BITS = 3L;
    private final static long MACHINE_ID_BITS = 2L;
    private final static long SEQUENCE_BITS = 8L;
    private final static long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    private long dataCenterId;
    private long machineId;
    private long sequence = 0L;
    private long lastStmp = -1L;

    private static final String machineIdKey = "parcelLockerMachineIdKey";
    private static final String dataCenterIdKey = "parcelLockerDataCenterKey";

    @Autowired
    RedisTemplate redisTemplate;

    @PostConstruct
    public void init() {
        this.machineId = getMachineIdFromRedis();
    }

    private long getMachineIdFromRedis() {
        return (long) redisTemplate.execute(new RedisCallback<Long>() {
            public Long doInRedis(RedisConnection connection) {
                return connection.incr(machineIdKey.getBytes());
            }
        });
    }

    private long getDataCenterIdFromRedis() {
        return (long) redisTemplate.execute(new RedisCallback<Long>() {
            public Long doInRedis(RedisConnection connection) {
                return connection.incr(dataCenterIdKey.getBytes());
            }
        });
    }

    public long nextId(int idSize) {
        long currStmp = getNewstmp();
        if (currStmp < lastStmp) {
            throw new RuntimeException("Clock moved backwards.  Refusing to generate id");
        }
        if (currStmp == lastStmp) {
            sequence = (sequence + 1) & MAX_SEQUENCE;
            if (sequence == 0L) {
                currStmp = getNextMill();
            }
        } else {
            sequence = 0L;
        }
        lastStmp = currStmp;

        long id = (currStmp - START_STMP) << (SEQUENCE_BITS + MACHINE_ID_BITS + DATA_CENTER_ID_BITS);
        id |= (dataCenterId << (SEQUENCE_BITS + MACHINE_ID_BITS));
        id |= (machineId << SEQUENCE_BITS);
        id |= sequence;
        return id % (long) Math.pow(10, idSize);
    }

    private long getNextMill() {
        long mill = getNewstmp();
        while (mill <= lastStmp) {
            mill = getNewstmp();
        }
        return mill;
    }

    private long getNewstmp() {
        return System.currentTimeMillis();
    }
}
