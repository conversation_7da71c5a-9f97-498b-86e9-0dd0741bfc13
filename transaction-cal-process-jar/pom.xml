<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-cal-process</artifactId>
        <groupId>com.wosai.upay</groupId>
        <version>1.5.76</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>transaction-cal-process-jar</artifactId>
    <packaging>jar</packaging>

    <properties>
        <java.version>1.8</java.version>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <org.mapstruct.version>1.2.0.Final</org.mapstruct.version>
        <business-logstash-api.version>1.14.0</business-logstash-api.version>
        <skipTests>false</skipTests>
        <sonar.exclusions>
            **/model/domain/**/*,
            **/model/dto/**/*,
        </sonar.exclusions>
        <logstash-encoder-version>6.4</logstash-encoder-version>
        <hera-version>1.5.3</hera-version>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>1.0.3</version>
        </dependency>


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>1.2.83</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>merchant-contract-job-api</artifactId>
            <version>2.6.2-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>2.2.4-alpha</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>${hera-version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-metrics</artifactId>
            <version>${hera-version}</version>
        </dependency>
        <!-- 接入sls日志模块-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-logback-1.x</artifactId>
            <version>${hera-version}</version>
        </dependency>
        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>${logstash-encoder-version}</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.5.0</version>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>wosai-database-instrumentation-springboot</artifactId>
            <version>5.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>apollo-client</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.alibaba</groupId>
                    <artifactId>druid</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>vault-sdk</artifactId>
            <version>1.0.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
            <version>3.25.3</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>2.9.6</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>hera-toolkit-sentinel</artifactId>
            <version>1.12.0</version>
        </dependency>
        <!--common-->
        <dependency>
            <groupId>com.wosai.common</groupId>
            <artifactId>wosai-common</artifactId>
            <version>1.6.9-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.ctrip.framework.apollo</groupId>
                    <artifactId>apollo-client</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wosai-brave-api</artifactId>
                    <groupId>com.wosai</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>wosai-brave153-api</artifactId>
                    <groupId>com.wosai</groupId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-redis</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <!--api-->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>transaction-cal-process-api</artifactId>
            <version>1.5.76</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!--instrument-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-instrumentation</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>apollo-client</artifactId>
                    <groupId>com.ctrip.framework.apollo</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--kafka-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-kafka</artifactId>
        </dependency>
        <!--web rpc-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-rpc</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- rpc-service -->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>core-business-api</artifactId>
            <version>2.9.0-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <groupId>com.wosai</groupId>
                    <artifactId>wosai-brave153-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai.itsys</groupId>
            <artifactId>cornucopia-admin-api</artifactId>
            <version>1.8.50</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
                <exclusion>
                    <groupId>com.wosai.middleware</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-core</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-webmvc</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring-web</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!--test-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-test</artifactId>
        </dependency>

        <!-- db:start -->
        <dependency>
            <groupId>org.mybatis.spring.boot</groupId>
            <artifactId>mybatis-spring-boot-starter</artifactId>
            <version>1.3.5</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>1.1.20</version>
        </dependency>
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>4.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.github.jsqlparser</groupId>
            <artifactId>jsqlparser</artifactId>
            <version>1.2</version>
        </dependency>
        <!--  redis -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        <!-- db:end -->

        <!--        &lt;!&ndash; log:start &ndash;&gt;-->
        <!--        <dependency>-->
        <!--            <groupId>org.slf4j</groupId>-->
        <!--            <artifactId>slf4j-api</artifactId>-->
        <!--            <version>1.7.25</version>-->
        <!--        </dependency>-->
        <!--        <dependency>-->
        <!--            <groupId>ch.qos.logback</groupId>-->
        <!--            <artifactId>logback-classic</artifactId>-->
        <!--            <version>1.2.3</version>-->
        <!--            <scope>test</scope>-->
        <!--        </dependency>-->
        <!--        &lt;!&ndash; log:end &ndash;&gt;-->

        <!-- tools:start -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>3.9</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>
        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>2.6</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>2.0.3</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.8</version>
            <scope>compile</scope>
        </dependency>
        <!-- tools:end -->
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-task-center-api</artifactId>
            <version>1.1.0</version>
        </dependency>

        <dependency>
            <groupId>tk.mybatis</groupId>
            <artifactId>mapper-spring-boot-starter</artifactId>
            <version>2.1.5</version>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>lark-chatbot-sdk</artifactId>
            <version>0.1.5</version>
        </dependency>

        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.2</version>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>javax.mail</artifactId>
            <version>1.6.2</version>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.54</version>
        </dependency>

        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>3.6</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.nextgen</groupId>
            <artifactId>data-common</artifactId>
            <version>2.0-SNAPSHOT</version>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>user-service-api</artifactId>
            <version>1.0.2-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.sp</groupId>
            <artifactId>business-logstash-api</artifactId>
            <version>${business-logstash-api.version}</version>
            <exclusions>
                <exclusion>
                    <artifactId>mybatis-plus-extension</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>mybatis-plus-core</artifactId>
                    <groupId>com.baomidou</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.sales</groupId>
            <artifactId>sales-system-service-api</artifactId>
            <version>0.5.3-SNAPSHOT</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.aliyun.odps</groupId>
            <artifactId>odps-sdk-core</artifactId>
            <version>0.38.3-public</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.mail</groupId>
                    <artifactId>mail</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>apollo-client</artifactId>
            <version>2.2.0</version>
        </dependency>


        <dependency>
            <groupId>net.logstash.logback</groupId>
            <artifactId>logstash-logback-encoder</artifactId>
            <version>6.4</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>logging-api</artifactId>
            <version>1.3.0-SNAPSHOT</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>signature-proxy-api</artifactId>
            <version>1.0.1-RELEASE</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>mpay-sdk-homebrew</artifactId>
            <version>2.4.67</version>
            <exclusions>
                <exclusion>
                    <groupId>com.squareup.okhttp3</groupId>
                    <artifactId>okhttp</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai</groupId>
            <artifactId>aop-gateway-api</artifactId>
            <version>1.3.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.github.briandilley.jsonrpc4j</groupId>
                    <artifactId>jsonrpc4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.app</groupId>
            <artifactId>merchant-user-api</artifactId>
            <version>1.10.12</version>
            <exclusions>
                <exclusion>
                    <artifactId>jsonrpc4j</artifactId>
                    <groupId>com.wosai.middleware</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-transaction-api</artifactId>
            <version>1.9.89</version>
            <exclusions>
                <exclusion>
                    <artifactId>fastjson</artifactId>
                    <groupId>com.alibaba</groupId>
                </exclusion>
            </exclusions>
        </dependency>
    </dependencies>

    <build>
        <finalName>transaction-cal-process</finalName>
        <plugins>
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>3.0.2</version>
                <executions>
                    <execution>
                        <id>copy-resources</id>
                        <phase>compile</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${basedir}/src/main/resources/odps</outputDirectory>
                            <resources>
                                <resource>
                                    <directory>${basedir}/src/main/resources/odps</directory>
                                    <includes>
                                        <include>*</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>1.5.14.RELEASE</version>
                <configuration>
                    <mainClass>com.wosai.upay.transaction.cal.process.Application</mainClass>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>1.3.7</version>
                <configuration>
                    <verbose>true</verbose>
                    <overwrite>true</overwrite>
                </configuration>
                <dependencies>
                    <!-- 添加 MySQL 驱动依赖 -->
                    <dependency>
                        <groupId>mysql</groupId>
                        <artifactId>mysql-connector-java</artifactId>
                        <version>8.0.33</version> <!-- 与数据库版本匹配 -->
                    </dependency>
                </dependencies>
            </plugin>
            <plugin>
                <groupId>com.wosai.middleware</groupId>
                <artifactId>wosai-logging-maven-plugin</artifactId>
                <version>1.8.0</version>
                <configuration>
                    <enableCallerData>true</enableCallerData>
                    <profiles>
                        <profile>
                            <name>prod</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_JSON</ref> <!-- 输出到标准输出，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>beta</name>
                            <level>INFO</level>
                            <references>
                                <ref>FT_FILE</ref> <!-- 输出到文件，格式是JSON -->
                            </references>
                        </profile>
                        <profile>
                            <name>default</name>   <!-- 在本地开发调试时，在IDE中设置 active profile为default -->
                            <level>INFO</level>
                            <references>
                                <ref>FT_CONSOLE_PATTERN</ref> <!-- 输出到标准输出，格式是格式化字符串 (1.2.0新增功能) -->
                            </references>
                        </profile>
                    </profiles>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-logback-spring</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>
