package com.wosai.upay.transaction.cal.process.model.response;

import com.wosai.upay.transaction.cal.process.model.dto.SummaryBillRemark;
import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SummaryBillResponse {
    private String sn;
    private String name;
    private String policyId;
    private String policyName;
    private String tradeMonth;
    private String serverChannelCode;
    private String ncCode;
    private String ncName;
    private String companyCode;
    private String companyName;
    /**
     * 业务性质ID
     */
    private String propertyId;
    /**
     * 业务性质名称
     */
    private String propertyName;
    private String totalSettlementAmount;
    private long totalSettlementLineCount;
    private long totalSourceMerchantCount;
    private SummaryBillStatus status;
    private FulfillmentPushStatus fulfillmentPushStatus;
    private String entryMonth;
    private List<RelatedImportTask> relatedImportTaskList;
    private SummaryBillRemark remark;
    private String sourceMerchantNumber;
    private Integer lineNumber;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class RelatedImportTask {
        private String taskSn;
        private String taskStartTime;
        private String taskEndTime;
        private String number;
        private String importTemplateRuleName;
        private SummaryBillRemark.SummaryBillImportTaskStatus status;
    }
}
