package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class StatementTaskUpdateParam {

    @NotNull(message = "taskId不能为空")
    private Long taskId;
    private Long planId;
    private Integer merchantType;
    private String groupSn;
    private String merchantIds;
    private String isvCode;
    private String createBy;

}
