package com.wosai.upay.transaction.cal.process.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.cal.process.model.ParserConfig;
import com.wosai.upay.transaction.cal.process.model.dto.*;
import com.wosai.upay.transaction.cal.process.model.request.BillListExportRequest;
import com.wosai.upay.transaction.cal.process.model.request.SourceBillPageRequest;
import com.wosai.upay.transaction.cal.process.validation.group.AddSourceBillTask;
import com.wosai.upay.transaction.cal.process.validation.group.ConfirmOrCancelSourceBillTask;
import com.wosai.upay.transaction.cal.process.validation.group.ExportBillList;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 上游账单 service
 *
 * <AUTHOR>
 * @date 2019-09-11 14:14
 */
@JsonRpcService("/rpc/sourcebill")
@Validated
public interface SourceBillService {

    /**
     * 上游账单类型列表查询
     *
     * @param param 查询条件
     * @return 上游账单类型列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/22009">api 文档</a>
     */
    List<TbSourceBillTypeDto> findSourceBillTypeList(TbSourceBillTypeDto param);

    /**
     * 导入上游账单
     *
     * @param param 入参
     * @return 导入成功返回账单简要信息，否则抛出异常
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/21550">api 文档</a>
     */
    @Validated(AddSourceBillTask.class)
    TbSourceBillInputTaskDto importSourceBill(@NotNull(message = "导入上游账单参数不能为空") @Valid TbSourceBillInputTaskDto param);

    /**
     * 聚宝盆导入数据后调用，创建上游账单数据
     *
     * @param params 入参
     * @return true-导入成功，false-导入失败
     */
    @Validated(AddSourceBillTask.class)
    boolean importSourceBillV2(@NotNull(message = "导入上游账单参数不能为空") @Valid TbSourceBillInfoDTO params);

    /**
     * 上游账单导入确认
     *
     * @param param 入参
     * @return result
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/21541">api 文档</a>
     */
    @Validated({ConfirmOrCancelSourceBillTask.class})
    TbSourceBillInputTaskDto importSourceBillConfirm(@NotNull(message = "确认导入上游账单参数不能为空") @Valid TbSourceBillInputTaskDto param);

    /**
     * 上游账单导入取消
     *
     * @param param 入参
     * @return result
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/21595">api 文档</a>
     */
    @Validated({ConfirmOrCancelSourceBillTask.class})
    TbSourceBillInputTaskDto importSourceBillCancel(@NotNull(message = "取消导入上游账单参数不能为空") @Valid TbSourceBillInputTaskDto param);

    /**
     * 上游账单任务详情查询
     *
     * @param id 任务id
     * @return 上游账单任务详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/21523">api 文档</a>
     */
    TbSourceBillInputTaskCustomDto findSourceBillInputTaskDetail(@NotNull(message = "任务id不能为空") Integer id);

    /**
     * 上游账单任务列表查询
     *
     * @param pageInfo           分页查询条件
     * @param sourceBillType     账单类型
     * @param sourceBillCompany  账单提供方
     * @param sourceBillClassify 账单大类
     * @param taskStatus         任务状态
     * @param createAtStart      导入时间（开始时间）
     * @param createAtEnd        导入时间（截止时间）
     * @param tradeDateMonth     账单月份（格式：201909）
     * @return 上游账单任务列表
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/21514">api 文档</a>
     */
    ListResult<TbSourceBillInputTaskCustomDto> findSourceBillInputTaskList(PageInfo pageInfo,
                                                                           Integer sourceBillType,
                                                                           Integer sourceBillCompany,
                                                                           Integer sourceBillClassify,
                                                                           Integer taskStatus,
                                                                           Date createAtStart,
                                                                           Date createAtEnd,
                                                                           @Pattern(regexp = "^\\d{6}$", message = "账单月份格式非法，格式示例：201909") String tradeDateMonth);

    /**
     * 账单匹配/输出结果详情查询
     *
     * @param id 上游账单详情id
     * @return 上游账单详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/22171">api 文档</a>
     */
    @Deprecated
    TbBillOutputCustomDto findBillOutputDetail(@NotNull(message = "账单详情id不能为空") Long id);

    /**
     * 账单匹配/输出结果详情查询
     *
     * @param id 上游账单详情id
     * @return 上游账单详情
     * @see <a href="http://yapi.wosai-inc.com/project/303/interface/api/22171">api 文档</a>
     */
    TbBillOutputCustomDto findBillOutputDetailV2(@NotNull(message = "账单详情id不能为空") Long id);

    /**
     * 上游账单明细列表查询
     *
     * @param billType           账单类型
     * @param billSourceCompany  上游账单提供方
     * @param billSourceClassify 账单大类
     * @param startDate          开始日期 /yyyy-MM 闭区间
     * @param endDate            结束日期 / yyyy-MM 闭区间
     * @param sourceMerchantId   支付源商户号
     * @param merchantName       支付源商户名
     * @param sqbMerchantSn      收钱吧商户号
     * @param pn                 pageNo
     * @param ps                 pageSize
     * @return ListResult<BillOutput>
     */
    @Deprecated
    ListResult<BillOutput> findBillOutput(Integer billType, Integer billSourceCompany, Integer billSourceClassify,
                                          String startDate, String endDate,
                                          String sourceMerchantId, String merchantName, String sqbMerchantSn,
                                          String serviceProviderId, Integer pn, Integer ps);

    ListResult<BillOutputV2> findBillOutputV2(SourceBillPageRequest sourceBillPageRequest);

    /**
     * 导出账单列表
     *
     * @param request 请求入参
     * @return 请求是否成功的message
     */
    @Validated({ExportBillList.class})
    Map<String, Object> exportBillList(@NotNull(message = "用户信息不能为空") @Valid BillListExportRequest request);

    /**
     * 修改账单解析配置
     *
     * @return
     */
    void updateParserConfig(Integer billType, ParserConfig config);

    /**
     * 获取微信直连对账单下载请求报文
     * @param params
     * @return
     *
     * @see <a href="http://yapi.wosai-inc.com/project/2571/interface/api/85990">获取微信直连对账单下载请求报文</a>
     */
    Map<String, String> getWeixinTradebillRequest(Map<String, Object> params);

    /**
     * 获取微信直连对账单下载地址签名
     * @param params
     * @return
     *
     * @see <a href="http://yapi.wosai-inc.com/project/2571/interface/api/85999">获取微信直连对账单下载地址签名</a>
     */
    Map<String, String> getWeixinDownloadUrlRequest(Map<String, String> params);

    /**
     * 获取支付宝直连对账单下载请求报文
     * @param params
     * @return
     *
     * @see <a href="http://yapi.wosai-inc.com/project/2571/interface/api/86008">获取支付宝直连对账单下载请求报文</a>
     */
    Map<String, String> getAlipayTradebillRequest(Map<String, Object> params);

    /**
     * 获取申请资金账单请求报文
     * @param params
     * @return
     *
     * @see <a href="https://yapi.wosai-inc.com/project/2571/interface/api/133411">获取申请资金账单请求报文</a>
     */
    Map<String, String> getWeixinFundflowBillRequest(Map<String, Object> params);

    /**
     * 获取申请单个子商户资金账单请求报文
     * @param params
     * @return
     *
     * @see <a href="https://yapi.wosai-inc.com/project/2571/interface/api/133420">获取申请单个子商户资金账单请求报文</a>
     */
    Map<String, String> getMerchantWeixinFundflowBillRequest(Map<String, Object> params);
}
