package com.wosai.upay.transaction.cal.process.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.cal.process.model.dto.MerchantAuthorizeDto;
import com.wosai.upay.transaction.cal.process.model.dto.MerchantAuthorizePageReq;
import com.wosai.upay.transaction.cal.process.model.dto.MerchantAuthorizeReq;
import com.wosai.upay.transaction.cal.process.model.dto.UpdateMerchantAuthorizeDto;
import com.wosai.upay.transaction.cal.process.model.response.MerchantAuthorizeVo;
import com.wosai.upay.transaction.cal.process.model.response.Page;
import org.springframework.validation.annotation.Validated;

@JsonRpcService("rpc/merchant_authorize")
@Validated
public interface MerchantAuthorizeService {


    /**
     * 创建商户授权
     * @param merchantAuthorizeDto
     * @return
     */
    boolean createMerchantAuthorize(MerchantAuthorizeDto merchantAuthorizeDto);


    /**
     * 更新商户授权
     * @param updateMerchantAuthorizeDto
     * @return
     */
    boolean updateMerchantAuthorize(UpdateMerchantAuthorizeDto updateMerchantAuthorizeDto);

    /**
     * 查询商户授权
     * @param merchantAuthorizeReq
     * @return
     */
    MerchantAuthorizeVo queryMerchantAuthorize(MerchantAuthorizeReq merchantAuthorizeReq);


    /**
     * 分页查询商户授权
     * @param merchantAuthorizePageReq
     * @return
     */
    Page<MerchantAuthorizeVo> queryMerchantAuthorizes(MerchantAuthorizePageReq merchantAuthorizePageReq);


    void retryMerchantAuthorizeAop(MerchantAuthorizeDto merchantAuthorizeDto);





}
