package com.wosai.upay.transaction.cal.process.model.response;

import java.util.List;

public class Page<T> {
    // 当前页码，从 1 开始
    private int pageNum;
    // 每页记录数
    private int pageSize;
    // 总记录数
    private long total;
    // 当前页的数据列表
    private List<T> records;

    public Page() {
    }

    public Page(int pageNum, int pageSize, long total, List<T> records) {
        this.pageNum = pageNum;
        this.pageSize = pageSize;
        this.total = total;
        this.records = records;
    }

    // 获取总页数
    public int getPages() {
        if (pageSize == 0) {
            return 0;
        }
        return (int) ((total + pageSize - 1) / pageSize);
    }

    // 判断是否为第一页
    public boolean isFirstPage() {
        return pageNum == 1;
    }

    // 判断是否为最后一页
    public boolean isLastPage() {
        return pageNum == getPages();
    }

    // Getters 和 Setters
    public int getPageNum() {
        return pageNum;
    }

    public void setPageNum(int pageNum) {
        this.pageNum = pageNum;
    }

    public int getPageSize() {
        return pageSize;
    }

    public void setPageSize(int pageSize) {
        this.pageSize = pageSize;
    }

    public long getTotal() {
        return total;
    }

    public void setTotal(long total) {
        this.total = total;
    }

    public List<T> getRecords() {
        return records;
    }

    public void setRecords(List<T> records) {
        this.records = records;
    }
}