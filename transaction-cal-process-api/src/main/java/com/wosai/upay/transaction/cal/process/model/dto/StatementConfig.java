package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Map;

/**
 * Created by hzq on 19/10/17.
 */
@Data
@Accessors(chain = true)
public class StatementConfig {

    private String language;
    private Integer split_type;
    private Map<String, Object> sheet_params;
    private Map<String, Object> sheet_detail_params;
    private String version;
    private Long periodStartTime;
    private Long periodEndTime;

}
