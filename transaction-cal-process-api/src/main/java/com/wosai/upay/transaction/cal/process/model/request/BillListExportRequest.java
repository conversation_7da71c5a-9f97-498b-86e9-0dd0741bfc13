package com.wosai.upay.transaction.cal.process.model.request;

import com.wosai.upay.transaction.cal.process.model.SpUpayCommon;
import com.wosai.upay.transaction.cal.process.validation.group.ExportBillList;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.util.Date;

/**
 * BillListExportRequest
 *
 * <AUTHOR>
 * @date 2019-10-21 11:58
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BillListExportRequest extends SpUpayCommon {
    /**
     * 上游账单类型
     */
    private Integer sourceBillType;

    /**
     * 上游账单提供方
     */
    private Integer sourceBillCompany;

    /**
     * 账单大类
     */
    private Integer sourceBillClassify;

    /**
     * 账单周期起始月份：yyyy-MM（包含）
     */
    @NotNull(message = "账单开始时间不能为空")
    @Pattern(regexp = "^(((?:19|20)\\d\\d)-(0?[1-9]|1[0-2]))$", message = "账单周期起始月份格式不正确，必须为 yyyy-mm 格式", groups = ExportBillList.class)
    private String sourceBillStartDate;

    /**
     * 账单周期截止月份：yyyy-MM（包含）
     */
    @NotNull(message = "账单结束时间不能为空")
    @Pattern(regexp = "^(((?:19|20)\\d\\d)-(0?[1-9]|1[0-2]))$", message = "账单周期结束月份格式不正确，必须为 yyyy-mm 格式", groups = ExportBillList.class)
    private String sourceBillEndDate;

    /**
     * 上游商户id
     */
    private String sourceMerchantId;

    /**
     * 上游商户名称
     */
    private String sourceMerchantName;

    /**
     * 收钱吧商户号
     */
    private String sqbMerchantNo;

    /**
     * 上游渠道号
     */
    private String serviceProviderId;
}
