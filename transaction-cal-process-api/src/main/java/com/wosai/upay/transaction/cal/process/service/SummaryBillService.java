package com.wosai.upay.transaction.cal.process.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.cal.process.model.request.SummaryBillPageRequest;
import com.wosai.upay.transaction.cal.process.model.response.BasePageResponse;
import com.wosai.upay.transaction.cal.process.model.response.SummaryBillResponse;
import org.springframework.validation.annotation.Validated;

import java.util.List;
import java.util.Map;

/**
 *
 */
@JsonRpcService("/rpc/summaryBill")
@Validated
public interface SummaryBillService {
    /**
     * 分页查询汇总账单
     * @param request
     * @return
     */
    BasePageResponse<SummaryBillResponse> pageSummaryBill(SummaryBillPageRequest request);

    /**
     * 查询汇总账单详情
     * @param summaryBillSn
     * @return
     */
    SummaryBillResponse querySummaryBillDetail(String summaryBillSn);

    /**
     * 确认汇总账单
     * @param summaryBillSn
     */
    void confirmSummaryBill(String summaryBillSn);

    /**
     * 推送汇总账单到业务订单
     * @param summaryBillSnList
     */
    void pushSummaryBIll(List<String> summaryBillSnList);

    /**
     * 根据导入任务号删除账单明细
     * @param importTaskSN
     */
    void removeSourceBillByImportTaskSn(String importTaskSN);

    void initBillType(Map<String, String> billTypeMap);

    /**
     * 删除测试用的账单数据及相关的TbBillOutputV2数据
     * @param summaryBillSn 汇总账单编号
     */
    void deleteTestBillData(String summaryBillSn);
}
