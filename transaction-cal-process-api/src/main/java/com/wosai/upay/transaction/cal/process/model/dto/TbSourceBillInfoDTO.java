package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@ToString
public class TbSourceBillInfoDTO {
    /**
     * 内部导入任务id
     */
    private Integer innerTaskId;
    /**
     * 导入任务 id
     */
    private String taskId;

    /**
     * 导入模版类型
     */
    private String importTemplateRuleCode;

    /**
     * 服务商唯一标识（ID/PID/NO）
     */
    private String serviceProviderId;

    /**
     * 服务商名称（或简称）
     */
    private String serviceProviderName;

    /**
     * 交易月份（必填）（格式：201908）
     */
    private String tradeMonth;

    /**
     * 入账月份
     */
    private String entryMonth;

    /**
     * 账单详情
     */
    private List<Detail> detailList;

    /**
     * 备注
     */
    private SummaryBillRemark remark;

    /**
     * 政策id
     */
    private String policyId;

    /**
     * 业务性质
     */
    private String propertyId;

    /**
     * 对方名称
     */
    private String ncName;

    /**
     * 我方名称
     */
    private String companyName;

    /**
     * 上游账单详情类
     */
    @Data
    @ToString
    public static class Detail {

        /**
         * 账单明细行编号（不包含头信息，行号为明细行从 1 起始的编号）
         */
        private Integer subNum;

        /**
         * 上游商户唯一标识（ID/PID/NO）
         */
        private String sourceMerchantId;

        /**
         * 上游商户名称（或简称）
         */
        private String sourceMerchantName;

        /**
         * 上游二级商户（门店）唯一标识（ID/PID/NO）
         */
        private String sourceLevel2MerchantId;

        /**
         * 上游二级商户（门店）名称
         */
        private String sourceLevel2MerchantName;

        /**
         * 收钱吧商户号
         */
        private String sqbMerchantSn;

        /**
         * 收钱吧商户名称
         */
        private String sqbMerchantName;

        /**
         * 设备号
         */
        private String deviceNo;

        /**
         * 维度（度量）（比如对于花呗分期，该值为花呗分期期数） 拉卡拉的时候为银联服务费
         */
        private String dimension;

        /**
         * 有效交易笔数
         */
        private Integer sourceValidTradeNum = 0;

        /**
         * 有效交易金额（单位：分）
         */
        private Long sourceValidTradeAmount = 0L;

        /**
         * 有效退款笔数
         */
        private Integer sourceValidRefundNum = 0;

        /**
         * 有效退款金额（单位：分）
         */
        private Long sourceValidRefundAmount = 0L;

        /**
         * 结算依据类型
         */
        private String sourceSettlementBasisType;

        /**
         * 结算依据（单位：分）
         */
        private Long sourceSettlementBasis = 0L;

        /**
         * 商户费率（小数格式，非百分制格式）
         */
        private String sourceMerchantFeeRate;

        /**
         * 结算费率（小数格式，非百分制格式）
         */
        private String sourceSettlementFeeRate = "0.0";

        /**
         * 封顶费率
         */
        private String cappingRate;

        /**
         * 应结算总金额（单位：分）
         */
        private Long sourceSettlementAmount = 0L;

        /**
         * 备注
         */
        private String remark;

        /**
         * 服务商唯一标识（ID/PID/NO）
         */
        private String serviceProviderId;

        /**
         * 未结算原因
         */
        private String notSettlementReason;

        /**
         * 支付源
         */
        private String source;

        /**
         * 支付源头成本（单位：分）
         */
        private Long sourceCost = 0L;

        /**
         * 渠道成本（单位：分）
         */
        private Long channelCost = 0L;

        /**
         * 商户手续费（单位：分）
         */
        private Long merchantCommission = 0L;

        private String extraParam1;

        private String extraParam2;

        private String extraParam3;

        private String extraParam4;

        private String extraParam5;

        private String isSingleChannelTrade;

        private String settlementState;

        private String merchantLevel;
    }
}
