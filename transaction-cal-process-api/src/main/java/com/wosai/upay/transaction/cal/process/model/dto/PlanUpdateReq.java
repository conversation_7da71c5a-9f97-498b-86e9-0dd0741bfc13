package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * Created by hzq on 19/10/17.
 */
@Data
@Accessors(chain = true)
public class PlanUpdateReq {

    @NotNull(message = "id不能为空")
    Long id;


    private String name;
    private Integer agreementType;
    private String email;

    private String username;
    private String password;
    private String host;
    private Integer port;
    private String path;
    private String folderName;


    private String language;
    private Integer splitType;
    private Integer sheetType;
    private Integer sheetDetailType;
    private String comment;
    private String version;
    private Long periodStartTime;
    private Long periodEndTime;
}
