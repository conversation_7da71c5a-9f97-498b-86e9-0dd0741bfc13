package com.wosai.upay.transaction.cal.process.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * TbSourceBillTypeDto
 *
 * <AUTHOR>
 * @date 2019-09-12 12:05
 */
@ToString
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TbSourceBillTypeDto implements Serializable {
    private static final long serialVersionUID = 1293827595210433759L;

    private Integer id;

    private String name;

    private Byte sourceBillCompany;

    private String sourceBillCompanyName;

    private Byte sourceBillClassify;

    private String sourceBillClassifyName;

    private Byte sourceBillCycle;

    private String sourceBillCycleName;

    private Byte sourceBillInputMethod;

    private String sourceBillInputMethodName;

    private Byte splitStrategy;

    private String splitStrategyName;

    private String config;

    private Boolean deleted;

    private Date createAt;

    private Date updateAt;

    private String merchantLevel;
}
