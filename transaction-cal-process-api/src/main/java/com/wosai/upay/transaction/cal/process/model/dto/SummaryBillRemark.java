package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SummaryBillRemark {
    private List<SummaryBillImportTask> taskList;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class SummaryBillImportTask {
        private String taskSn;
        private SummaryBillImportTaskStatus status = SummaryBillImportTaskStatus.SUCCESS;
        private String taskStartTime;
        private String taskEndTime;
        private String number;
        private String importTemplateRuleName;
    }

    @Getter
    public enum SummaryBillImportTaskStatus {
        SUCCESS,
        CANCELED;
    }

    public void addTask(String taskSn, String number, String importTemplateRuleName){
        if (this.taskList == null) {
            this.taskList = new java.util.ArrayList<>();
        }
        SummaryBillImportTask importTask = this.taskList.stream().filter(task -> task.getTaskSn().equals(taskSn)).findFirst().orElse(null);
        if (importTask == null)  {
            this.taskList.add(new SummaryBillImportTask(taskSn, SummaryBillImportTaskStatus.SUCCESS, new Date().toLocaleString(), new Date().toLocaleString(), number, importTemplateRuleName));
        } else {
            importTask.setNumber(String.valueOf(Long.parseLong(importTask.getNumber()) + Long.parseLong(number)));
        }
    }

    public void cancelTask(String taskSn) {
        for (SummaryBillImportTask task : taskList) {
            if (task.getTaskSn().equals(taskSn)) {
                task.setStatus(SummaryBillImportTaskStatus.CANCELED);
                task.setNumber("0");
                break;
            }
        }
    }
}
