package com.wosai.upay.transaction.cal.process.model.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;


@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class MerchantAuthorizeVo {

    private Long id;

    private Long taskId;

    private String merchantId;

    private Integer status;

    private Long ctime;

    private Long mtime;
}
