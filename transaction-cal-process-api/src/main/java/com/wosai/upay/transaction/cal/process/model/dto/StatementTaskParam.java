package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class StatementTaskParam {

    @NotNull(message = "planId")
    private Long planId;
    @NotNull(message = "merchantType")
    private Integer merchantType;
    private String groupSn;
    private String merchantIds;
    private String isvCode;
    private String fileUrl;
    private String createBy;

}
