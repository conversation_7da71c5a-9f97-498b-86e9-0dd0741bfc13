package com.wosai.upay.transaction.cal.process.model;

public class ParserConfig {

    private String sourceMerchantId;
    private String sourceMerchantName;
    private String sourceLevel2MerchantId;

    public String getMerchantLevel() {
        return merchantLevel;
    }

    public void setMerchantLevel(String merchantLevel) {
        this.merchantLevel = merchantLevel;
    }

    private String hbfqNum;
    private String sourceValidTradeNum;
    private String sourceValidTradeAmount;
    private String sourceValidRefundNum;
    private String sourceValidRefundAmount;
    private String sourceSettlementBasisType;
    private String sourceSettlementBasis;
    private String sourceMerchantFeeRate;
    private String sourceSettlementFeeRate;
    private String sourceSettlementAmount;
    private String sourceMerchantFee;
    private String unionServiceAmount;

    private String tradeType;

    private String isSingleChannelTrade;

    private String settlementState;

    private int serviceProviderNameRow;

    private String merchantLevel;



    public String getTradeType() {
        return tradeType;
    }

    public void setTradeType(String tradeType) {
        this.tradeType = tradeType;
    }

    public String getIsSingleChannelTrade() {
        return isSingleChannelTrade;
    }

    public void setIsSingleChannelTrade(String isSingleChannelTrade) {
        this.isSingleChannelTrade = isSingleChannelTrade;
    }

    public String getSettlementState() {
        return settlementState;
    }

    public void setSettlementState(String settlementState) {
        this.settlementState = settlementState;
    }

    private int serviceProviderNameColumn;
    private int serviceProviderIdRow;
    private int serviceProviderIdColumn;
    private int tableHeader; //表头
    private int tradeMonthRow; //业务月份
    private int tradeMonthColumn; //业务月份
    private String unit; //结算单位 元, 分

    public String getSourceMerchantId() {
        return sourceMerchantId;
    }

    public void setSourceMerchantId(String sourceMerchantId) {
        this.sourceMerchantId = sourceMerchantId;
    }

    public String getSourceMerchantName() {
        return sourceMerchantName;
    }

    public void setSourceMerchantName(String sourceMerchantName) {
        this.sourceMerchantName = sourceMerchantName;
    }

    public String getSourceLevel2MerchantId() {
        return sourceLevel2MerchantId;
    }

    public void setSourceLevel2MerchantId(String sourceLevel2MerchantId) {
        this.sourceLevel2MerchantId = sourceLevel2MerchantId;
    }

    public String getHbfqNum() {
        return hbfqNum;
    }

    public void setHbfqNum(String hbfqNum) {
        this.hbfqNum = hbfqNum;
    }

    public String getSourceValidTradeNum() {
        return sourceValidTradeNum;
    }

    public void setSourceValidTradeNum(String sourceValidTradeNum) {
        this.sourceValidTradeNum = sourceValidTradeNum;
    }

    public String getSourceValidTradeAmount() {
        return sourceValidTradeAmount;
    }

    public void setSourceValidTradeAmount(String sourceValidTradeAmount) {
        this.sourceValidTradeAmount = sourceValidTradeAmount;
    }

    public String getSourceValidRefundNum() {
        return sourceValidRefundNum;
    }

    public void setSourceValidRefundNum(String sourceValidRefundNum) {
        this.sourceValidRefundNum = sourceValidRefundNum;
    }

    public String getSourceValidRefundAmount() {
        return sourceValidRefundAmount;
    }

    public void setSourceValidRefundAmount(String sourceValidRefundAmount) {
        this.sourceValidRefundAmount = sourceValidRefundAmount;
    }

    public String getSourceSettlementBasisType() {
        return sourceSettlementBasisType;
    }

    public void setSourceSettlementBasisType(String sourceSettlementBasisType) {
        this.sourceSettlementBasisType = sourceSettlementBasisType;
    }

    public String getSourceSettlementBasis() {
        return sourceSettlementBasis;
    }

    public void setSourceSettlementBasis(String sourceSettlementBasis) {
        this.sourceSettlementBasis = sourceSettlementBasis;
    }

    public String getSourceMerchantFeeRate() {
        return sourceMerchantFeeRate;
    }

    public void setSourceMerchantFeeRate(String sourceMerchantFeeRate) {
        this.sourceMerchantFeeRate = sourceMerchantFeeRate;
    }

    public String getSourceSettlementFeeRate() {
        return sourceSettlementFeeRate;
    }

    public void setSourceSettlementFeeRate(String sourceSettlementFeeRate) {
        this.sourceSettlementFeeRate = sourceSettlementFeeRate;
    }

    public String getSourceSettlementAmount() {
        return sourceSettlementAmount;
    }

    public void setSourceSettlementAmount(String sourceSettlementAmount) {
        this.sourceSettlementAmount = sourceSettlementAmount;
    }

    public int getServiceProviderNameRow() {
        return serviceProviderNameRow;
    }

    public void setServiceProviderNameRow(int serviceProviderNameRow) {
        this.serviceProviderNameRow = serviceProviderNameRow;
    }

    public int getServiceProviderNameColumn() {
        return serviceProviderNameColumn;
    }

    public void setServiceProviderNameColumn(int serviceProviderNameColumn) {
        this.serviceProviderNameColumn = serviceProviderNameColumn;
    }

    public int getServiceProviderIdRow() {
        return serviceProviderIdRow;
    }

    public void setServiceProviderIdRow(int serviceProviderIdRow) {
        this.serviceProviderIdRow = serviceProviderIdRow;
    }

    public int getServiceProviderIdColumn() {
        return serviceProviderIdColumn;
    }

    public void setServiceProviderIdColumn(int serviceProviderIdColumn) {
        this.serviceProviderIdColumn = serviceProviderIdColumn;
    }

    public int getTableHeader() {
        return tableHeader;
    }

    public void setTableHeader(int tableHeader) {
        this.tableHeader = tableHeader;
    }

    public int getTradeMonthRow() {
        return tradeMonthRow;
    }

    public void setTradeMonthRow(int tradeMonthRow) {
        this.tradeMonthRow = tradeMonthRow;
    }

    public int getTradeMonthColumn() {
        return tradeMonthColumn;
    }

    public void setTradeMonthColumn(int tradeMonthColumn) {
        this.tradeMonthColumn = tradeMonthColumn;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public String getSourceMerchantFee() {
        return sourceMerchantFee;
    }

    public void setSourceMerchantFee(String sourceMerchantFee) {
        this.sourceMerchantFee = sourceMerchantFee;
    }

    public String getUnionServiceAmount() {
        return unionServiceAmount;
    }

    public void setUnionServiceAmount(String unionServiceAmount) {
        this.unionServiceAmount = unionServiceAmount;
    }

    @Override
    public String toString() {
        return "ParserConfig{" +
                "sourceMerchantId='" + sourceMerchantId + '\'' +
                ", sourceMerchantName='" + sourceMerchantName + '\'' +
                ", sourceLevel2MerchantId='" + sourceLevel2MerchantId + '\'' +
                ", hbfqNum='" + hbfqNum + '\'' +
                ", sourceValidTradeNum='" + sourceValidTradeNum + '\'' +
                ", sourceValidTradeAmount='" + sourceValidTradeAmount + '\'' +
                ", sourceValidRefundNum='" + sourceValidRefundNum + '\'' +
                ", sourceValidRefundAmount='" + sourceValidRefundAmount + '\'' +
                ", sourceSettlementBasisType='" + sourceSettlementBasisType + '\'' +
                ", sourceSettlementBasis='" + sourceSettlementBasis + '\'' +
                ", sourceMerchantFeeRate='" + sourceMerchantFeeRate + '\'' +
                ", sourceSettlementFeeRate='" + sourceSettlementFeeRate + '\'' +
                ", sourceSettlementAmount='" + sourceSettlementAmount + '\'' +
                ", sourceMerchantFee='" + sourceMerchantFee + '\'' +
                ", unionServiceAmount='" + unionServiceAmount + '\'' +
                ", serviceProviderNameRow=" + serviceProviderNameRow +
                ", serviceProviderNameColumn=" + serviceProviderNameColumn +
                ", serviceProviderIdRow=" + serviceProviderIdRow +
                ", serviceProviderIdColumn=" + serviceProviderIdColumn +
                ", tableHeader=" + tableHeader +
                ", tradeMonthRow=" + tradeMonthRow +
                ", tradeMonthColumn=" + tradeMonthColumn +
                ", unit='" + unit + '\'' +
                ", tradeType='" + tradeType + '\'' +
                ", isSingleChannelTrade='" + isSingleChannelTrade + '\'' +
                ", settlementState='" + settlementState + '\'' +
                '}';
    }
}
