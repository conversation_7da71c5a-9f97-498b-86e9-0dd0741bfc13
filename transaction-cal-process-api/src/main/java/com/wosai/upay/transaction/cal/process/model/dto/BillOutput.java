package com.wosai.upay.transaction.cal.process.model.dto;

import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import lombok.Data;

import java.util.Date;

@Data
public class BillOutput {
    private Integer id;

    private String summaryBillSn;

    private String policyName;

    private Integer sourceBillInputTaskId;

    private Date tradeDateMonth;

    private String startDate;

    private String endDate;

    private String sourceMerchantId;

    private String sqbMerchantId;

    private String sqbMerchantSn;

    private String sourceMerchantName;

    private String sourceLevel2MerchantId;

    private String sourceLevel2MerchantName;

    private String dimension;

    private Integer sourceBillType;

    private Integer sourceTradeNum;

    private Long sourceTradeAmount;

    private Integer sourceRefundNum;

    private Long sourceRefundAmount;

    private String sourceSettlementBasisType;

    private Long sourceSettlementBasis;

    private String sourceMerchantFeeRate;

    private String sourceSettlementFeeRate;

    private Long sourceSettlementAmount;

    private Integer sqbTradeNum;

    private Long sqbTradeAmount;

    private Integer sqbRefundNum;

    private Long sqbRefundAmount;

    private Integer sqbClearingNum;

    private Long sqbClearingAmount;

    private String remark;

    private Date createAt;

    private Date updateAt;

    private String sourceBillTypeName;

    private String cappingRate;

    /**
     * 服务商唯一标识（ID/PID/NO）
     */
    private String serviceProviderId;

    //1已确认生效,0未确认生效
    private Integer isImport;

    //收钱吧有效交易金额占比
    private String sourceValidTradeAmountRatio;
}