package com.wosai.upay.transaction.cal.process.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

/**
 * TbBillOutputCustomDto
 *
 * <AUTHOR>
 * @date 2019-09-16 23:55
 */
@EqualsAndHashCode(callSuper = true)
@ToString
@Data
@Accessors(chain = true)
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class TbBillOutputCustomDto extends TbBillOutputDto implements Serializable {
    private static final long serialVersionUID = -2199161274945519452L;

    private String sqbMerchantName;

    private TbSourceBillTypeDto tbSourceBillType;

    private String summaryBillSn;

    private String tradeMonth;

    private String ncName;

    private String companyName;

    private String policyName;

    private String propertyName;

}
