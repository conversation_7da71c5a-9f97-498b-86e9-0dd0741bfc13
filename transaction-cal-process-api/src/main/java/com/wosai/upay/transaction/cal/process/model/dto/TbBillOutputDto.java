package com.wosai.upay.transaction.cal.process.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.Date;

/**
 * TbBillOutputDto
 *
 * <AUTHOR>
 * @date 2019-09-16 17:37
 */
@ToString
@Data
@Accessors(chain = true)
//@JsonInclude(JsonInclude.Include.NON_NULL)
public class TbBillOutputDto implements Serializable {
    private static final long serialVersionUID = 555283317443455861L;

    private Long id;

    private Integer sourceBillInputTaskId;

    private Date tradeDateMonth;

    private Integer sourceBillType;

    private Integer subNum;

    private String sourceMerchantId;

    private String sourceMerchantName;

    private String sourceLevel2MerchantId;

    private String sourceLevel2MerchantName;

    private String sqbMerchantId;

    private String sqbMerchantSn;

    private String dimension;

    private Integer sourceValidTradeNum;

    private Long sourceValidTradeAmount;

    private Integer sourceValidRefundNum;

    private Long sourceValidRefundAmount;

    private String sourceValidTradeAmountRatio;

    private String sourceSettlementBasisType;

    private Long sourceSettlementBasis;

    private String sourceMerchantFeeRate;

    private String sourceSettlementFeeRate;

    private Long sourceSettlementAmount;

    private Integer sqbTradeNum;

    private Long sqbTradeAmount;

    private Integer sqbRefundNum;

    private Long sqbRefundAmount;

    private Integer sqbClearingNum;

    private Long sqbClearingAmount;

    private String remark;

    private Boolean isImport;

    private Date createAt;

    private Date updateAt;

    private String cappingRate;

    private String serviceProviderId;

    private String extraParams;

    private Long channelCost;

    private Long sourceCost;

    private String source;

    private Long merchantCommission;

    private String notSettlementReason;

    private String deviceNo;

    private String importTaskSn;
}
