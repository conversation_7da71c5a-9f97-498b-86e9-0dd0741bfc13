package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * Created by hzq on 19/10/17.
 */
@Data
@Accessors(chain = true)
public class PlanAddReq {

    @NotNull(message = "name不能为空")
    private String name;
    private Integer agreementType = 0;
    @NotNull(message = "username不能为空")
    private String username;
    @NotNull(message = "password不能为空")
    private String password;
    @NotNull(message = "host不能为空")
    private String host;
    private Integer port = 2322;
    private String path = "";
    private String folderName = "";

    private String email;

    private String language = "zh";
    private Integer splitType = 0;
    private Integer sheetType = 0;
    private Integer sheetDetailType = 0;
    private String comment = "";
    private String createBy;
    private String version;
    private Long periodStartTime;
    private Long periodEndTime;

}
