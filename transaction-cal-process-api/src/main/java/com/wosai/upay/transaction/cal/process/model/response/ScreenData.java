package com.wosai.upay.transaction.cal.process.model.response;

import lombok.Data;

import java.util.Map;

/**
 * Created by <PERSON><PERSON><PERSON><PERSON><PERSON> on 2024/1/8.
 */
@Data
public class ScreenData {

    private String day;
    private String year;

    private long newCustomerUserCount;
    private long accumulateCustomerUserCount;
    private long newMerchantCount;
    private long accumulateMerchantCount;

    private long accumulateTradeCount;
    private long accumulateTradeAmount;

    private long corpNewMerchantCount;
    private long accumulateCorpMerchantCount;

    private long corpTradeCount;
    private long corpTradeAmount;
    private long accumulateCorpTradeAmount;

    /**
     * 银行合作日top城市交易信息
     */
    private Map<String, Long> corpTopCitiesTrade;




}
