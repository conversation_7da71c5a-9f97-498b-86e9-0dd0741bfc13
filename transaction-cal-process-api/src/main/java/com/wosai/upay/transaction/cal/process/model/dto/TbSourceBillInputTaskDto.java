package com.wosai.upay.transaction.cal.process.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.wosai.upay.transaction.cal.process.validation.group.AddSourceBillTask;
import com.wosai.upay.transaction.cal.process.validation.group.ConfirmOrCancelSourceBillTask;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.io.Serializable;
import java.util.Date;

/**
 * TbSourceBillInputTaskDto
 *
 * <AUTHOR>
 * @date 2019-09-11 14:16
 */
@ToString
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TbSourceBillInputTaskDto implements Serializable {
    private static final long serialVersionUID = 1989076220235915499L;

    @NotNull(message = "任务 id 不能为空", groups = ConfirmOrCancelSourceBillTask.class)
    private Integer id;

    @NotNull(message = "上游账单类型不能为空", groups = AddSourceBillTask.class)
    private Integer sourceBillType;

    @NotNull(message = "上游账单周期起始日期不能为空", groups = AddSourceBillTask.class)
    private Date sourceBillStartDate;

    @NotNull(message = "上游账单周期截止日期不能为空", groups = AddSourceBillTask.class)
    private Date sourceBillEndDate;

    private Integer taskStatus;

    @NotEmpty(message = "上游账单文件URL不能为空", groups = AddSourceBillTask.class)
    private String fileUrl;

    private String createBy;

    private String confirmBy;

    private Date createAt;

    private Date updateAt;

    private String remark;

    private String errorInfo;
}
