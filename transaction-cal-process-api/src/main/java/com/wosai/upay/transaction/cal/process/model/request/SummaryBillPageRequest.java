package com.wosai.upay.transaction.cal.process.model.request;

import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import lombok.Data;

@Data
public class SummaryBillPageRequest {
    private Integer pageNo;
    private Integer pageSize;
    private SummaryBillPageQuery query;

    @Data
    public class SummaryBillPageQuery {
        private String policyId;
        private String tradeStartMonth;
        private String tradeEndMonth;
        private String entryStartMonth;
        private String entryEndMonth;
        private SummaryBillStatus status;
        private FulfillmentPushStatus pushFulfillmentStatus;
        private String serverChannelCode;
        private String summaryBillSn;
        private String name;
        /**
         * 业务性质ID
         */
        private String propertyId;

        /**
         * 对方名称
         */
        private String ncName;

        /**
         * 我方名称
         */
        private String companyName;
    }
}
