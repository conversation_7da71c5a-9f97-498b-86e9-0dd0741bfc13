package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class MerchantAuthorizeReq {

    @NotNull(message = "taskId is required")
    private Long taskId;

    @NotBlank(message = "merchantSn is required")
    private String merchantSn;
}
