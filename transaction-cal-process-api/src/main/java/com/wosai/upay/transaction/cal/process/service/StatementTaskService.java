package com.wosai.upay.transaction.cal.process.service;


import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskDto;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskFilterParam;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskParam;
import com.wosai.upay.transaction.cal.process.model.dto.StatementTaskUpdateParam;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import java.util.List;

@JsonRpcService("rpc/statementTask")
@Validated
public interface StatementTaskService {


    /**
     * 新建任务
     */
    void createTask(StatementTaskParam statementTaskParam);

    /**
     * 启用任务
     *
     * @param taskId
     */
    void enableTask(Long taskId);

    /**
     * 禁用任务
     */
    void disableTask(Long taskId);

    /**
     * 删除任务
     *
     * @param taskId
     */
    void deleteTask(Long taskId);

    /**
     * 更新任务
     *
     * @param statementTaskParam
     */
    void updateTask(StatementTaskUpdateParam statementTaskParam);

    /**
     * 查询任务列表
     *
     * @return
     */
    ListResult<StatementTaskDto> selectTaskList(PageInfo pageInfo, StatementTaskFilterParam statementTaskFilterParam);

    /**
     * 获取对账单版本
     * @return
     */
    List<String> getStatementVersion();

    List<StatementTaskDto> findTask(StatementTaskFilterParam statementTaskFilterParam);


}
