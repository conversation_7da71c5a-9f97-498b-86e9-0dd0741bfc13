package com.wosai.upay.transaction.cal.process.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * TbSourceBillInputTaskCustomDto
 *
 * <AUTHOR>
 * @date 2019-09-16 19:29
 */
@EqualsAndHashCode(callSuper = true)
@ToString
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TbSourceBillInputTaskCustomDto extends TbSourceBillInputTaskDto implements Serializable {
    private static final long serialVersionUID = -8674081910829731717L;

    private Long sourceTradeNumTotal;

    private TbSourceBillTypeDto tbSourceBillType;

    private TradeInfoDto tradeInfo;

}
