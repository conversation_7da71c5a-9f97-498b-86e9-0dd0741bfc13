package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.Date;

/**
 * Created by hzq on 19/10/17.
 */
@Data
@Accessors(chain = true)
public class PlanQueryResult {
    private Long id;
    private String name;
    private String email;
    private Integer agreementType;
    private Integer status;

    private String username;
    private String password;
    private String host;
    private Integer port;
    private String path;
    private String folderName;

    private String language;
    private Integer splitType;
    private Integer sheetType;
    private Integer sheetDetailType;

    private String version;
    private Long periodStartTime;
    private Long periodEndTime;


    private String comment;
    private String createBy;
    private Date createAt;
    private Date updateAt;
    private Date deleteAt;
}
