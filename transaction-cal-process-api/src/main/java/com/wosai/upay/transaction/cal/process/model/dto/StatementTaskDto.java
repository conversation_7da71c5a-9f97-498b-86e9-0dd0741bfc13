package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Map;

@Data
public class StatementTaskDto {

    private Date createTime;
    private Integer taskId;
    private String planId;
    private String planName;
    private String merchantType;
    private String groupSn;
    private String merchantIds;
    private String isvCode;
    private String status;
    private String createBy;

    private String groupMerchantName;
    private List<Map> merchantInfoList;
    private String isvName;

}
