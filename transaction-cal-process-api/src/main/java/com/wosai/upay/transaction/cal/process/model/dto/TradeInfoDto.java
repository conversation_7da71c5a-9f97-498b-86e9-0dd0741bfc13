package com.wosai.upay.transaction.cal.process.model.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.Data;
import lombok.ToString;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 账单交易信息
 *
 * <AUTHOR>
 * @date 2019-09-16 20:14
 */
@ToString
@Data
@Accessors(chain = true)
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TradeInfoDto implements Serializable {
    private static final long serialVersionUID = 1665916588681673083L;

    // ----------------------- 上游账单交易信息 -----------------------
    /**
     * 账单有效支付汇总笔数
     */
    private Long sourceValidPaymentNumTotal;

    /**
     * 账单有效支付退款汇总笔数
     */
    private Long sourceValidRefundNumTotal;

    /**
     * 账单有效交易汇总笔数 = 账单有效支付汇总笔数 - 账单有效支付退款汇总笔数
     */
    private Long sourceValidTradeNumTotal;

    /**
     * 账单有效支付汇总金额
     */
    private Long sourceValidPaymentAmountTotal;

    /**
     * 账单有效支付退款汇总金额
     */
    private Long sourceValidRefundAmountTotal;

    /**
     * 账单有效交易汇总金额 = 账单有效支付汇总金额 - 账单有效支付退款汇总金额
     */
    private Long sourceValidTradeAmountTotal;

    /**
     * 结算依据汇总金额
     */
    private Long sourceSettlementBasisTotal;

    /**
     * 账单结算总金额
     */
    private Long  sourceSettlementAmountTotal;

    // ----------------------- 该账期内对应收钱吧交易信息 -----------------------
    /**
     * 收钱吧有效支付汇总笔数
     */
    private Long sqbPaymentNumTotal;

    /**
     * 收钱吧有效支付退款汇总笔数
     */
    private Long sqbRefundNumTotal;

    /**
     * 收钱吧有效交易汇总笔数 = 收钱吧有效支付汇总笔数 - 收钱吧有效支付退款汇总笔数
     */
    private Long sqbTradeNumTotal;

    /**
     * 收钱吧有效支付汇总金额
     */
    private Long sqbPaymentAmountTotal;

    /**
     * 收钱吧有效支付退款汇总金额
     */
    private Long sqbRefundAmountTotal;

    /**
     * 收钱吧有效交易汇总金额 = 收钱吧有效支付汇总金额 - 收钱吧有效支付退款汇总金额
     */
    private Long sqbTradeAmountTotal;

    // ----------------------- 差额显示（差额大于0为上游多，反之为上游少） -----------------------

    /**
     * 上游账单与收钱吧统计笔数差
     */
    private Long validTradeNumTotalBalance;

    /**
     * 上游账单与收钱吧统计金额差
     */
    private Long validTradeAmountTotalBalance;

    /**
     * 上游账单文件记录数
     */
    private Long sourceBillRecordNumber;

    /**
     * 对账文件地址
     */
    private String reconciliationFile;
}
