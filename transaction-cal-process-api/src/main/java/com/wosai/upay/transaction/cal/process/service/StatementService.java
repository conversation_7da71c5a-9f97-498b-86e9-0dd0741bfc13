package com.wosai.upay.transaction.cal.process.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.common.bean.PageInfo;
import com.wosai.upay.transaction.cal.process.model.dto.*;
import com.wosai.web.api.ListResult;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;

/**
 * Created by hzq on 19/10/17.
 */
@JsonRpcService("rpc/statement")
@Validated
public interface StatementService {

    int addPlan(@Valid PlanAddReq req);

    int updatePlan(@Valid PlanUpdateReq req);

    int updatePlanStatus(Long id, Integer status);

    PlanQueryResult getPlanById(Long id);

    int delPlan(Long id);

    ListResult<PlanQueryResult> listPlans(PageInfo pageInfo, PlanListParam listParam);
}
