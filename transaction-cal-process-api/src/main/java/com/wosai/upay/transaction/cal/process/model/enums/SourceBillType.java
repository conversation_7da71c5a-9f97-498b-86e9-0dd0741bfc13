package com.wosai.upay.transaction.cal.process.model.enums;

/**
 * SourceBillType
 *
 * <AUTHOR>
 * @date 2019-09-11 11:59
 */
public interface SourceBillType {

    int TYPE_ALIPAY_DIRECT_TRADE = 1; // 支付直连交易返佣
    int TYPE_ALIPAY_KOUBEI = 2; // 支付宝口碑返佣
    int TYPE_ALIPAY_HUABEI_INSTALLMENT = 3; // 支付宝花呗分期返佣
    int TYPE_ALIPAY_DRAGONFLY = 4; // 支付宝蜻蜓返佣
    int TYPE_WEIXIN_DIRECT_TRADE = 5; // 微信直连A类
    int TYPE_WEIXIN_SCHOOL_CAFETERIA = 6; // 微信校园食堂间连返佣
    int TYPE_ALIPAY_SCHOOL = 7; // 支付宝校园
    int TYPE_WEIXIN_PUBLIC_HOSPITAL = 8; // 微信公立医院返佣
    int TYPE_ALIPAY_INDIRECT_TRADE = 11; // 支付宝间联
    int TYPE_WECHAT_OASIS_TRADE = 12; // 微信绿洲（间联账单）
    int TYPE_ALIPAY_BLUE_OCEAN_TRADE = 13; // 支付宝蓝海
    int TYPE_WEIXIN_SCHOOL_DIRECT_CAFETERIA = 14; // 微信校园食堂直连返佣
    int TYPE_ALIPAY_TUANCAN = 15; // 支付宝团餐
    int TYPE_ALIPAY_HUABEI_DIRECT = 16; //支付宝花呗直连
    int TYPE_ALIPAY_EDU = 17; //支付宝教育
    int TYPE_LKL = 18; //拉卡拉账单
    int TYPE_TL = 19; //通联账单
    int TYPE_WECHAT_INDIRECT_A = 20; //微信间连A类
    int TYPE_ALIPAY_NEW_BLUE_OCEAN_TRADE = 21; // 支付宝新蓝海
    int TYPE_ALIPAY_HBFQ_TX = 22; // 花呗分期间联商家贴息
    int TYPE_ALIPAY_HOSPITAL = 23; // 支付宝公立医院
    int TYPE_ALIPAY_K12 = 24; //支付宝k12账单

    int TYPE_ALIPAY_OFFLINE_MINI_PROGRAM_BASIC_PAYMENT = 25; //支付宝线下小程序奖励

    int TYPE_ALIPAY_SCHOOL_V2 = 26;//支付宝未来校园2.0

    int TYPE_ALIPAY_HUABEI_FQ_INDIRECT = 27;//支付宝间连花呗分期

    int TYPE_ALIPAY_HUABEI_FQ_DIRECT = 28;//支付宝直连花呗分期
    int TYPE_DEFAULT_BILL_TYPE = 99; //默认账单类型，无需匹配拆分

}
