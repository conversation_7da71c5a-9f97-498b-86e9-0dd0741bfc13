package com.wosai.upay.transaction.cal.process.service;

import com.googlecode.jsonrpc4j.JsonRpcService;
import com.wosai.upay.transaction.cal.process.model.response.ScreenData;
import org.springframework.validation.annotation.Validated;


/**
 * 新大楼智慧大屏数据服务
 * Created by hzq on 19/10/17.
 */
@JsonRpcService("rpc/digitalScreenService")
@Validated
public interface DigitalScreenService {

    ScreenData getScreenData(String day);

}
