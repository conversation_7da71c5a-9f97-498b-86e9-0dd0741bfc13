package com.wosai.upay.transaction.cal.process.model.dto;

import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import com.wosai.upay.transaction.cal.process.model.enums.SummaryBillStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BillOutputV2 extends BillOutput {
    private String policyId;

    private String tradeMonth;

    private SummaryBillStatus status;

    private FulfillmentPushStatus fulfillmentPushStatus;

    private String sourceMerchantSn;

    private String deviceNo;

    private String summaryBillSn;

    private String summaryBillName;

    private Boolean isSettlement;

    private String ncName;

    private String companyName;

    private String reason;

    private String source;

    private Long sourceCost;

    private Long channelCost;

    private String propertyId;

    private String propertyName;
}
