package com.wosai.upay.transaction.cal.process.model.request;

import com.wosai.upay.transaction.cal.process.model.enums.FulfillmentPushStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class SourceBillPageRequest {
    private Integer pageNo;
    private Integer pageSize;
    private SourceBillPageQuery query;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SourceBillPageQuery {
        private String summaryBillSn;
        private String summaryBillName;
        private String policyId;
        private String tradeStartMonth;
        private String tradeEndMonth;
        private String status;
        private FulfillmentPushStatus fulfillmentPushStatus;
        private String sqbMerchantSn;
        private String sourceMerchantId;
        private String deviceNo;
        private String serviceProviderId;
        private String sourceLevel2MerchantName;
        private String sourceLevel2MerchantId;
        private String propertyId;
        private String templateCode;
    }
}
