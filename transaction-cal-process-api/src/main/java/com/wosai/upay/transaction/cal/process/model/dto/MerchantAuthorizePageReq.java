package com.wosai.upay.transaction.cal.process.model.dto;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Accessors(chain = true)
public class MerchantAuthorizePageReq {

    @NotNull(message = "taskId is required")
    private Long taskId;

    private Integer status;

    @NotNull(message = "页码不能为空")
    @Min(value = 1, message = "页码不能小于 1")
    private Integer pageNum = 1;

    @NotNull(message = "页大小不能为空")
    private Integer pageSize = 10;
}
