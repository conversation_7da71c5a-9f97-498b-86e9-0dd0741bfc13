package com.wosai.upay.transaction.cal.process.model.response;

import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
public class BasePageResponse<t> {
    private List<t> records;
    private long total = 0L;
    private int size;
    private int current;

    public BasePageResponse() {

    }

    public BasePageResponse(List<t> records, long total, int size, int current) {
        this.records = records;
        this.total = total;
        this.size = size;
        this.current = current;
    }
}
