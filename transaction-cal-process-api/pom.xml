<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>transaction-cal-process</artifactId>
        <groupId>com.wosai.upay</groupId>
        <version>1.5.76</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>transaction-cal-process-api</artifactId>
    <packaging>jar</packaging>
    <version>1.5.76</version>

    <dependencies>
        <!--JsonRpc4j-->
        <dependency>
            <groupId>com.wosai.middleware</groupId>
            <artifactId>jsonrpc4j</artifactId>
            <version>1.5.3-20200420</version>
        </dependency>
        <!--context-->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context</artifactId>
        </dependency>
        <!--validator-->
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-validation</artifactId>
        </dependency>
        <dependency>
            <groupId>javax.validation</groupId>
            <artifactId>validation-api</artifactId>
            <version>2.0.1.Final</version>
        </dependency>
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <version>1.18.8</version>
            <scope>compile</scope>
        </dependency>
        <dependency>
            <groupId>com.wosai.pantheon</groupId>
            <artifactId>wosai-common-web-api</artifactId>
            <version>1.1.8</version>
        </dependency>
        <dependency>
            <groupId>com.wosai.upay</groupId>
            <artifactId>upay-common</artifactId>
            <version>1.0.2-RC1</version>
        </dependency>
    </dependencies>

</project>
